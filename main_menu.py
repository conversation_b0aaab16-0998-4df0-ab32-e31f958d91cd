
import os
import random
# import shutil
import sys
import urllib
import zipfile
from functools import partial
# import ttkbootstrap as tb
# from ttkbootstrap.constants import *
# from requests_html import HTMLSession
# from tqdm import tqdm
from tkinter import ttk, messagebox
import subprocess
from urllib.parse import quote
import tkinter as tk
import requests
from packaging import version
import psutil

import moni11
import func1
import func12
import func12bak

import func13
import func14
import func15
import func16
import func17
import func19

import func2
import func20
import func21
import func22
import func23
import func24
import func25
import func26
import func27
import func28
import func3
import func4
import func5
import func6
import func7
import func8
import func9
import func10
import func11

import func18
from os import path

import moni10
import moni11bak
import moni12
import moni2
import moni5
import moni9

# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

class FunctionMenuApp:
    def __init__(self, root, current_version):

        # 保存图标的本地路径
        local_icon_path = "chinapost.ico"
        if self.is_process_running("新一代神器.exe"):
            messagebox.showwarning("警告", "已有一个实例在运行，无法再次启动。")
            sys.exit()
        self.root = root
        self.current_version = current_version  # 存储 current_version
        # 在初始化时调用检测更新函数
        self.check_update()
        # 下载依赖文件
        #self.download_ylwj()
        self.username = None  # 用于存储登录的用户名
        #self.root.withdraw()  # 隐藏主窗口
        # 创建登录窗口
        #self.create_login_window()
        self.user_role_ids = ['1','2','3','4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22','23','24','25','26','27','28']
        self.setup_window(current_version)
        self.create_buttons()


        # 添加窗口关闭事件绑定
        self.root.protocol("WM_DELETE_WINDOW", self.quit)

    def create_menu_bar(self):
        menu_bar = tk.Menu(self.root)
        self.root.config(menu=menu_bar)

        # 功能菜单
        function_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="功能", menu=function_menu)
        function_menu.add_command(label="修改密码", command=self.change_password)
        #function_menu.add_command(label="更换UI样式", command=self.change_ui_style)

    def change_password(self):
        self.change_password_window = tk.Toplevel(self.root)
        self.change_password_window.title("修改密码")
        # 设置图标
        icon_path = 'chinapost.ico'
        self.change_password_window.iconbitmap(icon_path)
        tk.Label(self.change_password_window, text="旧密码:").grid(row=0, column=0, padx=10, pady=10)
        self.old_password_entry = tk.Entry(self.change_password_window, show="*")
        self.old_password_entry.grid(row=0, column=1, padx=10, pady=10)

        tk.Label(self.change_password_window, text="新密码:").grid(row=1, column=0, padx=10, pady=10)
        self.new_password_entry = tk.Entry(self.change_password_window, show="*")
        self.new_password_entry.grid(row=1, column=1, padx=10, pady=10)

        tk.Label(self.change_password_window, text="再次输入新密码:").grid(row=2, column=0, padx=10, pady=10)
        self.confirm_password_entry = tk.Entry(self.change_password_window, show="*")
        self.confirm_password_entry.grid(row=2, column=1, padx=10, pady=10)

        submit_button = tk.Button(self.change_password_window, text="提交", command=self.submit_new_password)
        submit_button.grid(row=3, column=0, columnspan=2, pady=10)

    def submit_new_password(self):
        old_password = self.old_password_entry.get()
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()

        if new_password != confirm_password:
            messagebox.showerror("错误", "两次新密码输入不一致")
            return

        response = self.update_password(old_password, new_password)
        role_data = response["data"][0]
        if response and "1 rows affected" in role_data:
            messagebox.showinfo("成功", "密码修改成功")
            self.change_password_window.destroy()
        else:
            messagebox.showerror("错误", "密码修改失败，请确认旧密码是否正确")

    def update_password(self, old_password, new_password):
        url = "http://10.194.69.22:8520/api/xyd/update_password"
        payload = {"user":self.username,"old_password": old_password, "new_password": new_password}
        try:
            response = requests.post(url, json=payload)
            return response.json()
        except Exception as e:
            messagebox.showerror("Error", f"Error occurred: {e}")
            return None

    def is_process_running(self,process_name):
        current_pid = psutil.Process().pid
        running_instances = sum(1 for process in psutil.process_iter(['pid','name']) if
                                process.info['name'] == process_name and process.info['pid'] != current_pid)
        return running_instances > 2

    # 从网页获取.ico文件并保存到本地
    def download_icon(self,url, save_path):
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            return False

    def create_login_window(self):
        self.login_window = tk.Toplevel(self.root)
        self.login_window.title("登录神器")
        # 设置图标
        icon_path = 'chinapost.ico'
        self.login_window.iconbitmap(icon_path)
        tk.Label(self.login_window, text="账号:").grid(row=0, column=0, padx=10, pady=10)
        self.username_entry = tk.Entry(self.login_window)
        self.username_entry.grid(row=0, column=1, padx=10, pady=10)

        tk.Label(self.login_window, text="密码:").grid(row=1, column=0, padx=10, pady=10)
        self.password_entry = tk.Entry(self.login_window, show="*")
        self.password_entry.grid(row=1, column=1, padx=10, pady=10)

        login_button = tk.Button(self.login_window, text="登录", command=self.check_credentials)
        login_button.grid(row=2, column=0, columnspan=2, pady=10)

    def check_credentials(self):
        username = self.username_entry.get()
        password = self.password_entry.get()
        current_version = self.current_version
        response = self.authenticate_user(username, password)

        if response and response["success"]:
            role_data = response["data"][0]
            self.username = username
            self.user_role_ids = role_data["ROLE_ID"].split(',')
            print(self.user_role_ids)
            self.login_window.destroy()
            self.root.deiconify()#显示主窗口
            self.setup_window(current_version)
            self.create_menu_bar()
            self.create_buttons()
        else:
            messagebox.showerror("登录失败", "账号或密码不正确")

    def authenticate_user(self, username, password):
        url = "http://10.194.69.22:8520/api/xyd/sqdl"
        payload = {"user": username, "password": password}
        try:
            response = requests.post(url, json=payload)
            print(response.json())
            return response.json()
        except Exception as e:
            messagebox.showerror("Error", f"Error occurred: {e}")
            return None

    def create_buttons(self):
        button_data = [
            ("批量补发短信", func1.open_func_window, "批量补发短信", "1"),
            #("查国内残缺邮件", func2.open_func_window, "查残缺邮件", "2"),
            ("查国内残缺邮件★", moni2.open_func_window, "查残缺邮件", "2"),
            ("交航扫描统计", func3.open_func_window, "交航扫描统计", "3"),
            ("批量处理截留邮件", func4.open_func_window, "批量处理截留邮件", "4"),
            #("邮件全程信息统计", func5.open_func_window, "邮件全程信息统计", "5"),
            ("邮件全程信息统计★", moni5.open_func_window, "邮件全程信息统计", "5"),
            ("路单查询取数", func6.open_func_window, "路单查询取数", "6"),
            ("进口信息统计💀", func7.open_func_window, "进口信息统计", "7"),
            ("导异常验单图片💀", func8.open_func_window, "导异常验单图片", "8"),
            #("导邮件轨迹", func9.open_func_window, "导邮件轨迹", "9"),
            ("导邮件轨迹★", moni9.open_func_window, "导邮件轨迹", "9"),
            #("导总包轨迹", func10.open_func_window, "导总包轨迹", "10"),
            ("导总包轨迹★", moni10.open_func_window, "导总包轨迹", "10"),
            #("导邮件收寄计费信息", func11.open_func_window, "导邮件收寄计费信息", "11"),
            ("导邮件收寄计费信息★", moni11.open_func_window, "导邮件收寄计费信息", "11"),

            ("导邮件回执", func12bak.open_func_window, "导邮件回执", "12"),
            #("导邮件最后回执★", moni12.open_func_window, "导邮件最后回执", "12"),
            ("导邮件扣仓数据", func13.open_func_window, "导邮件扣仓数据", "13"),
            ("导邮件全状态回执", func14.open_func_window, "导邮件全状态回执", "14"),
            ("导邮件补录信息", func15.open_func_window, "导邮件补录信息", "15"),
            ("导邮袋回执", func16.open_func_window, "导邮袋回执", "16"),
            ("导补录复核信息", func17.open_func_window, "导补录复核信息", "17"),
            ("导mis信息", func18.open_func_window, "导mis信息", "18"),
            ("批量处理截留总包", func19.open_func_window, "批量处理截留总包", "19"),
            ("封发生产查询统计", func20.open_func_window, "封发生产查询统计", "20"),
            ("导解车交接对象", func21.open_func_window, "导解车交接对象", "21"),
            ("导总包内件", func22.open_func_window, "导总包内件", "22"),
            #("机构内邮件差异报告", func23.open_func_window, "机构内邮件差异报告", "23"),
            ("车辆接发情况监控", func24.open_func_window, "车辆接发情况监控", "24"),
            ("包分机模拟入格", func25.open_func_window, "包分机模拟入格", "25"),
            ("开拆生产查询统计", func26.open_func_window, "开拆生产查询统计", "26"),
            ("导操作记录统计", func27.open_func_window, "导操作记录统计", "27"),
            ("海关税收回执查询", func28.open_func_window, "海关税收回执查询", "28"),
            #("测试★", api_analyzer.open_func_window, "导邮件收寄计费信息", "27"),


        ]

        num_cols = 5

        for i, (text, command, title, role_id) in enumerate(button_data, 1):
            numbered_text = f"{i}. {text}"
            if role_id in self.user_role_ids:
                button_command = partial(command, self.root, title)
            else:
                button_command = partial(self.no_permission, text)
            button = ttk.Button(self.root, text=numbered_text, command=button_command)
            button.grid(row=(i - 1) // num_cols, column=(i - 1) % num_cols, padx=10, pady=10, sticky='nsew')

        for i in range((len(button_data) - 1) // num_cols + 1):
            self.root.grid_rowconfigure(i, weight=1)
        for j in range(num_cols):
            self.root.grid_columnconfigure(j, weight=1)

    def no_permission(self, text):
        messagebox.showwarning("拒绝访问", f"你没有开通 {text} 的权限，请向管理员申请")

    def download_ylwj(self):
        current_dir = os.getcwd()
        print(current_dir)
        instantclient_dir = os.path.join(current_dir, 'instantclient_12_1')
        instantclient_zip_path = os.path.join(current_dir, 'instantclient_12_1.zip')
        icon_dir = os.path.join(current_dir, 'chinapost.ico')
        Tesseract_dir = os.path.join(current_dir, 'Tesseract-OCR')
        Tesseract_zip_path = os.path.join(current_dir, 'Tesseract-OCR.zip')

        if not os.path.exists(icon_dir):
            web_icon_url = 'http://************:42300/' + quote('chinapost.ico')
            response = requests.get(web_icon_url)
            if response.status_code == 200:
                with open(icon_dir, 'wb') as f:
                    f.write(response.content)

        def download_and_extract(url, zip_path, extract_path, window_title):
            root = tk.Tk()
            root.title(window_title)

            progress_bar = ttk.Progressbar(root, orient=tk.HORIZONTAL, length=300, mode='determinate')
            progress_bar.pack(pady=10)

            response = requests.get(url, stream=True)
            if response.status_code == 200:
                total = int(response.headers.get('content-length', 0))
                progress_bar["maximum"] = total
                downloaded = 0
                with open(zip_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=1024):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            progress_bar["value"] = downloaded
                            root.update_idletasks()
                root.destroy()
                if downloaded >= total:
                    messagebox.showinfo('Info', f'开始解压文件到 {extract_path}')
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_path)
                    messagebox.showinfo('Info', '文件解压成功')
                    os.remove(zip_path)
                else:
                    messagebox.showerror('Error', '下载未完成或文件损坏')
            else:
                root.destroy()
                messagebox.showerror('Error', 'Failed to download zip file')

        if not os.path.exists(instantclient_dir):
            os.makedirs(instantclient_dir)
            instantclient_zip_url = 'http://************:42300/instantclient_12_1.zip'
            download_and_extract(instantclient_zip_url, instantclient_zip_path, instantclient_dir,
                                 "Instantclient Download Progress")

        if not os.path.exists(Tesseract_dir):
            os.makedirs(Tesseract_dir)
            Tesseract_zip_url = 'http://************:42300/Tesseract-OCR.zip'
            download_and_extract(Tesseract_zip_url, Tesseract_zip_path, Tesseract_dir,
                                 "Tesseract OCR Download Progress")
            messagebox.showinfo('Info', '依赖安装完成')

    # 设置窗口图标
    def set_window_icon(self,icon_path):
        if os.path.exists(icon_path):

            self.root.iconbitmap(icon_path)
            self.root.mainloop()
        else:
            print("文件路径不存在")

    def get_resource_path(self,relative_path):
        """ 获取资源文件的路径 """
        base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
        return path.join(base_path, relative_path)



    def get_random_poetry(self):
        api_url = 'http://************:28520/one.json'

        headers = {
            "User-Agent": "Mozilla/5.0"
        }

        try:
            response = requests.get(api_url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                content = data['data']['content']
                title = data['data']['origin']['title']
                dynasty = data['data']['origin']['dynasty']
                author = data['data']['origin']['author']
                return content,title, dynasty, author

            else:
                print("请求古诗词API失败，请检查网络或API是否可用。")
                return None, None, None, None
        except Exception as e:
            print(f"发生错误: {e}")
            return None, None, None, None
    def get_builtin_poetry(self):

    # 内置100条古诗词数据
        builtin_poetry = [
            ("东风恶，欢情薄，一怀愁绪，几年离索。错！错！错！", "声声慢", "宋", "李清照"),
            ("风急天高猿啸哀，渚清沙白鸟飞回。无边落木萧萧下，不尽长江滚滚来。", "登高", "唐", "杜甫"),
            ("枯藤老树昏鸦，小桥流水人家。古道西风瘦马。夕阳西下，断肠人在天涯。", "天净沙·秋思", "元", "马致远"),
            ("金风玉露一相逢，便胜却人间无数。", "鹊桥仙", "宋", "秦观"),
            ("少年不识愁滋味，爱上层楼。爱上层楼，为赋新词强说愁。", "丑奴儿", "宋", "辛弃疾"),
            ("砌下落梅如雪乱，拂了一身还满。", "清平乐", "五代", "李煜"),
            ("十年生死两茫茫。不思量，自难忘。千里孤坟，无处话凄凉。", "江城子", "宋", "苏轼"),
            ("无言独上西楼，月如钩。寂寞梧桐深院锁清秋。", "相见欢", "五代", "李煜"),
            ("日长篱落无人过，惟有蜻蜓蛱蝶飞。", "闲居初夏午睡起", "宋", "杨万里"),
            ("独自莫凭栏，无限江山，别时容易见时难。", "浪淘沙", "唐", "刘禹锡"),
            ("山有木兮木有枝，心悦君兮君不知。", "越人歌", "先秦", "佚名"),
            ("自是人生长恨水长东。", "相见欢", "五代", "李煜"),
            ("江城如画里，山晚望晴空。两水夹明镜，双桥落彩虹。", "秋登兰山寄张五", "唐", "孟浩然"),
            ("东篱把酒黄昏后，有暗香盈袖。", "醉花阴", "宋", "李清照"),
            ("千呼万唤始出来，犹抱琵琶半遮面。", "琵琶行", "唐", "白居易"),
            ("云想衣裳花想容，春风拂槛露华浓。", "清平调", "唐", "李白"),
            ("若问闲情都几许？一川烟草，满城风絮，梅子黄时雨。", "青玉案", "宋", "贺铸"),
            ("清晨入古寺，初日照高林。曲径通幽处，禅房花木深。", "题破山寺后禅院", "唐", "常建"),
            ("江南春尽离肠断，蘭舟一去何时返？", "相见欢", "五代", "李煜"),
            ("莫道不消魂，帘卷西风，人比黄花瘦。", "醉花阴", "宋", "李清照"),
            ("身无彩凤双飞翼，心有灵犀一点通。", "无题", "唐", "李商隐"),
            ("人面不知何处去，桃花依旧笑春风。", "题都城南庄", "唐", "崔护"),
            ("我寄愁心与明月，随君直到夜郎西。", "闻王昌龄左迁龙标遥有此寄", "唐", "李白"),
            ("沧海月明珠有泪，蓝田日暖玉生烟。", "锦瑟", "唐", "李商隐"),
            ("只恐双溪蚱蜢舟，载不动许多愁。", "武陵春", "宋", "李清照"),
            ("此情无计可消除，才下眉头，却上心头。", "一剪梅", "宋", "李清照"),
            ("春心莫共花争发，一寸相思一寸灰。", "无题", "唐", "李商隐"),
            ("天涯地角有穷时，只有相思无尽处。", "玉楼春", "宋", "晏殊"),
            ("春蚕到死丝方尽，蜡炬成灰泪始干。", "无题", "唐", "李商隐"),
            ("忍把千金酬一笑？毕竟相思，不似相逢好。", "青玉案", "宋", "贺铸"),
            ("梦后楼台高锁，酒醒帘幕低垂。去年春恨却来时，落花人独立，微雨燕双飞。", "临江仙", "宋", "晏几道"),
            ("生当复来归，死当长相思。", "留别妻", "魏晋", "苏武"),
            ("青青子衿，悠悠我心。纵我不往，子宁不嗣音？", "子衿", "先秦", "诗经"),
            ("江州司马青衫湿。", "琵琶行", "唐", "白居易"),
            ("昨夜星辰昨夜风，画楼西畔桂堂东。", "无题", "唐", "李商隐"),
            ("衣带渐宽终不悔，为伊消得人憔悴。", "凤求凰", "唐", "柳永"),
            ("人生自是有情痴，此恨不关风与月。", "玉楼春", "宋", "欧阳修"),
            ("多情自古伤离别，更那堪冷落清秋节。", "雨霖铃", "宋", "柳永"),
            ("此去经年，应是良辰好景虚设。便纵有，千种风情，更与何人说。", "雨霖铃", "宋", "柳永"),
            ("此情可待成追忆，只是当时已惘然。", "锦瑟", "唐", "李商隐"),
            ("忍看朋辈成新鬼，怒向刀丛觅小诗。", "狱中题壁", "清", "谭嗣同"),
            ("白头搔更短，浑欲不胜簪。", "秋登宣城谢眺北楼", "唐", "李白"),
            ("人生若只如初见，何事秋风悲画扇。", "木兰花", "清", "纳兰性德"),
            ("死生契阔，与子成说。执子之手，与子偕老。", "击鼓", "先秦", "诗经"),
            ("长相思兮长相忆，短相思兮无穷极。", "长相思", "汉", "佚名"),
            ("结发为夫妻，恩爱两不疑。", "留别妻", "魏晋", "苏武"),
            ("君当作磐石，妾当作蒲苇。蒲苇韧如丝，磐石无转移。", "孔雀东南飞", "汉", "佚名"),
            ("东边日出西边雨，道是无晴却有晴。", "竹枝词", "唐", "刘禹锡"),
            ("去年元夜时，花市灯如昼。月上柳梢头，人约黄昏后。", "生查子", "宋", "欧阳修"),
            ("相见争如不见，有情何似无情。", "西江月", "宋", "朱淑真"),
            ("今日俸钱过十万，与君营奠复营斋。", "送沈子福之江东", "唐", "王昌龄"),
            ("梧桐更兼细雨，到黄昏、点点滴滴。这次第，怎一个愁字了得！", "声声慢", "宋", "李清照"),
            ("日日思君不见君，共饮长江水。", "卜算子", "宋", "李之仪"),
            ("千里黄云白日曛，北风吹雁雪纷纷。", "别董大", "唐", "高适"),
            ("平生不会相思，才会相思，便害相思。", "折桂令", "元", "徐再思"),
            ("梧桐树，三更雨，不道离情正苦。一叶叶，一声声，空阶滴到明。", "长相思", "清", "纳兰性德"),
            ("何当共剪西窗烛，却话巴山夜雨时。", "夜雨寄北", "唐", "李商隐"),
            ("问君能有几多愁？恰似一江春水向东流。", "虞美人", "南唐", "李煜"),
            ("风住尘香花已尽，日晚倦梳头。", "武陵春", "宋", "李清照"),
            ("从别后，忆相逢，几回魂梦与君同。", "鹧鸪天", "宋", "晏几道"),
            ("昔我往矣，杨柳依依；今我来思，雨雪霏霏。", "采薇", "先秦", "诗经"),
            ("青青子衿，悠悠我心。纵我不往，子宁不嗣音？", "子衿", "先秦", "诗经"),
            ("花自飘零水自流。一种相思，两处闲愁。", "一剪梅", "宋", "李清照"),
            ("小楼昨夜又东风，故国不堪回首月明中。", "虞美人", "南唐", "李煜"),
            ("红藕香残玉簟秋，轻解罗裳，独上兰舟。", "一剪梅", "宋", "李清照"),
            ("月上柳梢头，人约黄昏后。", "生查子", "宋", "欧阳修"),
            ("一片花飞减却春，风飘万点正愁人。", "春残", "宋", "李清照"),
            ("当时明月在，曾照彩云归。", "临江仙", "宋", "晏几道"),
            ("花褪残红青杏小。燕子飞时，绿水人家绕。", "蝶恋花", "宋", "苏轼"),
            ("归去来兮，田园将芜胡不归？", "归去来兮辞", "东晋", "陶渊明"),
            ("无可奈何花落去，似曾相识燕归来。", "浣溪沙", "宋", "晏殊"),
            ("桃花潭水深千尺，不及汪伦送我情。", "赠汪伦", "唐", "李白"),
            ("梧桐更兼细雨，到黄昏，点点滴滴。这次第，怎一个愁字了得。", "声声慢", "宋", "李清照"),
            ("昔人已乘黄鹤去，此地空余黄鹤楼。", "黄鹤楼", "唐", "崔颢"),
            ("青山遮不住，毕竟东流去。", "菩萨蛮", "宋", "辛弃疾"),
            ("相思树底说相思，思郎恨郎郎不知。", "台湾竹枝词", "清", "丘逢甲"),
            ("只缘感君一回顾，使我思君朝与暮。", "古相思曲", "汉", "佚名"),
            ("关关雎鸠，在河之洲。窈窕淑女，君子好逑。", "关雎", "先秦", "诗经"),
            ("此夜曲中闻折柳，何人不起故园情。", "春夜洛城闻笛", "唐", "李白"),
            ("长安一片月，万户捣衣声。", "子夜吴歌·秋歌", "唐", "李白"),
            ("桃之夭夭，灼灼其华。之子于归，宜其室家。", "桃夭", "先秦", "诗经"),
            ("花间一壶酒，独酌无相亲。举杯邀明月，对影成三人。", "月下独酌", "唐", "李白"),
            ("山有木兮木有枝，心悦君兮君不知。", "越人歌", "先秦", "佚名"),
            ("曾经沧海难为水，除却巫山不是云。", "离思", "唐", "元稹"),
            ("他朝若是同淋雪，此生也算共白头。", "无题", "现代", "佚名"),
        ]
        return random.choice(builtin_poetry)
    def setup_window(self,current_version):

        # 调用函数并设置窗口标题
        random_poetry,title, dynasty, author = self.get_random_poetry()
        if not random_poetry:
            random_poetry, title, dynasty, author = self.get_builtin_poetry()

        title_text = f"新一代神器 版本:{current_version}     Power by:LHX              {random_poetry} —— 【{dynasty}】· {author}《{title}》"
        # if random_poetry:
        #     title = f"新一代神器 版本:{current_version}     Power by:LHX              {random_poetry} —— 《{title}》{author}·{dynasty}  "
        # else:
        #     title = f"新一代神器 版本:{current_version}     Power by LHX"
        self.root.title(title_text)

        #self.root.title("新一代神器 版本:"+current_version+"     Power by LHX")
        self.root.overrideredirect(False)  # 允许用户移动窗口

        # 设置图标
        icon_path = self.get_resource_path('chinapost.ico')#'chinapost.ico'
        self.root.iconbitmap(icon_path)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        window_width = int(screen_width / 2)
        #window_height = int(screen_height / 2)
        window_height = int(screen_height / 16 * 9)
        window_geometry = f"{window_width}x{window_height}"
        self.root.geometry(window_geometry)

    # def create_buttons(self):
    #     button_data = [
    #         ("批量补发短信", func1.open_func_window, "批量补发短信"),  # 如果不需要额外参数，传递 None
    #         ("查国内残缺邮件", func2.open_func_window, "查残缺邮件"),
    #         ("交航扫描统计", func3.open_func_window, "交航扫描统计"),
    #         ("批量处理截留邮件", func4.open_func_window, "批量处理截留邮件"),
    #         ("邮件全程信息统计", func5.open_func_window, "邮件全程信息统计"),
    #         ("路单查询取数", func6.open_func_window, "路单查询取数"),
    #         ("进口信息统计", func7.open_func_window, "进口信息统计"),
    #         ("导异常验单图片", func8.open_func_window, "导异常验单图片"),
    #         ("导邮件轨迹", func9.open_func_window, "导邮件轨迹"),
    #         ("导总包轨迹", func10.open_func_window, "导总包轨迹"),
    #         ("导邮件收寄计费信息", func11.open_func_window, "导邮件收寄计费信息"),  # 传递 title 参数
    #         ("导邮件最后回执", func12.open_func_window, "导邮件最后回执"),
    #         ("导邮件扣仓数据", func13.open_func_window, "导邮件扣仓数据"),
    #         ("导邮件全状态回执", func14.open_func_window, "导邮件全状态回执"),
    #         ("导邮件补录信息", func15.open_func_window, "导邮件补录信息"),
    #         ("导邮袋回执", func16.open_func_window, "导邮袋回执"),
    #         ("导补录复核信息", func17.open_func_window, "导补录复核信息"),
    #         ("导mis信息", func18.open_func_window, "导mis信息"),
    #         ("批量处理截留总包", func19.open_func_window, "批量处理截留总包"),
    #         ("封发生产查询统计", func20.open_func_window, "封发生产查询统计"),
    #         ("导解车交接对象", func21.open_func_window, "导解车交接对象"),
    #         ("导总包内件", func22.open_func_window, "导总包内件"),
    #     ]
    #
    #     # 指定每行几列
    #     num_cols = 5
    #
    #     for i, (text, command,title) in enumerate(button_data, 1):  # 设置 i 的初始值为 1)
    #         # 添加序号到按钮文本
    #         numbered_text = f"{i}. {text}"
    #         # 使用 functools.partial 创建带参数的函数
    #         button_command = partial(command, self.root, title)
    #         button = ttk.Button(self.root, text=numbered_text, command=button_command)
    #         button.grid(row=(i - 1) // num_cols, column=(i - 1) % num_cols, padx=10, pady=10, sticky='nsew')
    #
    #     # 让行和列的权重不相等，调整按钮大小
    #     for i in range(len(button_data)):
    #         self.root.grid_rowconfigure(i // num_cols, weight=1)
    #         self.root.grid_columnconfigure(i % num_cols, weight=1)


    # def open_function_window(self, command):
    #     self.root.withdraw()
    #     command(self.root)
    def change_ui_style(self):
        styles = ["Default", "Dark", "Light"]
        style_window = tk.Toplevel(self.root)
        style_window.title("Change UI Style")

        tk.Label(style_window, text="Select Style:").pack(padx=10, pady=10)
        style_var = tk.StringVar(value=styles[0])

        for style in styles:
            tk.Radiobutton(style_window, text=style, variable=style_var, value=style,
                           command=lambda: self.apply_ui_style(style_var.get())).pack(anchor="w", padx=10)

    def apply_ui_style(self, style):
        if style == "Dark":
            self.root.tk_setPalette(background='#2E2E2E', foreground='#FFFFFF')
            self.root.option_add('*TButton*background', '#4A4A4A')
            self.root.option_add('*TButton*foreground', '#FFFFFF')
            self.root.option_add('*TButton*borderWidth', 1)
            self.root.option_add('*TButton.padding', [6, 6])
        elif style == "Light":
            self.root.tk_setPalette(background='#FFFFFF', foreground='#000000')
            self.root.option_add('*TButton*background', '#E0E0E0')
            self.root.option_add('*TButton*foreground', '#000000')
            self.root.option_add('*TButton*borderWidth', 1)
            self.root.option_add('*TButton.padding', [6, 6])
        else:  # Default
            self.root.tk_setPalette(background='#F0F0F0', foreground='#000000')
            self.root.option_add('*TButton*background', '#D9D9D9')
            self.root.option_add('*TButton*foreground', '#000000')
            self.root.option_add('*TButton*borderWidth', 1)
            self.root.option_add('*TButton.padding', [6, 6])
    def check_update(self):
        try:
            # 获取最新版本号和更新内容
            response = urllib.request.urlopen('http://************:42300/version.txt')
            version_info = response.readline().decode('utf-8').lstrip('\ufeff').split()

            latest_version = version_info[0]
            update_content = ' '.join(version_info[1:]) if len(version_info) > 1 else '修复部分问题'

            # 获取当前版本号
            current_version = self.current_version  # 使用实例变量
            print(f'当前版本:{current_version}服务器版本:{latest_version}')
            # 检查是否需要更新
            if version.parse(latest_version) > version.parse(current_version):
                # 构建更新提示文本
                update_message = f"有新版本可用 ({latest_version})！！！\n\n更新内容:\n{update_content}"

                # 弹出对话框询问用户是否更新（设置标题为中文）
                answer = messagebox.askquestion("更新提示", update_message)

                if answer == 'yes':
                    # 提升权限
                    elevate()
                    subprocess.Popen(['updater.exe'])
                    self.quit()
                else:
                    self.quit()

        except urllib.error.URLError as e:
            messagebox.showerror("错误", f"检查更新时发生错误: {e.reason}")
            self.quit()
        except Exception as e:
            messagebox.showerror("未知错误", f"发生了一个未知错误: {e}")
            self.quit()

    def quit(self):
        # 获取当前 Python 进程 ID
        this_pid = os.getpid()

        # 关闭当前进程
        os.kill(this_pid, 9)
        # 销毁窗口以结束程序
        self.root.destroy()