<?xml version="1.0"?>

<!--  $Header: rdbms/demo/oraaccess.xml /main/3 2013/10/03 23:38:48 kkverma Exp $ -->

<!-- 
 Copyright (c) 2013, Oracle and/or its affiliates. All rights reserved.

   NAME
     oraaccess.xml 

   DESCRIPTION
     Here is a sample oraaccess.xml.  This shows defaulting of global and
     connection parameters across all connections. 

     The tags can also be prefixed with "oci:". For example <oci:prefetch>

   NOTES
     Configured values are for illustration purposes only and not necessarily
     recommended 

   MODIFIED   (MM/DD/YY)
   kkverma     09/16/13 - Creation

-->
 <oraaccess xmlns="http://xmlns.oracle.com/oci/oraaccess"
  xmlns:oci="http://xmlns.oracle.com/oci/oraaccess"
  schemaLocation="http://xmlns.oracle.com/oci/oraaccess
  http://xmlns.oracle.com/oci/oraaccess.xsd">
  <default_parameters>
    <prefetch>
      <rows>50</rows> 
    </prefetch>
    <statement_cache>
      <size>100</size> 
    </statement_cache>
    <result_cache>
      <max_rset_rows>100</max_rset_rows> 
      <!-- 'K' or 'k' for kilobytes and 'M' or 'm' for megabytes -->
      <max_rset_size>10K</max_rset_size> 
      <max_size>64M</max_size> 
    </result_cache>
  </default_parameters>
</oraaccess>

