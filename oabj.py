import asyncio
import tkinter as tk
from tkinter import ttk, messagebox
from tkcalendar import DateEntry
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from datetime import datetime
import pandas as pd
import json
import os
import sys
from os import path
import concurrent.futures
from tkinter import font as tkfont
import threading
import platform
from typing import Optional

def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)

# ================== 常量 ==================
BASE_URL = "https://newoa.postoa.com.cn"
API_URL = BASE_URL + "/prod-api/system/index/getWaitDoList"
SUBMIT_URL = BASE_URL + "/worknoticeInfoController/zjbj.done"
ARCHIVES_SUBMIT_URL = BASE_URL + "/archivesInController/killWorkflow.done"
CONFIG_FILE = 'config.json'

# 默认配置
DEFAULT_CONFIG = {
    "orgs": {
        "手动选择": "",  # 添加空选项
        "生产管控2": "020088003",
        "邮件作业中心": "020088004",

    },
    'thread_count': 5,
    'idea': '已阅',
    'password': '',
    'org_name': '',
    'org_code': '',
    'target_date': ''
}

LOGIN_DATA = {}
daiban_records = []

# ================== 配置读取与保存 ==================
def load_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                try:
                    config = json.load(f)
                    print("=== 加载的配置文件内容 ===")
                    print(f"配置文件路径: {os.path.abspath(CONFIG_FILE)}")
                    print(f"配置内容: {config}")

                    # 确保所有必需的键都存在
                    for key in DEFAULT_CONFIG:
                        if key not in config:
                            print(f"配置中缺少键: {key}，使用默认值: {DEFAULT_CONFIG[key]}")
                            config[key] = DEFAULT_CONFIG[key]

                    # 特殊处理orgs字典，确保包含所有默认选项
                    if 'orgs' in config:
                        for org_name, org_code in DEFAULT_CONFIG['orgs'].items():
                            if org_name not in config['orgs']:
                                config['orgs'][org_name] = org_code

                    return config
                except json.JSONDecodeError:
                    print("配置文件格式错误，使用默认配置")
                    return DEFAULT_CONFIG
        print(f"配置文件不存在: {CONFIG_FILE}")
        return DEFAULT_CONFIG
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return DEFAULT_CONFIG

def save_config(new_config):
    try:
        # 先加载现有配置
        current_config = load_config()

        # 更新配置，保持现有的orgs不变
        config = {
            "orgs": current_config["orgs"],  # 保持现有的机构列表
            "thread_count": int(new_config.get('thread_count', DEFAULT_CONFIG['thread_count'])),
            "idea": str(new_config.get('idea', DEFAULT_CONFIG['idea'])),
            "password": str(new_config.get('password', '')),
            "org_name": str(new_config.get('org_name', '')),
            "org_code": str(new_config.get('org_code', '')),
            "target_date": str(new_config.get('target_date', ''))
        }

        # 如果有新的机构需要添加（在登录过程中手动选择的）
        if 'new_org_name' in new_config and 'new_org_code' in new_config:
            config['orgs'][new_config['new_org_name']] = new_config['new_org_code']

        # 格式化写入，确保文件格式正确
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

        print(f"配置已保存到: {os.path.abspath(CONFIG_FILE)}")
        print(f"保存的配置内容: {config}")

    except Exception as e:
        print(f"保存配置文件失败: {e}")

# ================== 登录界面 ==================
class LoginDialog:
    def __init__(self, saved_config):
        # print("=== LoginDialog初始化 ===")
        # print(f"接收到的配置: {saved_config}")

        self.root = tk.Tk()
        self.root.title("OA办结神器")
        self.root.geometry("450x700")  # 增加窗口高度从650到700
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f2f5')

        # 设置窗口左上角图标
        try:
            # 使用get_resource_path函数获取图标文件路径
            icon_path = get_resource_path("chinapost.ico")
            self.root.iconbitmap(icon_path)
            print(f"成功设置窗口图标: {icon_path}")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

        # 设置样式
        style = ttk.Style()
        style.configure('TLabel', font=('Microsoft YaHei UI', 11), background='#f0f2f5')
        style.configure('TButton', font=('Microsoft YaHei UI', 11))
        style.configure('TEntry', font=('Microsoft YaHei UI', 11))
        style.configure('TFrame', background='#f0f2f5')

        main_frame = ttk.Frame(self.root, padding="20", style='TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Logo和标题区域
        title_frame = ttk.Frame(main_frame, style='TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk.Label(title_frame,
                               text="OA办结神器",
                               font=('Microsoft YaHei UI', 20, 'bold'),
                               foreground='#1890ff',
                               background='#f0f2f5')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame,
                                 text="自动化办理OA待办事项",
                                 font=('Microsoft YaHei UI', 11),
                                 foreground='#666666',
                                 background='#f0f2f5')
        subtitle_label.pack(pady=(5, 0))

        # 创建表单框架
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 15))

        WIDGET_WIDTH = 18

        # 第一行：机构选择
        ttk.Label(form_frame, text="选择机构", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=0, column=0, sticky='w', pady=(0, 5))
        ttk.Label(form_frame, text="线程数", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=0, column=1, sticky='w', pady=(0, 5))

        # 机构下拉框
        self.org_var = tk.StringVar()
        if saved_config.get('org_name'):
            self.org_var.set(saved_config['org_name'])

        self.org_dropdown = ttk.Combobox(
            form_frame,
            textvariable=self.org_var,
            values=list(saved_config['orgs'].keys()),
            state='readonly',
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        self.org_dropdown.grid(row=1, column=0, padx=(0, 10), pady=(0, 10))

        # 线程数下拉框
        self.thread_var = tk.StringVar()
        if saved_config.get('thread_count'):
            self.thread_var.set(str(saved_config['thread_count']))

        thread_dropdown = ttk.Combobox(
            form_frame,
            textvariable=self.thread_var,
            values=["5", "10", "15", "20"],
            state='readonly',
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        thread_dropdown.grid(row=1, column=1, pady=(0, 10))

        # 第二行：密码和日期
        ttk.Label(form_frame, text="密码", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=2, column=0, sticky='w', pady=(0, 5))
        ttk.Label(form_frame, text="日期", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=2, column=1, sticky='w', pady=(0, 5))

        # 密码输入框
        self.password_entry = ttk.Entry(
            form_frame,
            show="*",
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('password'):
            self.password_entry.insert(0, saved_config['password'])
        self.password_entry.grid(row=3, column=0, padx=(0, 10), pady=(0, 10))

        # 日期选择框
        self.date_entry = DateEntry(
            form_frame,
            width=WIDGET_WIDTH,
            background='#1890ff',
            foreground='white',
            borderwidth=2,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('target_date'):
            try:
                date_obj = datetime.strptime(saved_config['target_date'], '%Y-%m-%d')
                self.date_entry.set_date(date_obj)
            except ValueError:
                pass
        self.date_entry.grid(row=3, column=1, pady=(0, 10))

        # 第三行：办结意见
        ttk.Label(form_frame, text="办结意见", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=4, column=0, sticky='w', pady=(0, 5))

        # 办结意见输入框
        self.idea_entry = ttk.Entry(
            form_frame,
            width=WIDGET_WIDTH * 2 + 1,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('idea'):
            self.idea_entry.insert(0, saved_config['idea'])
        self.idea_entry.grid(row=5, column=0, columnspan=2, pady=(0, 10))

        # 开始处理按钮
        button_frame = tk.Frame(main_frame, bg='#f0f2f5')
        button_frame.pack(pady=(0, 15))

        submit_btn = tk.Button(
            button_frame,
            text="开始处理",
            command=self.submit,
            font=('Microsoft YaHei UI', 11, 'bold'),
            fg='white',
            bg='#1890ff',
            activebackground='#096dd9',
            activeforeground='white',
            relief='flat',
            padx=25,
            pady=8,
            cursor='hand2'
        )

        def on_enter(e):
            submit_btn['bg'] = '#40a9ff'
        def on_leave(e):
            submit_btn['bg'] = '#1890ff'

        submit_btn.bind("<Enter>", on_enter)
        submit_btn.bind("<Leave>", on_leave)
        submit_btn.pack()

        # 进度显示区域
        self.progress_frame = ttk.Frame(main_frame, style='TFrame')
        self.progress_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建水平框架来容纳文本框和滚动条
        text_container = ttk.Frame(self.progress_frame)
        text_container.pack(fill=tk.BOTH, expand=True)

        # 先创建垂直滚动条
        scrollbar_v = ttk.Scrollbar(text_container, orient="vertical")
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建水平滚动条
        scrollbar_h = ttk.Scrollbar(text_container, orient="horizontal")
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 创建文本框
        self.progress_text = tk.Text(
            text_container,
            height=12,
            font=('Consolas', 9),  # 使用等宽字体，更适合显示代码和日志
            wrap=tk.NONE,  # 不自动换行，这样水平滚动条才有用
            background='#ffffff',
            foreground='#333333',
            yscrollcommand=scrollbar_v.set,
            xscrollcommand=scrollbar_h.set
        )
        self.progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 配置滚动条命令
        scrollbar_v.configure(command=self.progress_text.yview)
        scrollbar_h.configure(command=self.progress_text.xview)

        # 配置标签样式
        self.progress_text.tag_configure('success', foreground='#52c41a')
        self.progress_text.tag_configure('error', foreground='#ff4d4f')
        self.progress_text.tag_configure('info', foreground='#1890ff')

        # 初始时禁用文本框编辑
        self.progress_text.configure(state='disabled')

        # 版权信息 - 重新调整位置
        copyright_frame = ttk.Frame(self.root, style='TFrame')  # 改为self.root而不是main_frame
        copyright_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(0, 10))

        copyright_label = ttk.Label(
            copyright_frame,
            text="© 2025 OA办结神器 by 李浩贤",
            font=('Microsoft YaHei UI', 9),
            foreground='#999999',
            background='#f0f2f5',
            anchor='center'
        )
        copyright_label.pack(fill=tk.X)

        self.loop = None
        self.running_task = None

    def update_progress(self, message, message_type='normal'):
        """更新进度信息，支持不同类型的消息样式"""
        self.progress_text.configure(state='normal')
        if '✅' in message:
            self.progress_text.insert(tk.END, message + '\n', 'success')
        elif '❌' in message:
            self.progress_text.insert(tk.END, message + '\n', 'error')
        else:
            self.progress_text.insert(tk.END, message + '\n', 'info')
        self.progress_text.see(tk.END)
        self.progress_text.configure(state='disabled')
        self.root.update()

    def submit(self):
        global LOGIN_DATA
        selected_org_name = self.org_var.get()
        config = load_config()

        # 获取日期并确保格式正确
        date_str = self.date_entry.get_date().strftime('%Y-%m-%d')

        LOGIN_DATA = {
            'password': self.password_entry.get(),
            'org_name': selected_org_name,
            'org_code': config['orgs'][selected_org_name],  # 获取机构代码
            'target_date': date_str,
            'thread_count': int(self.thread_var.get()),
            'idea': self.idea_entry.get()
        }

        # 保存配置时同时保存机构名称和代码
        save_config({
            'password': LOGIN_DATA['password'],
            'org_name': LOGIN_DATA['org_name'],
            'org_code': LOGIN_DATA['org_code'],  # 保存机构代码
            'target_date': LOGIN_DATA['target_date'],  # 保存字符串格式的日期
            'thread_count': LOGIN_DATA['thread_count'],
            'idea': LOGIN_DATA['idea']
        })

        self.update_progress("\n=== 开始处理 ===")

        # 创建新线程运行异步任务
        threading.Thread(target=self.run_async_task, daemon=True).start()

    def run_async_task(self):
        """在新线程中运行异步任务"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.running_task = self.loop.create_task(self.process_tasks())
        self.loop.run_until_complete(self.running_task)

    async def process_tasks(self):
        """处理所有任务的异步函数"""
        try:
            async with async_playwright() as p:
                print("\n=== 检查Chrome浏览器 ===")
                # 检查本地Chrome
                chrome_path = find_chrome_executable()
                if chrome_path:
                    print(f"✓ 使用找到的Chrome浏览器: {chrome_path}")
                    # 使用本地Chrome
                    browser = await p.chromium.launch(
                        executable_path=chrome_path,
                        headless=False
                    )
                else:
                    current_dir_chrome = os.path.join(os.getcwd(), "chrome", "chrome.exe")
                    # 使用Playwright内置浏览器
                    browser = await p.chromium.launch(executable_path=current_dir_chrome, headless=False)
                #browser = await p.chromium.launch(headless=False)
                context = await browser.new_context()

                self.update_progress("正在登录...")
                page, login_success = await get_cookies_after_login(context)

                if not login_success:
                    self.update_progress("❌ 登录失败：密码错误，请重新运行程序输入密码！")
                    return
                else:
                    self.update_progress("✅ 登录成功")

                self.update_progress("\n开始获取代办列表...")
                all_data = await get_all_daiban_list_data(page)

                # 先分析API数据中的代办类型
                self.update_progress("\n🔍 开始分析API数据中的代办类型...")
                try:
                    analyze_daiban_types_from_api_data(all_data)
                except Exception as e:
                    self.update_progress(f"❌ 分析代办类型时出错: {e}")
                    import traceback
                    traceback.print_exc()

                daiban_list = parse_all_daiban(all_data, LOGIN_DATA['target_date'])

                if not daiban_list:
                    self.update_progress("\n没有需要处理的代办事项")
                else:
                    # 打印代办摘要
                    print_daiban_summary(daiban_list)

                    for idx, daiban in enumerate(daiban_list, 1):
                        self.update_progress(f"\n✅ 正在提交第 {idx}/{len(daiban_list)} 条：{daiban['标题']}")
                        result, success = await fetch_and_submit(page, daiban)
                        daiban['提交结果'] = "成功" if success else "失败"
                        status = '✅ 成功' if success else '❌ 失败'
                        self.update_progress(f"提交结果: {status}")
                        if not success:
                            self.update_progress(f"错误信息: {result}")
                        daiban_records.append(daiban)

                    if daiban_records:
                        export_to_excel(daiban_records)
                        self.update_progress("\n✅ Excel文件已导出")

                await browser.close()
                self.update_progress("\n=== 处理完成 ===")

        except Exception as e:
            self.update_progress(f"\n❌ 处理过程出错: {str(e)}")
            import traceback
            self.update_progress(f"\n{traceback.format_exc()}")

# ================== 获取 cookies ==================
async def get_cookies_after_login(context):
    print("\n=== 开始登录流程 ===")
    page = await context.new_page()
    await page.goto(BASE_URL + "/#/login")
    print("1. 已访问登录页面")

    await page.wait_for_selector('input[type="password"].el-input__inner')
    print("2. 已找到密码输入框")

    # 先点击密码输入框激活它，然后移除readonly属性
    password_input = page.locator('input[type="password"].el-input__inner')
    await password_input.click()
    await page.evaluate('document.querySelector(\'input[type="password"].el-input__inner\').removeAttribute("readonly")')
    await password_input.fill(LOGIN_DATA['password'])

    await page.click('button.el-button.el-button--primary.el-button--medium')
    print("3. 已输入密码并点击登录按钮")

    try:
        print("4. 等待登录结果...")
        # 等待一小段时间让页面加载
        await page.wait_for_timeout(500)

        # 首先检查是否有机构选择框
        has_org_selector = await page.locator('.many-box').count() > 0

        if has_org_selector:
            # 需要选择机构的情况
            print("5. 检测到需要选择机构")

            if LOGIN_DATA['org_name'] and LOGIN_DATA['org_name'] != "手动选择":  # 如果有预设的机构名称
                # 查找包含指定机构名称的选项
                try:
                    # 等待机构选项加载
                    await page.wait_for_selector('.many_info', timeout=5000)

                    # 查找匹配的机构选项
                    org_elements = await page.locator('.many_info').all()
                    found_org = False

                    for org_element in org_elements:
                        org_text = await org_element.inner_text()
                        if LOGIN_DATA['org_name'] in org_text:
                            await org_element.click()
                            print(f"6. 已找到并点击目标机构：{LOGIN_DATA['org_name']}")
                            found_org = True
                            break

                    if not found_org:
                        print("预设机构未找到，等待手动选择")
                        # 等待用户手动选择
                        await page.wait_for_selector('.many_info.active', timeout=60000)

                except Exception as e:
                    print(f"查找机构时出错：{e}，等待手动选择")
                    await page.wait_for_selector('.many_info.active', timeout=60000)

            else:  # 手动选择模式
                print("等待手动选择机构...")
                # 等待用户点击某个机构
                await page.wait_for_selector('.many_info.active', timeout=60000)

            # 获取选中的机构信息并保存
            try:
                selected_org_element = page.locator('.many_info.active')
                selected_org_text = await selected_org_element.inner_text()
                print(f"检测到选择的机构：{selected_org_text}")

                # 如果是手动选择，更新配置
                if not LOGIN_DATA['org_name'] or LOGIN_DATA['org_name'] == "手动选择":
                    save_config({
                        'new_org_name': selected_org_text,
                        'new_org_code': '',  # 新结构中没有机构代码
                        'password': LOGIN_DATA['password'],
                        'org_name': selected_org_text,
                        'org_code': '',
                        'target_date': LOGIN_DATA['target_date'],
                        'thread_count': LOGIN_DATA['thread_count'],
                        'idea': LOGIN_DATA['idea']
                    })
                    print("新机构已添加到配置文件")
            except:
                print("获取选中机构信息失败")

            # 等待并点击登录按钮
            await page.wait_for_selector('button.el-button.el-button--primary.el-button--medium', state='visible')
            await page.wait_for_timeout(500)
            print("8. 已找到登录按钮")

            # 点击登录按钮
            await page.click('button.el-button.el-button--primary.el-button--medium')
            print("9. 已点击登录按钮")

            # 等待登录完成
            await page.wait_for_timeout(3000)
            print("10. 等待3秒完成")
        else:
            # 直接登录成功的情况
            print("5. 检测到直接登录模式")
            await page.wait_for_timeout(2000)
            print("6. 等待页面加载完成")

        # 验证登录状态
        content = await page.content()
        if "首页" in content:
            print("✓ 登录成功：已验证页面内容")
            return page, True
        else:
            print("✗ 登录失败：未找到首页标识")
            return page, False

    except Exception as e:
        print(f"登录过程出错: {str(e)}")
        # 最后检查一次是否实际已登录
        try:
            content = await page.content()
            if "首页" in content:
                print("✓ 检测到已成功登录")
                return page, True
            if "密码错误" in content or "请重新登录" in content:
                print("✗ 登录失败：密码错误或需要重新登录")
            else:
                print("✗ 登录失败：未知原因")
        except:
            pass
        print("=== 登录流程异常结束 ===\n")
        return page, False

# 在主登录函数中添加重试逻辑
async def simulate_login(username, password, max_retries=3):
    LOGIN_DATA['password'] = password

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()

        for attempt in range(max_retries):
            print(f"\n=== 开始第 {attempt + 1} 次登录尝试 ===")
            page, login_success = await get_cookies_after_login(context)

            if login_success:
                print("✓ 登录成功！")
                print("=== 登录流程完成 ===\n")
                return
            else:
                print(f"✗ 登录尝试 {attempt + 1} 失败，{'正在重试...' if attempt < max_retries - 1 else '已达到最大重试次数'}")
                if attempt < max_retries - 1:
                    await page.wait_for_timeout(2000)  # 等待2秒后重试
                    continue

        messagebox.showerror("登录失败", "密码错误或登录异常，请检查后重试！")

# ================== 网络请求监听 ==================
async def setup_network_monitoring(context):
    """设置网络请求监听，包括新窗口"""

    # 创建日志文件
    log_filename = f"network_requests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    def log_request(message):
        """记录请求到文件和控制台"""
        print(message)
        with open(log_filename, 'a', encoding='utf-8') as f:
            f.write(message + '\n')

    log_request(f"\n{'='*80}")
    log_request(f"网络请求监听开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    log_request(f"{'='*80}")

    # 监听请求
    async def handle_request(request):
        url = request.url
        method = request.method
        headers = request.headers

        # 只记录API请求，过滤静态资源
        if any(ext in url.lower() for ext in ['.js', '.css', '.png', '.jpg', '.ico', '.woff', '.svg', '.ttf']):
            return

        log_request(f"\n🌐 请求: {method} {url}")

        # 记录请求头（只记录重要的）
        important_headers = ['authorization', 'content-type', 'cookie']
        for header_name in important_headers:
            if header_name in headers:
                if header_name == 'cookie':
                    # Cookie太长，只记录关键部分
                    cookie_value = headers[header_name]
                    if 'Admin-Token=' in cookie_value:
                        import re
                        token_match = re.search(r'Admin-Token=([^;]+)', cookie_value)
                        if token_match:
                            log_request(f"   🔑 Admin-Token: {token_match.group(1)}")
                else:
                    log_request(f"   📋 {header_name}: {headers[header_name]}")

        # 记录POST请求的数据
        if method == 'POST':
            try:
                post_data = request.post_data
                if post_data:
                    log_request(f"   📤 请求数据: {post_data}")
            except:
                log_request(f"   📤 请求数据: [无法获取]")

    # 监听响应
    async def handle_response(response):
        url = response.url
        status = response.status

        # 只记录API响应，过滤静态资源
        if any(ext in url.lower() for ext in ['.js', '.css', '.png', '.jpg', '.ico', '.woff', '.svg', '.ttf']):
            return

        log_request(f"   📥 响应: {status}")

        # 记录响应内容（只记录JSON响应）
        try:
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                response_text = await response.text()
                # 限制响应长度，避免日志过大
                if len(response_text) > 2000:
                    response_text = response_text[:2000] + "...[截断]"
                log_request(f"   📄 响应内容: {response_text}")
        except Exception as e:
            log_request(f"   📄 响应内容: [获取失败: {e}]")

    # 为现有页面设置监听
    for page in context.pages:
        page.on("request", handle_request)
        page.on("response", handle_response)
        log_request(f"✅ 已为现有页面设置监听: {page.url}")

    # 监听新页面的创建
    async def handle_new_page(page):
        log_request(f"\n🆕 检测到新页面: {page.url}")
        page.on("request", handle_request)
        page.on("response", handle_response)
        log_request(f"✅ 已为新页面设置监听")

    context.on("page", handle_new_page)

    log_request(f"\n✅ 网络监听已启动，日志文件: {log_filename}")
    log_request(f"🎯 请开始手动操作，所有网络请求将被记录...")
    log_request(f"📱 包括新打开的窗口和标签页")

# ================== 获取所有代办列表 ==================
# 全局变量存储token，避免重复获取
_cached_token = None

async def get_admin_token(page):
    """获取Admin-Token，只获取一次"""
    global _cached_token
    if _cached_token is None:
        _cached_token = await page.evaluate("""
            () => {
                // 从cookie中提取Admin-Token
                const cookies = document.cookie;
                const adminTokenMatch = cookies.match(/Admin-Token=([^;]+)/);
                const adminToken = adminTokenMatch ? adminTokenMatch[1] : null;

                // 也尝试从decryptShow获取（看起来也是token）
                const decryptShowMatch = cookies.match(/decryptShow=([^;]+)/);
                const decryptShow = decryptShowMatch ? decryptShowMatch[1] : null;

                return {
                    admin_token: adminToken,
                    decrypt_show: decryptShow,
                    cookies: cookies
                };
            }
        """)
        print(f"🔐 首次获取Token信息: {_cached_token}")
    return _cached_token

async def get_daiban_list_json(page, page_index):
    """获取指定页的代办列表JSON数据"""
    post_data = {
        "sort": "",
        "hj": "",
        "indexFlag": "1",
        "pageIndex": page_index,
        "pageSize": 10
    }

    # 获取缓存的token
    token_info = await get_admin_token(page)

    return await page.evaluate("""
        async ([url, data, tokenInfo]) => {
            // 构建请求头
            const headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            // 使用Admin-Token作为Authorization Bearer token
            if (tokenInfo.admin_token) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.admin_token;
            } else if (tokenInfo.decrypt_show) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.decrypt_show;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(data),
                credentials: 'include'
            });

            const result = await response.json();
            return result;
        }
    """, [API_URL, post_data, token_info])

async def get_total_pages(page):
    """获取总页数"""
    first_page_data = await get_daiban_list_json(page, 1)

    # 调试：打印返回的数据结构
    print(f"🔍 API返回数据: {first_page_data}")

    # 检查返回数据的结构
    if not first_page_data:
        print("❌ API返回空数据")
        return 1

    if 'data' not in first_page_data:
        print(f"❌ API返回数据中没有'data'字段，实际字段: {list(first_page_data.keys())}")
        return 1

    data = first_page_data['data']
    if 'total' not in data:
        print(f"❌ data中没有'total'字段，实际字段: {list(data.keys())}")
        return 1

    total_records = data['total']
    page_size = data.get('size', 10)  # 默认10条
    total_pages = (total_records + page_size - 1) // page_size  # 向上取整
    print(f"📊 总记录数: {total_records}, 每页: {page_size}, 总页数: {total_pages}")
    return total_pages

async def get_page_data(page, page_index):
    print(f"📄 正在获取第 {page_index} 页")
    return await get_daiban_list_json(page, page_index)

async def get_all_daiban_list_data(page):
    """获取所有代办列表数据"""
    total_pages = await get_total_pages(page)

    # 创建所有任务
    tasks = []
    for page_index in range(1, total_pages + 1):
        task = asyncio.create_task(get_page_data(page, page_index))
        tasks.append(task)

    # 等待所有任务完成
    all_pages_data = await asyncio.gather(*tasks)

    return all_pages_data

# ================== 提取ID和获取办结参数 ==================
def extract_id_from_url(url):
    """从URL中提取id参数"""
    import re
    match = re.search(r'id=([^&]+)', url)
    return match.group(1) if match else None

async def get_banjie_params(page, flow_id):
    """根据flow_id获取办结参数"""
    post_data = {"id": flow_id}
    detail_api_url = BASE_URL + "/prod-api/worknotice/worknotice/getDbByFlowId"

    # 获取缓存的token
    token_info = await get_admin_token(page)

    return await page.evaluate("""
        async ([url, data, tokenInfo]) => {
            // 构建请求头
            const headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            // 使用Admin-Token作为Authorization Bearer token
            if (tokenInfo.admin_token) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.admin_token;
            } else if (tokenInfo.decrypt_show) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.decrypt_show;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(data),
                credentials: 'include'
            });

            const result = await response.json();
            return result;
        }
    """, [detail_api_url, post_data, token_info])

# ================== 解析代办 ==================
def parse_all_daiban(json_data_list, target_date):
    """解析所有代办数据"""
    # 如果传入的是datetime对象，转换为字符串
    if isinstance(target_date, datetime):
        target_date = target_date.strftime("%Y-%m-%d")

    # 确保日期格式正确（将可能的 / 替换为 -）
    target_date = target_date.replace('/', '-')

    try:
        target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError as e:
        print(f"日期格式错误: {target_date}")
        raise

    daiban_list = []

    for json_data in json_data_list:
        # 调试：打印每页的数据结构
        print(f"🔍 处理页面数据: {json_data}")

        if not json_data:
            print("⚠️ 跳过空数据页面")
            continue

        if json_data.get('code') == 200 and 'data' in json_data:
            records = json_data['data'].get('records', [])
            print(f"📄 当前页面有 {len(records)} 条记录")
            for record in records:
                title = record.get('title', '')
                date_str = record.get('createTime', '')
                url = record.get('url', '')

                if not title or not date_str or not url:
                    continue

                try:
                    # 解析日期格式 "2025-05-23 15:30"
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M")

                    # 修正时间判断逻辑：处理目标日期及之后的代办事项
                    if date_obj.date() <= target_date_obj.date():
                        full_url = BASE_URL + url

                        # 提取URL中的id参数
                        flow_id = extract_id_from_url(url)

                        daiban_list.append({
                            "标题": title,
                            "日期": date_str,
                            "链接": full_url,
                            "原始URL": url,
                            "流程ID": flow_id,
                            "记录ID": record.get('recordId', ''),
                            "文件类型": record.get('filetypename', ''),
                            "前置用户": record.get('preUserName', '')
                        })

                        # 打印代办信息
                        print(f"\n📋 代办事项:")
                        print(f"   标题: {title}")
                        print(f"   日期: {date_str}")
                        print(f"   流程ID: {flow_id}")
                        print(f"   文件类型: {record.get('filetypename', '')}")
                        print(f"   前置用户: {record.get('preUserName', '')}")
                        print(f"   链接: {full_url}")

                except ValueError as e:
                    print(f"日期解析错误: {date_str}, 错误: {e}")
                    continue

    return daiban_list

# ================== 批量办结流程 ==================
async def fetch_and_submit_new_worknotice(page, daiban):
    """新的工作联系单办结流程"""
    flow_id = daiban.get('流程ID')
    if not flow_id:
        return "未找到流程ID", False

    print(f"🔍 获取办结参数，流程ID: {flow_id}")

    # 获取办结参数
    banjie_data = await get_banjie_params(page, flow_id)

    if banjie_data.get('code') != 200:
        return f"获取办结参数失败: {banjie_data.get('msg', '未知错误')}", False

    data = banjie_data.get('data', {})
    data_id = data.get('id', '')  # 主记录的id

    if not data_id:
        return "未找到data.id", False

    print(f"📤 准备办结提交，flow_id: {flow_id}, data_id: {data_id}")

    # 构建办结提交URL（根据你发现的格式）
    submit_api_url = f"{BASE_URL}/prod-api/worknotice/worknotice/zjbj/{flow_id}/{data_id}"

    print(f"🌐 办结API地址: {submit_api_url}")

    # 获取缓存的token
    token_info = await get_admin_token(page)

    # 使用GET请求提交办结
    result = await page.evaluate("""
        async ([url, tokenInfo]) => {
            // 构建请求头
            const headers = {
                'Accept': 'application/json, text/plain, */*',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            // 使用Admin-Token作为Authorization Bearer token
            if (tokenInfo.admin_token) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.admin_token;
            } else if (tokenInfo.decrypt_show) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.decrypt_show;
            }

            const response = await fetch(url, {
                method: 'GET',
                headers: headers,
                credentials: 'include'
            });

            const result = await response.json();
            return result;
        }
    """, [submit_api_url, token_info])

    print(f"📥 办结响应: {result}")

    # 判断提交是否成功
    success = result.get('code') == 200
    return result, success

async def fetch_and_submit_archives(page, daiban):
    detail_html = await page.evaluate("""async (url) => {
        const response = await fetch(url, { method: 'GET', credentials: 'include' });
        return await response.text();
    }""", daiban['链接'])

    soup = BeautifulSoup(detail_html, "html.parser")
    form = soup.find("form", {"id": "frm"})
    if not form:
        return "表单未找到"

    # 收集所有hidden input的值
    post_data = {}
    for input_tag in form.find_all("input", type="hidden"):
        name = input_tag.get("name")
        value = input_tag.get("value", "")
        if name:
            post_data[name] = value

    # 提取特定字段的值
    special_fields = ['needGradeName', 'secretGradeName', 'saveLimitIdName']
    for field in special_fields:
        input_tag = form.find("input", {"name": field})
        if input_tag:
            post_data[field] = input_tag.get("value", "")

    # 添加必要的其他字段
    additional_fields = {
        'idea': LOGIN_DATA['idea']  # 使用用户输入的办结意见
    }
    post_data.update(additional_fields)

    # 提交办结
    result = await page.evaluate("""([url, data]) => {
        const formBody = new URLSearchParams(data);
        return fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: formBody,
            credentials: 'include'
        }).then(r => r.text());
    }""", [ARCHIVES_SUBMIT_URL, post_data])

    return result

async def get_old_oa_login_url(page, original_url):
    """获取旧版OA的登录URL"""
    # 确保页面在正确的域名下（newoa.postoa.com.cn）
    current_url = page.url
    if 'newoa.postoa.com.cn' not in current_url:
        print(f"🔄 当前页面不在newoa域名下 ({current_url})，需要先跳转回主页")
        try:
            await page.goto(BASE_URL, wait_until='networkidle')
            print(f"✅ 已跳转回主页: {BASE_URL}")
        except Exception as e:
            print(f"❌ 跳转回主页失败: {e}")
            return None, f"跳转回主页失败: {e}"

    # 从原始URL中提取相对路径部分
    # 例如：从 "/worknoticeInfoController/getDbByFlowId.done?listFlag=indexDb&id=25030505211649497928"
    # 提取整个路径
    import re
    url_match = re.search(r'(/worknoticeInfoController/[^"]*)', original_url)
    if not url_match:
        return None, "无法从URL中提取路径"

    relative_url = url_match.group(1)
    print(f"🔗 提取的相对URL: {relative_url}")

    # 构建loginOldOa请求
    login_old_oa_url = BASE_URL + "/prod-api/gongwencommon/gongwencommon/loginOldOa"
    post_data = {"url": relative_url}

    print(f"🌐 请求loginOldOa: {login_old_oa_url}")
    print(f"📤 请求参数: {post_data}")

    # 获取缓存的token
    token_info = await get_admin_token(page)

    # 发送请求获取实际的处理URL
    result = await page.evaluate("""
        async ([url, data, tokenInfo]) => {
            // 构建请求头
            const headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Accept': 'application/json, text/plain, */*',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            };

            // 使用Admin-Token作为Authorization Bearer token
            if (tokenInfo.admin_token) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.admin_token;
            } else if (tokenInfo.decrypt_show) {
                headers['Authorization'] = 'Bearer ' + tokenInfo.decrypt_show;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(data),
                credentials: 'include'
            });

            const result = await response.json();
            return result;
        }
    """, [login_old_oa_url, post_data, token_info])

    print(f"📥 loginOldOa响应: {result}")

    # 检查响应
    if result.get('code') == 200 and 'data' in result:
        actual_url = result['data']
        print(f"✅ 获取到实际处理URL: {actual_url}")
        return actual_url, None
    else:
        error_msg = f"获取旧版OA登录URL失败: {result.get('msg', '未知错误')}"
        print(f"❌ {error_msg}")
        return None, error_msg

# 修改原有的fetch_and_submit函数名称
async def fetch_and_submit_worknotice(page, daiban):
    """旧版工作联系单办结流程 - 支持新的两步流程"""
    original_url = daiban['链接']

    # 检查是否是需要通过loginOldOa的旧版工作联系单
    if 'worknoticeInfoController' in original_url:
        print(f"🔄 检测到旧版工作联系单，尝试通过loginOldOa获取实际URL")

        # 第一步：获取实际的处理URL
        actual_url, error = await get_old_oa_login_url(page, original_url)
        if error:
            return error, False

        if actual_url:
            print(f"🎯 使用实际URL进行处理: {actual_url}")
            # 使用实际URL替换原始URL
            processing_url = actual_url
        else:
            print(f"⚠️ 未获取到实际URL，使用原始URL")
            processing_url = original_url
    else:
        processing_url = original_url

    # 第二步：使用获取到的URL进行实际处理
    # 检查是否是跨域URL，如果是则使用页面导航
    if 'vip.postoa.com.cn' in processing_url:
        print(f"🌐 检测到跨域URL，使用页面导航访问")
        try:
            # 导航到实际URL
            await page.goto(processing_url, wait_until='networkidle')
            # 获取页面内容
            detail_html = await page.content()
            print(f"✅ 成功访问跨域URL并获取页面内容")
        except Exception as e:
            print(f"❌ 访问跨域URL失败: {e}")
            return f"访问跨域URL失败: {e}", False
    else:
        # 同域URL，使用fetch
        detail_html = await page.evaluate("""async (url) => {
            const response = await fetch(url, { method: 'GET', credentials: 'include' });
            return await response.text();
        }""", processing_url)

    soup = BeautifulSoup(detail_html, "html.parser")
    post_data = {tag.get("name"): tag.get("value", "") for tag in soup.find_all("input", type="hidden") if tag.get("name")}
    textarea = soup.find("textarea", {"id": "text"})
    if textarea:
        post_data["text"] = textarea.text.strip()

    # 根据当前页面域名确定提交URL
    if 'vip.postoa.com.cn' in processing_url:
        # 如果在vip域名下，使用vip的提交URL
        submit_url = "https://vip.postoa.com.cn/worknoticeInfoController/zjbj.done"
        print(f"🎯 使用vip域名提交URL: {submit_url}")
    else:
        # 使用原有的提交URL
        submit_url = SUBMIT_URL
        print(f"🎯 使用原有提交URL: {submit_url}")

    result = await page.evaluate("""([url, data]) => {
        const formBody = new URLSearchParams(data);
        return fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: formBody, credentials: 'include' }).then(r => r.text());
    }""", [submit_url, post_data])
    return result

# 根据URL类型选择不同的提交方法
async def fetch_and_submit(page, daiban):
    url = daiban.get('原始URL', daiban.get('链接', ''))
    file_type = daiban.get('文件类型', '')

    print(f"🔄 处理代办类型: {file_type}")
    print(f"🔗 URL: {url}")

    # 根据文件类型和URL特征选择处理方法
    if file_type == "直属单位部室收文" or '/sw/' in url or 'flowTypeClassification=SW' in url:
        # SW类型收文，使用新的办结流程
        print(f"📋 识别为收文类型，使用新办结流程")
        result, success = await fetch_and_submit_new_worknotice(page, daiban)
    elif '/gztz/' in url or 'flowTypeClassification=GZTZ' in url:
        # GZTZ工作联系单类型
        print(f"📋 识别为工作联系单类型，使用新办结流程")
        result, success = await fetch_and_submit_new_worknotice(page, daiban)
    elif 'worknoticeInfoController' in url:
        # 旧版工作联系单
        print(f"📋 识别为旧版工作联系单，使用旧办结流程")
        result = await fetch_and_submit_worknotice(page, daiban)
        # 检查result是否是元组（新流程可能返回错误信息）
        if isinstance(result, tuple):
            result, success = result
        else:
            success = "001" in str(result)
    elif 'archives/swAction' in url:
        # 档案管理
        print(f"📋 识别为档案管理，使用档案办结流程")
        result = await fetch_and_submit_archives(page, daiban)
        success = "流程结束" in str(result)
    else:
        # 默认尝试新的办结流程
        print(f"⚠️ 未知类型 (文件类型: {file_type})，尝试新办结流程")
        result, success = await fetch_and_submit_new_worknotice(page, daiban)

    return result, success

# ================== 导出 Excel ==================
def export_to_excel(records):
    df = pd.DataFrame(records)
    filename = f"代办记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False)
    print(f"📄 已导出：{filename}")

# ================== API数据类型分析 ==================
def analyze_daiban_types_from_api_data(all_data):
    """从API返回数据中分析代办类型"""
    type_analysis = {
        'fileTypeId': {},      # 文件类型ID统计
        'filetypename': {},    # 文件类型名称统计
        'sysType': {},         # 系统类型统计
        'url_patterns': {},    # URL模式统计
        'combined_types': {}   # 组合类型统计
    }

    total_records = 0

    print("\n" + "="*80)
    print("📊 API数据中的代办类型分析")
    print("="*80)

    for json_data in all_data:
        if json_data.get('code') == 200 and 'data' in json_data:
            records = json_data['data'].get('records', [])
            total_records += len(records)

            for record in records:
                # 分析各个字段
                file_type_id = record.get('fileTypeId', 'Unknown')
                file_type_name = record.get('filetypename', 'Unknown')
                sys_type = record.get('sysType', 'Unknown')
                url = record.get('url', '')

                # 统计fileTypeId
                if file_type_id not in type_analysis['fileTypeId']:
                    type_analysis['fileTypeId'][file_type_id] = {'count': 0, 'examples': []}
                type_analysis['fileTypeId'][file_type_id]['count'] += 1
                if len(type_analysis['fileTypeId'][file_type_id]['examples']) < 3:
                    type_analysis['fileTypeId'][file_type_id]['examples'].append({
                        'title': record.get('title', ''),
                        'url': url,
                        'filetypename': file_type_name
                    })

                # 统计filetypename
                if file_type_name not in type_analysis['filetypename']:
                    type_analysis['filetypename'][file_type_name] = {'count': 0, 'examples': []}
                type_analysis['filetypename'][file_type_name]['count'] += 1
                if len(type_analysis['filetypename'][file_type_name]['examples']) < 3:
                    type_analysis['filetypename'][file_type_name]['examples'].append({
                        'title': record.get('title', ''),
                        'url': url,
                        'fileTypeId': file_type_id
                    })

                # 统计sysType
                if sys_type not in type_analysis['sysType']:
                    type_analysis['sysType'][sys_type] = {'count': 0, 'examples': []}
                type_analysis['sysType'][sys_type]['count'] += 1
                if len(type_analysis['sysType'][sys_type]['examples']) < 3:
                    type_analysis['sysType'][sys_type]['examples'].append({
                        'title': record.get('title', ''),
                        'url': url,
                        'filetypename': file_type_name
                    })

                # 分析URL模式
                url_pattern = extract_url_pattern(url)
                if url_pattern not in type_analysis['url_patterns']:
                    type_analysis['url_patterns'][url_pattern] = {'count': 0, 'examples': []}
                type_analysis['url_patterns'][url_pattern]['count'] += 1
                if len(type_analysis['url_patterns'][url_pattern]['examples']) < 3:
                    type_analysis['url_patterns'][url_pattern]['examples'].append({
                        'title': record.get('title', ''),
                        'url': url,
                        'filetypename': file_type_name
                    })

                # 组合类型分析 (fileTypeId + filetypename)
                combined_key = f"{file_type_id}|{file_type_name}"
                if combined_key not in type_analysis['combined_types']:
                    type_analysis['combined_types'][combined_key] = {'count': 0, 'examples': []}
                type_analysis['combined_types'][combined_key]['count'] += 1
                if len(type_analysis['combined_types'][combined_key]['examples']) < 3:
                    type_analysis['combined_types'][combined_key]['examples'].append({
                        'title': record.get('title', ''),
                        'url': url
                    })

    # 打印分析结果
    print(f"📈 总记录数: {total_records}")

    print(f"\n🔸 按文件类型ID (fileTypeId) 分类:")
    for type_id, data in sorted(type_analysis['fileTypeId'].items(), key=lambda x: x[1]['count'], reverse=True):
        print(f"   {type_id}: {data['count']} 条")
        for example in data['examples']:
            print(f"     - {example['title']} ({example['filetypename']})")

    print(f"\n🔸 按文件类型名称 (filetypename) 分类:")
    for type_name, data in sorted(type_analysis['filetypename'].items(), key=lambda x: x[1]['count'], reverse=True):
        print(f"   {type_name}: {data['count']} 条")
        for example in data['examples']:
            print(f"     - {example['title']} (ID: {example['fileTypeId']})")

    print(f"\n🔸 按系统类型 (sysType) 分类:")
    for sys_type, data in sorted(type_analysis['sysType'].items(), key=lambda x: x[1]['count'], reverse=True):
        print(f"   {sys_type}: {data['count']} 条")
        for example in data['examples']:
            print(f"     - {example['title']} ({example['filetypename']})")

    print(f"\n🔸 按URL模式分类:")
    for pattern, data in sorted(type_analysis['url_patterns'].items(), key=lambda x: x[1]['count'], reverse=True):
        print(f"   {pattern}: {data['count']} 条")
        for example in data['examples']:
            print(f"     - {example['title']} ({example['filetypename']})")

    return type_analysis

def extract_url_pattern(url):
    """提取URL的模式特征"""
    if not url:
        return "空URL"

    # 提取主要路径模式
    if '/gztz/' in url:
        return "工作联系单(/gztz/)"
    elif '/sw/' in url:
        return "收文管理(/sw/)"
    elif '/worknotice/' in url:
        return "工作通知(/worknotice/)"
    elif '/archives/' in url:
        return "档案管理(/archives/)"
    elif '/examine/' in url:
        return "审查(/examine/)"
    elif '/approval/' in url:
        return "审批(/approval/)"
    elif 'worknoticeInfoController' in url:
        return "旧版工作联系单(worknoticeInfoController)"
    elif 'swAction' in url:
        return "档案操作(swAction)"
    elif 'flowTypeClassification=SW' in url:
        return "收文流程(SW)"
    elif 'flowTypeClassification=GZTZ' in url:
        return "工作联系单流程(GZTZ)"
    else:
        # 提取第一级路径
        import re
        match = re.search(r'/([^/]+)/', url)
        if match:
            return f"其他类型(/{match.group(1)}/)"
        else:
            return "未知模式"

# 新增打印代办信息的函数
def print_daiban_summary(daiban_list):
    """打印代办事项摘要"""
    print("\n" + "="*50)
    print("代办列表摘要:")
    print("="*50)

    for idx, daiban in enumerate(daiban_list, 1):
        print(f"\n{idx}. {daiban['标题']}")
        print(f"   日期: {daiban['日期']}")
        print(f"   流程ID: {daiban.get('流程ID', 'N/A')}")
        print(f"   文件类型: {daiban.get('文件类型', 'N/A')}")
        print(f"   前置用户: {daiban.get('前置用户', 'N/A')}")
        print(f"   链接: {daiban['链接']}")

# ================== 主流程 ==================
def find_chrome_executable() -> Optional[str]:
    """
    查找本地安装的Chrome浏览器可执行文件
    返回: 找到则返回路径，未找到返回None
    """
    system = platform.system()
    print(f"当前操作系统: {system}")

    if system == "Windows":
        # Windows下的常见Chrome安装路径
        locations = [
            os.path.expandvars(r"%ProgramFiles%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%LocalAppData%\Google\Chrome\Application\chrome.exe"),
        ]
        print("Windows系统，检查以下Chrome安装路径:")
        for loc in locations:
            print(f"- {loc}")
    else:
        print(f"不支持的操作系统: {system}")
        locations = []

    # 检查所有可能的位置
    for path in locations:
        if os.path.exists(path) and os.path.isfile(path):
            print(f"找到本地Chrome浏览器: {path}")
            return path

    print("未找到本地Chrome浏览器，将使用Playwright内置浏览器")
    return None

# async def main():
#     saved_config = load_config()
#     login_dialog = LoginDialog(saved_config)
#     login_dialog.root.mainloop()

#     async with async_playwright() as p:
#         print("\n=== 检查Chrome浏览器 ===")
#         # 检查本地Chrome
#         chrome_path = find_chrome_executable()
#         if chrome_path:
#             print(f"✓ 使用找到的Chrome浏览器: {chrome_path}")
#             # 使用本地Chrome
#             browser = await p.chromium.launch(
#                 executable_path=chrome_path,
#                 headless=False
#             )
#         else:
#             current_dir_chrome = os.path.join(os.getcwd(), "chrome", "chrome.exe")
#             # 使用Playwright内置浏览器
#             browser = await p.chromium.launch( executable_path=current_dir_chrome,headless=False)

#         context = await browser.new_context()
#         page, login_success = await get_cookies_after_login(context)

#         if not login_success:
#             messagebox.showerror("登录失败", "密码错误，请重新运行程序输入密码！")
#             return

#         save_config({
#             'password': LOGIN_DATA['password'],
#             'org_name': LOGIN_DATA['org_name'],
#             'target_date': LOGIN_DATA['target_date']
#         })

#         # 获取代办列表
#         print("\n开始获取代办列表...")
#         all_data = await get_all_daiban_list_data(page)

#         # 先分析API数据中的代办类型
#         print("\n" + "="*60)
#         print("🔍 开始分析API数据中的代办类型...")
#         print("="*60)
#         try:
#             analyze_daiban_types_from_api_data(all_data)
#         except Exception as e:
#             print(f"❌ 分析代办类型时出错: {e}")
#             import traceback
#             traceback.print_exc()

#         # 解析代办数据
#         daiban_list = parse_all_daiban(all_data, LOGIN_DATA['target_date'])

#         if not daiban_list:
#             print("\n没有需要处理的代办事项")
#         else:
#             # 打印代办摘要
#             print_daiban_summary(daiban_list)
#             for idx, daiban in enumerate(daiban_list, 1):
#                 print(f"\n✅ 正在提交第 {idx}/{len(daiban_list)} 条：{daiban['标题']}\n{daiban['链接']}")
#                 result, success = await fetch_and_submit(page, daiban)
#                 daiban['提交结果'] = "成功" if success else "失败"
#                 print(f"提交结果: {'✅ 成功' if success else '❌ 失败'}")
#                 daiban_records.append(daiban)

#             # 只在有提交记录时导出Excel
#             if daiban_records:
#                 export_to_excel(daiban_records)

#         await browser.close()

# ================== 启动 ==================
if __name__ == "__main__":
    # 加载配置
    config = load_config()

    # 创建并显示登录对话框
    dialog = LoginDialog(config)
    #asyncio.run(main())
    dialog.root.mainloop()
