{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1067", "installByDefault": true, "browserVersion": "115.0.5790.24"}, {"name": "chromium-with-symbols", "revision": "1067", "installByDefault": false, "browserVersion": "115.0.5790.24"}, {"name": "chromium-tip-of-tree", "revision": "1120", "installByDefault": false, "browserVersion": "116.0.5805.0"}, {"name": "firefox", "revision": "1408", "installByDefault": true, "browserVersion": "113.0"}, {"name": "firefox-beta", "revision": "1410", "installByDefault": false, "browserVersion": "114.0b3"}, {"name": "webkit", "revision": "1860", "installByDefault": true, "revisionOverrides": {"mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816", "ubuntu18.04": "1728"}, "browserVersion": "16.4"}, {"name": "ffmpeg", "revision": "1009", "installByDefault": true}, {"name": "android", "revision": "1000", "installByDefault": false}]}