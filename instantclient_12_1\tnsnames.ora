# tnsnames.ora Network Configuration File:  tnsnames.ora
# Generated by Oracle configuration tools.

GZAMC1_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gzamc1)
      (SERVER = DEDICATED)
    )
  )

CBMIS_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = cbmis)
      (SERVER = DEDICATED)
    )
  )

GJINTER_*********** =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gjinter)
      (SERVER = DEDICATED)
    )
  )
  
GJINTER_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gjinter)
      (SERVER = DEDICATED)
    )
  )
  
GJINTER_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gjinter)
      (SERVER = DEDICATED)
    )
  )
  
ORCL_************* =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = orcl)
      (SERVER = DEDICATED)
    )
  )

  
GJINTER_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gjinter)
      (SERVER = DEDICATED)
    )
  )

GZAMC1_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gzamc1)
      (SERVER = DEDICATED)
    )
  )

GZAMC2_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gzamc2)
      (SERVER = DEDICATED)
    )
  )

ZNFJ_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = znfj)
      (SERVER = DEDICATED)
    )
  )

GZAMC1_************ =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = gzamc1)
      (SERVER = DEDICATED)
    )
 )

gjpy.world =
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521))
    )
    (CONNECT_DATA =
     (SERVER = DEDICATED)(SID = gjinter) 
    )
  )
 
cpstems.WORLD =
  (DESCRIPTION =
      (ADDRESS_LIST =
  (ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521))
      )
  (CONNECT_DATA =
    (SERVER = DEDICATED)
    (SID = orcl)
  )
 )

dbcenter=
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = *************)(PORT = 1521))
    )
    (CONNECT_DATA =
      (SID = dbcenter)
      (SERVER = DEDICATED)
    )
  ) 
gkxt_************ =
(DESCRIPTION =                                                     
   (ADDRESS_LIST =                                                
      (ADDRESS = (PROTOCOL = TCP)(HOST = ************)(PORT = 1521))
   )                                                              
   (CONNECT_DATA =                                                
     (SERVICE_NAME = gkxt)                                        
   )                                                              
  )   
gzemstest_********* =
(DESCRIPTION =                                                     
   (ADDRESS_LIST =                                                
      (ADDRESS = (PROTOCOL = TCP)(HOST = *********)(PORT = 1521))
   )                                                              
   (CONNECT_DATA =                                                
     (SERVICE_NAME = gzemstest)                                        
   )                                                              
  )  