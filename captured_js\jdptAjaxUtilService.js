if (typeof dwr == 'undefined' || dwr.engine == undefined) throw new Error('You must include DWR engine before including this file');

(function() {
if (dwr.engine._getObject("jdptAjaxUtilService") == undefined) {
var p;

p = {};







p.BAK_checkLoginSmsKaptcha_BAK01 = function(p0, p1, p2, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'BAK_checkLoginSmsKaptcha_BAK01', arguments);
};






p.verifyAuthCode = function(p0, p1, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'verifyAuthCode', arguments);
};





p.sendAuthCode = function(p0, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'sendAuthCode', arguments);
};






p.checkLoginForPhoneSMS = function(p0, p1, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'checkLoginForPhoneSMS', arguments);
};






p.BAK_checkLoginForPhoneSMS_BAK01 = function(p0, p1, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'BAK_checkLoginForPhoneSMS_BAK01', arguments);
};




p.hello = function(callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'hello', arguments);
};







p.checkLoginSmsKaptcha = function(p0, p1, p2, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'checkLoginSmsKaptcha', arguments);
};




p.init = function(callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'init', arguments);
};





p.test = function(p0, callback) {
return dwr.engine._execute(p._path, 'jdptAjaxUtilService', 'test', arguments);
};

dwr.engine._setObject("jdptAjaxUtilService", p);
}
})();

