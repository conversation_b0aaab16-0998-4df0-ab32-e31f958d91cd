import datetime
import io
import os
import socket
import sys
import threading
import tkinter as tk
import traceback
import urllib
from datetime import timedelta
from functools import reduce
from multiprocessing.dummy import Pool
from tkinter import *
from tkinter import scrolledtext
from tkinter import ttk
import json
from urllib.parse import quote
import concurrent.futures
import pandas
import re
import requests
import time
from os import path
from bs4 import BeautifulSoup
from tkcalendar import DateEntry

from tool import Tool


# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

output_folder = "路单查询统计-" +datetime.datetime.now().strftime("%Y%m%d%H%M%S")  # 文件夹名称

output_folder=os.path.join(current_dir, output_folder)
def getckld(Date):


    start_date = Date + ' 00:00:00'
    end_date = Date + ' 23:59:59'

    tool.process_input('开始日期:' + start_date)
    tool.process_input('结束日期:' + end_date)
    # 获取选择的值
    selected_interFlag = interFlag_options[interFlag_combobox.get()]
    selected_routeLevel = routeLevel_options[routeLevel_combobox.get()]
    selected_statusJ = statusJ_options[statusJ_combobox.get()]
    selected_workShop = workshop_options[workshop_combobox.get()]
    handoverObjectName = input_textbox.get()
    # 匹配第一个冒号后面的内容
    match = re.search(r":\s*(.*)", handoverObjectName)
    if match:
        handoverObjectNo = match.group(1).strip()
    else:
        handoverObjectNo = ""
    # tool.process_input('查询类型:' + interFlag_combobox.get())
    # tool.process_input('邮路级别:' + routeLevel_combobox.get())
    # tool.process_input('状态:' + statusJ_combobox.get())
    # tool.process_input('车间:' + workshop_combobox.get())

    if organization_combobox.get() == '国际':
        opOrgCode1 = '51040034'
        opOrgName1 = '广州国际'
        exitOpOrgName1 = '广州国际'
        exitOpOrgCode1 = '51040034'

    elif organization_combobox.get() == '国内':
        opOrgCode1 = '51000061'
        opOrgName1 = '广州广航'
        exitOpOrgName1 = '广州广航'
        exitOpOrgCode1 = '51000061'

    exitShopCode1 = selected_workShop
    exitShopName1 = workshop_combobox.get()
    entOpOrgCode1 = ''
    entOpOrgName1 = ''
    entShopCode1 = ''
    entShopName1 = ''
    status1 = selected_statusJ
    statusJ1 = ''


    # print(selected_interFlag,selected_routeLevel,selected_statusJ)
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/tolist',
        'Sec-Ch-Ua': '"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    data = {
        'pageNo': 1,
        'pageSize': 2000,
        'businessUnit': 'B',
        'opOrgCode': opOrgCode1,
        'opOrgName': opOrgName1,
        'exitOpOrgName': exitOpOrgName1,
        'exitOpOrgCode': exitOpOrgCode1,
        'exitShopCode': exitShopCode1,
        'exitShopName': exitShopName1,
        'interFlag': selected_interFlag,
        'entOpOrgCode': entOpOrgCode1,
        'entShopCode': entShopCode1,
        'entShopName': entShopName1,
        'handoverObjectName': handoverObjectName,
        'handoverObjectNo': handoverObjectNo,
        'billName': '',
        'gmtCreatedBegin': start_date,
        'gmtCreatedEnd': end_date,
        'routeLevel': selected_routeLevel,
        'billNo': '',
        'truckingNo': '',
        'status': status1,
        'statusJ': statusJ1,
        'vehicleNo': ''
    }

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    # while '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
    #     response = session.post(url, headers=headers, data=data, verify=False)
    #     r = response.text
    # print(r)
    if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员')

    else:
        # html=BeautifulSoup(r,'html.parser')
        html = BeautifulSoup(r, 'lxml')
        # 匹配数字部分
        match = re.search(r'共\s*(\d+)页', str(html))

        if match:
            page_number = int(match.group(1))
            print("提取的数字为:", page_number)
        else:
            print(html)
            print("未找到匹配的数字")

        form = html.find('form', id='searchForm')
        if form is None:
            # 处理未找到表单元素的情况
            print(form)
        else:
            dataOutput = {
                "类型": [],
                "交接对象": [],
                "派车单号": [],
                "航班/车牌号": [],
                "车厢/车牌号": [],
                "车厢码": [],
                "路单流水号": [],
                "邮件总数": [],
                "总数": [],
                "重量(kg)": [],
                "进口机构": [],
                "已勾核数量": [],
                "未勾核数量": [],
                "操作员": [],
                "封车时间": [],
                "状态": [],
                "装车时间": [],
                "装车完毕时间": [],
                "装车时长": [],
                "进局时间": [],
                "解车时间": []
            }
            # 初始化表单数据
            form_data = {}
            for input_tag in form.find_all(["input", "select"]):
                if input_tag.name == "input":
                    name = input_tag.get("name")
                    value = input_tag.get("value", "")
                elif input_tag.name == "select":
                    name = input_tag.get("name")
                    #value = input_tag.find("option", selected=True).get("value", "")
                    # 查找带有 selected="selected" 属性的 option 标签

                    selected_option = input_tag.find("option", attrs={"selected": ""})
                    #print(selected_option)
                    if selected_option:
                        value = selected_option.get("value", "")
                    else:
                        value = ""  # 没有默认选中的情况下将值设置为空字符串
                form_data[name] = value

        for i in range(1, page_number + 1):
            print('第' + str(i) + '页')
            form_data["pageNo"]= str(i)
            requests.packages.urllib3.disable_warnings()
            url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/sealExport"
            response = session.post(url, headers=headers, data=data, verify=False)
            #print(response.text)
            if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in response.text:
                tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员')
                tool.process_input('导出异常')
            else:
                jsonObj = json.loads(response.text)
                print(jsonObj)

                for line in jsonObj:
                    dataOutput["类型"].append(line.get('reserved8', ''))
                    dataOutput["交接对象"].append(line.get('handoverObjectName', ''))
                    dataOutput["派车单号"].append(line.get('truckingNo', ''))
                    dataOutput["航班/车牌号"].append(line.get('vehicleNo', ''))
                    dataOutput["车厢/车牌号"].append(line.get('throwVehicleNo', ''))
                    dataOutput["车厢码"].append(line.get('carriageNo', ''))
                    dataOutput["路单流水号"].append(line.get('billNo', '0'))
                    dataOutput["邮件总数"].append(line.get('totalMailNum', ''))
                    dataOutput["总数"].append(line.get('totalNum', ''))
                    dataOutput["重量(kg)"].append(line.get('totalWeight', ''))
                    dataOutput["进口机构"].append(line.get('destOrgName', ''))
                    dataOutput["已勾核数量"].append(line.get('expensiveProductsMailbagNum', ''))
                    dataOutput["未勾核数量"].append(line.get('expensiveProductsMailNum', ''))
                    dataOutput["操作员"].append(line.get('createUserName', ''))
                    dataOutput["封车时间"].append(line.get('reserved6', ''))
                    dataOutput["状态"].append(line.get('status', ''))
                    dataOutput["装车时间"].append(line.get('reserved5', ''))
                    dataOutput["装车完毕时间"].append(line.get('reserved7', ''))
                    dataOutput["装车时长"].append(line.get('reserved10', ''))
                    dataOutput["进局时间"].append(line.get('notes', ''))
                    dataOutput["解车时间"].append(line.get('reserved4', ''))

    return dataOutput

def getjkld(Date):


    start_date = Date + ' 00:00:00'
    end_date = Date + ' 23:59:59'

    tool.process_input('开始日期:' + start_date)
    tool.process_input('结束日期:' + end_date)
    # 获取选择的值
    selected_interFlag = interFlag_options[interFlag_combobox.get()]
    selected_routeLevel = routeLevel_options[routeLevel_combobox.get()]
    selected_statusJ = statusJ_options[statusJ_combobox.get()]
    selected_workShop = workshop_options[workshop_combobox.get()]
    handoverObjectName = input_textbox.get()
    # 匹配第一个冒号后面的内容
    match = re.search(r":\s*(.*)", handoverObjectName)
    if match:
        handoverObjectNo = match.group(1).strip()
    else:
        handoverObjectNo = ""
    # tool.process_input('查询类型:' + interFlag_combobox.get())
    # tool.process_input('邮路级别:' + routeLevel_combobox.get())
    # tool.process_input('状态:' + statusJ_combobox.get())
    # tool.process_input('车间:' + workshop_combobox.get())

    if organization_combobox.get() == '国际':
        opOrgCode1 = '51040034'
        opOrgName1 = '广州国际'
        entOpOrgCode1 = '51040034'
        entOpOrgName1 = '广州国际'


    elif organization_combobox.get() == '国内':
        opOrgCode1 = '51000061'
        opOrgName1 = '广州广航'
        entOpOrgName1 = '广州广航'
        entOpOrgCode1 = '51000061'

    exitOpOrgName1 = ''
    exitOpOrgCode1 = ''
    exitShopCode1 = ''
    exitShopName1 = ''

    entShopCode1 = selected_workShop
    entShopName1 = workshop_combobox.get()
    status1 = ''
    statusJ1 = selected_statusJ
    # print(selected_interFlag,selected_routeLevel,selected_statusJ)
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/listExport'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/tolist',
        'Sec-Ch-Ua': '"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    data = {
        'pageNo': 1,
        'pageSize': 2000,
        'businessUnit': 'B',
        'opOrgCode': opOrgCode1,
        'opOrgName': opOrgName1,
        'exitOpOrgName': exitOpOrgName1,
        'exitOpOrgCode': exitOpOrgCode1,
        'exitShopCode': exitShopCode1,
        'exitShopName': exitShopName1,
        'interFlag': selected_interFlag,
        'entOpOrgCode': entOpOrgCode1,
        'entShopCode': entShopCode1,
        'entShopName': entShopName1,
        'handoverObjectName': handoverObjectName,
        'handoverObjectNo': handoverObjectNo,
        'billName': '',
        'gmtCreatedBegin': start_date,
        'gmtCreatedEnd': end_date,
        'routeLevel': selected_routeLevel,
        'billNo': '',
        'truckingNo': '',
        'status': status1,
        'statusJ': statusJ1,
        'vehicleNo': ''
    }

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    file_path = os.path.join(output_folder, Date + interFlag_combobox.get() + routeLevel_combobox.get() + statusJ_combobox.get()+ datetime.datetime.now().strftime("%Y%m%d%H%M%S")+ "-路单查询.xlsx")  # 组合文件夹路径和文件名
    with open(file_path, "wb") as f:
        f.write(response.content)


def getmail(parent):
    start_date_str = str(
        start_date_entry.get_date()) + ' ' + starthour_combobox.get() + ':' + startminute_combobox.get() + ':' + startsecond_combobox.get()
    end_date_str = str(
        end_date_entry.get_date()) + ' ' + endhour_combobox.get() + ':' + endminute_combobox.get() + ':' + endsecond_combobox.get()

    # 将字符串转换为datetime对象
    start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d %H:%M:%S')
    end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d %H:%M:%S')

    # 定义时间间隔
    time_interval = timedelta(days=3)

    # 初始化当前分组的开始时间
    current_start_date = start_date

    while current_start_date < end_date:
        # 定义当前分组的结束时间
        current_end_date = min(current_start_date + time_interval, end_date)

        # 将当前分组的开始时间和结束时间转换为字符串
        current_start_date_str = current_start_date.strftime('%Y-%m-%d %H:%M:%S')
        current_end_date_str = current_end_date.strftime('%Y-%m-%d %H:%M:%S')

        # 在当前时间分组内执行代码
        print(f'运行代码，时间分组：{current_start_date_str} 到 {current_end_date_str}')
        # 在这里插入要运行的代码，例如调用某个函数:
        # run_your_code(current_start_date, current_end_date)
        getzb(current_start_date_str, current_end_date_str,parent)
        # 更新当前分组的开始时间
        current_start_date = current_end_date
    result = dataOutput
    return result
def getzb(start_date,end_date,parent):
    #print(jdptid)
    # start_date = str(start_date_entry.get_date())+' 00:00:00'
    # end_date = str(end_date_entry.get_date())+' 23:59:59'
    # start_date = str(start_date_entry.get_date()) + ' '+starthour_combobox.get()+':'+startminute_combobox.get()+':'+startsecond_combobox.get()
    # end_date = str(end_date_entry.get_date()) + ' '+endhour_combobox.get()+':'+endminute_combobox.get()+':'+endsecond_combobox.get()

    # tool.process_input('开始日期:' + start_date)
    # tool.process_input('结束日期:' + end_date)

    tool.process_input(f'时间分组：{start_date} 到 {end_date}')
    # 获取选择的值
    selected_interFlag = interFlag_options[interFlag_combobox.get()]
    selected_routeLevel = routeLevel_options[routeLevel_combobox.get()]
    selected_statusJ = statusJ_options[statusJ_combobox.get()]
    selected_workShop = workshop_options[workshop_combobox.get()]

    handoverObjectName = input_textbox.get()
    # 匹配第一个冒号后面的内容
    match = re.search(r":\s*(.*)", handoverObjectName)
    if match:
        handoverObjectNo = match.group(1).strip()
    else:
        handoverObjectNo = ""

    tool.process_input('查询类型:' + interFlag_combobox.get())
    tool.process_input('邮路级别:' + routeLevel_combobox.get())
    tool.process_input('状态:' + statusJ_combobox.get())
    tool.process_input('车间:' + workshop_combobox.get())
    tool.process_input('交接对象:' + input_textbox.get())

    if organization_combobox.get()=='国际':
        opOrgCode= '51040034'
        opOrgName= '广州国际'

        # 判断进出口
        if interFlag_combobox.get()=='出口':
            exitOpOrgName = '广州国际'
            exitOpOrgCode = '51040034'
            exitShopCode = selected_workShop
            exitShopName = workshop_combobox.get()
            entOpOrgCode = ''
            entOpOrgName = ''
            entShopCode = ''
            entShopName = ''
            status = selected_statusJ
            statusJ = ''
        else:
            exitOpOrgName = ''
            exitOpOrgCode = ''
            exitShopCode = ''
            exitShopName = ''
            entOpOrgCode = '51040034'
            entOpOrgName = '广州国际'
            entShopCode = selected_workShop
            entShopName = workshop_combobox.get()
            status = ''
            statusJ = selected_statusJ

    elif organization_combobox.get()=='国内':
        opOrgCode = '51000061'
        opOrgName = '广州广航'
        # 判断进出口
        if interFlag_combobox.get() == '出口':
            exitOpOrgName = '广州广航'
            exitOpOrgCode = '51000061'
            exitShopCode = selected_workShop
            exitShopName = workshop_combobox.get()
            entOpOrgCode = ''
            entOpOrgName = ''
            entShopCode = ''
            entShopName = ''
            status = selected_statusJ
            statusJ = ''
        else:
            exitOpOrgName = ''
            exitOpOrgCode = ''
            exitShopCode = ''
            exitShopName = ''
            entOpOrgCode = '51000061'
            entOpOrgName = '广州广航'
            entShopCode = selected_workShop
            entShopName = workshop_combobox.get()
            status = ''
            statusJ = selected_statusJ

    #print(selected_interFlag,selected_routeLevel,selected_statusJ)
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/tolist',
        'Sec-Ch-Ua':'"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }


    data = {
        'pageNo': 1,
        'pageSize': 200,
        'businessUnit': 'B',
        'opOrgCode': opOrgCode,
        'opOrgName': opOrgName,
        'exitOpOrgName': exitOpOrgName,
        'exitOpOrgCode': exitOpOrgCode,
        'exitShopCode': exitShopCode,
        'exitShopName': exitShopName,
        'interFlag': selected_interFlag,
        'entOpOrgCode': entOpOrgCode,
        'entShopCode': entShopCode,
        'entShopName': entShopName,
        'handoverObjectName': handoverObjectName,
        'handoverObjectNo': handoverObjectNo,
        'billName': '',
        'gmtCreatedBegin': start_date,
        'gmtCreatedEnd': end_date,
        'routeLevel': selected_routeLevel,
        'billNo': '',
        'truckingNo': '',
        'status': status,
        'statusJ': statusJ,
        'vehicleNo': ''
    }



    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text

    results=''
    # while '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
    #     response = session.post(url, headers=headers, data=data, verify=False)
    #     r = response.text
    #print(r)
    if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员')

    else:
    #html=BeautifulSoup(r,'html.parser')
        html = BeautifulSoup(r, 'lxml')

        zjs = html.find('tbody')
        #print(zjs)
        # 匹配数字部分
        match = re.search(r'共\s*(\d+)页', str(html))

        if match:
            page_number = int(match.group(1))
            print("提取的数字为:", page_number)
        else:
            print(html)
            print("未找到匹配的数字")

        with concurrent.futures.ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            futures = [
                executor.submit(process_page, i, session, url, headers, opOrgCode, opOrgName, exitOpOrgName, exitOpOrgCode,
                                exitShopCode, exitShopName, selected_interFlag, entOpOrgCode, entShopCode, entShopName,
                                start_date, end_date, selected_routeLevel, status, statusJ, selected_statusJ,entOpOrgName,parent,
                                handoverObjectName,handoverObjectNo
                                ) for i in range(1, page_number + 1)]
            for future in concurrent.futures.as_completed(futures):
                results = future.result()

        # for i in range(1, page_number + 1):
        #     print('第' + str(i) + '页')
        #
        #     data = {
        #         'pageNo': i,
        #         'pageSize': 200,
        #         'businessUnit': 'B',
        #         'opOrgCode': opOrgCode,
        #         'opOrgName': opOrgName,
        #         'exitOpOrgName': exitOpOrgName,
        #         'exitOpOrgCode': exitOpOrgCode,
        #         'exitShopCode': exitShopCode,
        #         'exitShopName': exitShopName,
        #         'interFlag': selected_interFlag,
        #         'entOpOrgCode': entOpOrgCode,
        #         'entShopCode': entShopCode,
        #         'entShopName': entShopName,
        #         'handoverObjectName': '',
        #         'billName': '',
        #         'gmtCreatedBegin': start_date,
        #         'gmtCreatedEnd': end_date,
        #         'routeLevel': selected_routeLevel,
        #         'billNo': '',
        #         'truckingNo': '',
        #         'status': status,
        #         'statusJ': statusJ,
        #         'vehicleNo': ''
        #     }
        #
        #     response = session.post(url, headers=headers, data=data, verify=False)
        #     r = response.text
        #
        #     if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        #         parent.after(0,tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员'))
        #
        #     else:
        #         # html=BeautifulSoup(r,'html.parser')
        #         html = BeautifulSoup(r, 'lxml')
        #
        #         zjs = html.find('tbody')
        #     if zjs:
        #
        #         trs = zjs.find_all('tr')
        #         for tr in trs:
        #             tds=tr.find_all('td')
        #             #print(input_textbox.get())
        #             #if input_textbox.get() in tds[3].get_text():
        #
        #             if selected_interFlag=='1':#出口
        #
        #                 if '未封车' in statusJ_combobox.get():
        #                     a = tds[7]
        #                     jjdx=tds[3].get_text()
        #                     tool.process_input(tds[3].get_text() + '总数:' + a.get_text())
        #                     pattern = r"'(.*?)'"
        #                     result = re.findall(pattern, str(a))
        #                     print(result)
        #                     iid=result[0]
        #                     interFlag = result[1]
        #                     djType=result[2]
        #
        #                     results=gotoLinks(iid, interFlag,djType, start_date, end_date, selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
        #                     exitOpOrgCode ,
        #                     exitShopCode ,
        #                     exitShopName,
        #                     entOpOrgName,
        #                     entOpOrgCode,
        #                     entShopCode,
        #                     entShopName,parent)
        #                 elif '已封车' in statusJ_combobox.get():
        #                     a = tds[10]
        #                     jjdx = tds[3].get_text()
        #                     tool.process_input(tds[3].get_text() + '总数:' + a.get_text())
        #                     pattern = r"'(.*?)'"
        #                     result = re.findall(pattern, str(a))
        #                     print(result)
        #                     entOpOrgCodeBY=result[0]
        #                     handoverObjectNo = result[1]
        #                     truckingNo = result[2]
        #                     vehicleNo = result[3]
        #                     throwVehicleNo = result[4]
        #                     carriageNo=result[5]
        #                     billNo = result[6]
        #                     createUserCode = result[7]
        #                     status = result[8]
        #                     departId = result[9]
        #                     interFlag = result[10]
        #                     djType = result[11]
        #                     loadTime = result[12]
        #
        #                     results=gotoLinksGroup(entOpOrgCodeBY,handoverObjectNo, truckingNo, vehicleNo, throwVehicleNo, carriageNo, billNo,
        #                                    createUserCode, status, departId, interFlag, djType, loadTime, start_date,
        #                                    end_date, selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
        #                     exitOpOrgCode ,
        #                     exitShopCode ,
        #                     exitShopName,
        #                     entOpOrgName,
        #                     entOpOrgCode,
        #                     entShopCode,
        #                     entShopName,)
        #             else:
        #
        #
        #                 if '未进局' in statusJ_combobox.get():
        #                     a = tds[11]
        #                     jjdx = tds[3].get_text()
        #                     tool.process_input(tds[3].get_text() + '总数:' + a.get_text())
        #                     pattern = r"'(.*?)'"
        #                     result = re.findall(pattern, str(a))
        #                     print(result)
        #                     handoverObjectNoGroup = result[0]
        #                     truckingNoGroup = result[1]
        #                     vehicleNoGroup = result[2]
        #                     throwVehicleNoGroup = result[3]
        #                     carriageNoGroup = result[4]
        #                     billNoGroup = result[5]
        #                     interFlag = result[6]
        #                     djType = result[7]
        #                     areaOrOrgTypeData = result[8]
        #
        #                     results=gotoLinksGroupJinWjj(handoverObjectNoGroup, truckingNoGroup, vehicleNoGroup, throwVehicleNoGroup,
        #                                          carriageNoGroup, billNoGroup,interFlag,  djType, areaOrOrgTypeData, start_date,
        #                                          end_date, selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
        #                     exitOpOrgCode ,
        #                     exitShopCode ,
        #                     exitShopName,
        #                     entOpOrgName,
        #                     entOpOrgCode,
        #                     entShopCode,
        #                     entShopName,)
        #                 elif '进局未解车' in statusJ_combobox.get():
        #                     a = tds[10]
        #                     jjdx = tds[3].get_text()
        #                     tool.process_input(tds[3].get_text() + '总数:' + a.get_text())
        #                     pattern = r"'(.*?)'"
        #                     result = re.findall(pattern, str(a))
        #                     print(result)
        #                     arriveId = result[0]
        #                     interFlag = result[1]
        #                     djType = result[2]
        #                     areaOrOrgTypeData = result[3]
        #
        #                     results=gotoLinksGroupJin(arriveId,interFlag,djType,areaOrOrgTypeData,start_date,end_date,selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
        #                     exitOpOrgCode ,
        #                     exitShopCode ,
        #                     exitShopName,
        #                     entOpOrgName,
        #                     entOpOrgCode,
        #                     entShopCode,
        #                     entShopName,)
        #                 else:
        #                     a = tds[10]
        #                     jjdx = tds[3].get_text()
        #                     if jjdx != '航空转运':
        #
        #                         tool.process_input(tds[3].get_text() + '总数:' + a.get_text())
        #                         pattern = r"'(.*?)'"
        #                         result = re.findall(pattern, str(a))
        #                         print(result)
        #                         iid = result[0]
        #                         interFlag = result[1]
        #                         djType = result[2]
        #                         results=gotoLinks(iid, interFlag, djType, start_date, end_date, selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
        #                         exitOpOrgCode ,
        #                         exitShopCode ,
        #                         exitShopName,
        #                         entOpOrgName,
        #                         entOpOrgCode,
        #                         entShopCode,
        #                         entShopName,parent)
        #     else:
        #         tool.process_input('无数据')
    return results


def process_page(i, session, url, headers, opOrgCode, opOrgName, exitOpOrgName, exitOpOrgCode, exitShopCode,
                 exitShopName,
                 selected_interFlag, entOpOrgCode, entShopCode, entShopName, start_date, end_date, selected_routeLevel,
                 status, statusJ, selected_statusJ,entOpOrgName,parent,handoverObjectName,handoverObjectNo):
    print('第' + str(i) + '页')

    data = {
        'pageNo': i,
        'pageSize': 200,
        'businessUnit': 'B',
        'opOrgCode': opOrgCode,
        'opOrgName': opOrgName,
        'exitOpOrgName': exitOpOrgName,
        'exitOpOrgCode': exitOpOrgCode,
        'exitShopCode': exitShopCode,
        'exitShopName': exitShopName,
        'interFlag': selected_interFlag,
        'entOpOrgCode': entOpOrgCode,
        'entShopCode': entShopCode,
        'entShopName': entShopName,
        'handoverObjectName': handoverObjectName,
        'handoverObjectNo': handoverObjectNo,
        'billName': '',
        'gmtCreatedBegin': start_date,
        'gmtCreatedEnd': end_date,
        'routeLevel': selected_routeLevel,
        'billNo': '',
        'truckingNo': '',
        'status': status,
        'statusJ': statusJ,
        'vehicleNo': ''
    }

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text

    if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        parent.after(0,tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员'))
        return

    html = BeautifulSoup(r, 'lxml')
    zjs = html.find('tbody')
    if zjs:
        trs = zjs.find_all('tr')
        with concurrent.futures.ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            futures = []
            for tr in trs:
                futures.append(
                    executor.submit(process_tr, tr, selected_interFlag, statusJ_combobox, start_date, end_date,
                                    selected_routeLevel, selected_statusJ, exitOpOrgName, exitOpOrgCode, exitShopCode,
                                    exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,parent))
            for future in concurrent.futures.as_completed(futures):
                results=future.result()

    return results

def process_tr(tr, selected_interFlag, statusJ_combobox, start_date, end_date, selected_routeLevel, selected_statusJ,
               exitOpOrgName, exitOpOrgCode, exitShopCode, exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,parent):
    tds = tr.find_all('td')
    jjdx = tds[3].get_text()
    hbh = tds[5].get_text()

    if selected_interFlag == '1':  # 出口
        if '未封车' in statusJ_combobox.get():
            process_unsealed(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                             exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh)
        elif '已封车' in statusJ_combobox.get():
            process_sealed(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                           exitOpOrgCode, exitShopCode, exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,parent,hbh)
    else:
        if '未进局' in statusJ_combobox.get():
            process_unentered(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                              exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh)
        elif '进局未解车' in statusJ_combobox.get():
            process_unloaded(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                             exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh)
        else:
            process_other(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode,
                  exitShopCode, exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,parent,hbh)


def process_unsealed(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                     exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent):
    a = tds[7]
    parent.after(0,tool.process_input(tds[3].get_text() + '总数:' + a.get_text()))
    pattern = r"'(.*?)'"
    result = re.findall(pattern, str(a))
    print(tds)
    # 检查result列表是否为空或者长度不足
    if not result or len(result) < 3:
        parent.after(0,tool.process_input("警告：无法获取必要的参数，跳过处理"))
        return
        
    iid = result[0]
    interFlag = result[1]
    djType = result[2]
    # Call the function with appropriate arguments
    gotoLinks(iid, interFlag, djType, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
              exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent)


def process_sealed(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode,
                   exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh):
    a = tds[11]
    # 打印所有 <td> 元素的内容
    # for i, td in enumerate(tds):
    #     #parent.after(0, tool.process_input(f"td[{i}]: {td.get_text(strip=True)}"))
    #     print(f"td[{i}]:{td}")
    #print(a)
    parent.after(0,tool.process_input(tds[3].get_text() + '总数:' + a.get_text()))
    pattern = r"'(.*?)'"
    result = re.findall(pattern, str(a))
    #print(result)
    # 检查result列表是否为空或者长度不足
    if not result or len(result) < 13:
        parent.after(0,tool.process_input("警告：无法获取必要的参数，跳过处理"))
        return
        
    entOpOrgCodeBY = result[0]
    handoverObjectNo = result[1]
    truckingNo = result[2]
    vehicleNo = result[3]
    throwVehicleNo = result[4]
    carriageNo = result[5]
    billNo = result[6]
    createUserCode = result[7]
    status = result[8]
    departId = result[9]
    interFlag = result[10]
    djType = result[11]
    loadTime = result[12]
    # Call the function with appropriate arguments
    gotoLinksGroup(entOpOrgCodeBY, handoverObjectNo, truckingNo, vehicleNo, throwVehicleNo, carriageNo, billNo,
                   createUserCode, status, departId, interFlag, djType, loadTime, start_date, end_date,
                   selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode, exitShopCode,
                   exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,hbh,)




def process_unentered(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                      exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh):
    a = tds[11]
    parent.after(0,tool.process_input(tds[3].get_text() + '总数:' + a.get_text()))
    pattern = r"'(.*?)'"
    result = re.findall(pattern, str(a))
    print(tds)
    # 检查result列表是否为空或者长度不足
    if not result or len(result) < 9:
        parent.after(0,tool.process_input("警告：无法获取必要的参数，跳过处理"))
        return
        
    handoverObjectNoGroup = result[0]
    truckingNoGroup = result[1]
    vehicleNoGroup = result[2]
    throwVehicleNoGroup = result[3]
    carriageNoGroup = result[4]
    billNoGroup = result[5]
    interFlag = result[6]
    djType = result[7]
    areaOrOrgTypeData = result[8]
    # Call the function with appropriate arguments
    gotoLinksGroupJinWjj(handoverObjectNoGroup, truckingNoGroup, vehicleNoGroup, throwVehicleNoGroup, carriageNoGroup,
                         billNoGroup, interFlag, djType, areaOrOrgTypeData, start_date, end_date, selected_routeLevel,
                         selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode, exitShopCode, exitShopName,entOpOrgName, entOpOrgCode,
                         entShopCode, entShopName,hbh)


def process_unloaded(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName,
                     exitOpOrgCode, exitShopCode, exitShopName,entOpOrgName, entOpOrgCode, entShopCode, entShopName,parent,hbh):
    a = tds[10]
    parent.after(0,tool.process_input(tds[3].get_text() + '总数:' + a.get_text()))
    pattern = r"'(.*?)'"
    result = re.findall(pattern, str(a))
    print(tds)
    # 检查result列表是否为空或者长度不足
    if not result or len(result) < 4:
        parent.after(0,tool.process_input("警告：无法获取必要的参数，跳过处理"))
        return
        
    arriveId = result[0]
    interFlag = result[1]
    djType = result[2]
    areaOrOrgTypeData = result[3]
    # Call the function with appropriate arguments
    gotoLinksGroupJin(arriveId, interFlag, djType, areaOrOrgTypeData, start_date, end_date, selected_routeLevel,
                      selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode, exitShopCode, exitShopName, entOpOrgName,
                      entOpOrgCode, entShopCode, entShopName,hbh)


def process_other(tds, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx, exitOpOrgName, exitOpOrgCode,
                  exitShopCode, exitShopName, entOpOrgName,entOpOrgCode, entShopCode, entShopName,parent,hbh):
    a = tds[11]
    if jjdx != '航空转运':
        parent.after(0,tool.process_input(tds[3].get_text() + '总数:' + a.get_text()))
        pattern = r"'(.*?)'"
        result = re.findall(pattern, str(a))
        
        # 检查result列表是否为空或者长度不足
        if not result or len(result) < 3:
            parent.after(0,tool.process_input("警告：无法获取必要的参数，跳过处理"))
            return
            
        iid = result[0]
        interFlag = result[1]
        djType = result[2]
        # Call the function with appropriate arguments
        gotoLinks(iid, interFlag, djType, start_date, end_date, selected_routeLevel, selected_statusJ, jjdx,
                  exitOpOrgName,
                  exitOpOrgCode,
                  exitShopCode,
                  exitShopName,
                  entOpOrgName,
                  entOpOrgCode,
                  entShopCode,
                  entShopName,parent,hbh)


def gotoLinksGroup(entOpOrgCodeBY,handoverObjectNo,truckingNo,vehicleNo,throwVehicleNo,carriageNo,billNo,createUserCode,status, departId,interFlag,djType,loadTime,start_date,end_date
                                     ,selected_routeLevel,selected_statusJ,jjdx, exitOpOrgName,
                            exitOpOrgCode ,
                            exitShopCode ,
                            exitShopName,
                            entOpOrgName,
                            entOpOrgCode,
                            entShopCode,
                            entShopName,hbh):
    requests.packages.urllib3.disable_warnings()
    start_date = urllib.parse.quote(start_date, safe=":")
    end_date = urllib.parse.quote(end_date, safe=":")

    url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/detailListGroup?departIdGroup=" + \
          departId+"&interFlag="+ interFlag+ "&gmtCreatedBegin=" + start_date+"&gmtCreatedEnd="+end_date+\
          "&handoverObjectName=&billName=&vehicleNo="+vehicleNo+"&routeLevel="+selected_routeLevel+\
          "&djType="+djType+"&status="+status+"&handoverObjectNoGroup="+handoverObjectNo+"&truckingNoGroup="+truckingNo+\
          "&vehicleNoGroup="+vehicleNo+"&throwVehicleNoGroup="+throwVehicleNo+"&carriageNoGroup="+carriageNo+\
          "&createUserCodeGroup="+createUserCode+"&statusGroup="+selected_statusJ+"&billNoGroup="+billNo+\
          "&loadTime="+loadTime+"&entShopCode="+entShopCode+"&entOpOrgCode="+entOpOrgCode+"&exitShopCode="+exitShopCode+\
          "&exitOpOrgCode="+exitOpOrgCode+"&entShopName="+entShopName+"&entOpOrgName="+entOpOrgName+\
          "&exitShopName="+exitShopName+"&exitOpOrgName="+exitOpOrgName+"&entOpOrgCodeBY="+entOpOrgCodeBY+"&truckingNo="+truckingNo+"&billNo="
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list',
        'Sec-Ch-Ua': '"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    print(url)
    response = session.get(url, verify=False)

    html = response.text
    # print(html)
    # 创建一个 BeautifulSoup 对象，并找到表单元素
    soup = BeautifulSoup(html, 'html.parser')
    form = soup.find('form', id='openPositionSearchform')
    if form is None:
        # 处理未找到表单元素的情况
        print(form)
    else:
        # 构造表单数据
        data = {}
        for input_field in form.find_all('input', attrs={'name': True}):
            name = input_field['name']
            value = input_field['value']
            data[name] = value

        # 发送 POST 请求
        url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/getDetailData"
        response = session.post(url, headers=headers, data=data, verify=False)
        print(response.text)
        jsonObj = json.loads(response.text)
        for line in jsonObj:
            dataOutput["交接对象"].append(jjdx)
            dataOutput["航班号"].append(hbh)
            dataOutput["号码"].append(line.get('waybillNo', ''))
            dataOutput["封发局"].append(line.get('originOrgName', ''))
            dataOutput["寄达局"].append(line.get('destinationOrgName', ''))
            dataOutput["总包种类"].append(line.get('mailbagClassName', ''))
            dataOutput["数量"].append(line.get('num', ''))
            dataOutput["重量(kg)"].append("{:.2f}".format(round(float(line.get('weight', '0')), 2)))
            dataOutput["状态"].append(line.get('status', ''))
            dataOutput["配发时间"].append(line.get('gmtCreatedStr2', ''))
            dataOutput["操作员"].append(line.get('createUserName', ''))
            dataOutput["数据来源"].append(line.get('dataSource', ''))
    return dataOutput
# 循环结束后释放session资源
    #session.close()

#导出excel
def gotoLinksGroupJinWjj(handoverObjectNo,truckingNo,vehicleNo,throwVehicleNo,carriageNo,billNo,interFlag,djType,areaOrOrgTypeData,start_date,end_date
                                     ,selected_routeLevel,selected_statusJ,jjdx, exitOpOrgName,
                            exitOpOrgCode ,
                            exitShopCode ,
                            exitShopName,
                            entOpOrgName,
                            entOpOrgCode,
                            entShopCode,
                            entShopName,hbh):
    requests.packages.urllib3.disable_warnings()
    start_date=urllib.parse.quote(start_date,safe=":")
    end_date = urllib.parse.quote(end_date,safe=":")

    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/detailListGroup?handoverObjectNoGroup='+handoverObjectNo+'&' \
          'truckingNoGroup='+truckingNo+'&vehicleNoGroup='+vehicleNo+'&throwVehicleNoGroup='+throwVehicleNo+'&carriageNoGroup='+carriageNo+'&' \
           'billNoGroup='+billNo+'&interFlag='+interFlag+'&gmtCreatedBegin='+start_date+'&gmtCreatedEnd='+end_date+'&handoverObjectName=&billName=&' \
          'vehicleNo=&routeLevel='+selected_routeLevel+'&djType='+djType+'&statusJ='+selected_statusJ+'&areaOrOrgTypeData='+areaOrOrgTypeData+\
          '&entShopCode='+entShopCode+'&entOpOrgCode='+entOpOrgCode+'&exitShopCode='+exitShopCode+'&exitOpOrgCode='+exitOpOrgCode+\
          '&entShopName='+entShopName+'&entOpOrgName='+entOpOrgName+'&exitShopName='+exitShopName+'&exitOpOrgName='+exitOpOrgName
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list',
        'Sec-Ch-Ua':'"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    print(url)
    response = session.get(url,  verify=False)


    html=response.text
    #print(html)
    # 创建一个 BeautifulSoup 对象，并找到表单元素
    soup = BeautifulSoup(html, 'html.parser')
    form = soup.find('form', id='openPositionSearchform')
    if form is None:
    # 处理未找到表单元素的情况
        print(form)
    else:
        # 构造表单数据
        data = {}
        for input_field in form.find_all('input', attrs={'name': True}):
            name = input_field['name']
            value = input_field['value']
            data[name] = value

        # 发送 POST 请求
        url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/getDetailData"
        response = session.post(url, headers=headers, data=data, verify=False)
        print(response.text)
        jsonObj = json.loads(response.text)
        for line in jsonObj:
            dataOutput["交接对象"].append(jjdx)
            dataOutput["航班号"].append(hbh)
            dataOutput["号码"].append(line.get('waybillNo', ''))
            dataOutput["封发局"].append(line.get('originOrgName', ''))
            dataOutput["寄达局"].append(line.get('destinationOrgName', ''))
            dataOutput["总包种类"].append(line.get('mailbagClassName', ''))
            dataOutput["数量"].append(line.get('mailNum', ''))
            dataOutput["重量(kg)"].append("{:.2f}".format(round(float(line.get('weight', '0')), 2)))
            dataOutput["状态"].append(line.get('affirmFlag', ''))
            dataOutput["勾核时间"].append(line.get('affirmTimeStr', ''))
            dataOutput["堆位(拖车)"].append(line.get('containerCode', ''))
            dataOutput["数据来源"].append(line.get('dataSource', ''))
# 循环结束后释放session资源
    #session.close()
    return dataOutput
def gotoLinksGroupJin(arriveId,interFlag,djType,areaOrOrgTypeData,start_date,end_date,selected_routeLevel, selected_statusJ,jjdx, exitOpOrgName,
                            exitOpOrgCode ,
                            exitShopCode ,
                            exitShopName,
                            entOpOrgName,
                            entOpOrgCode,
                            entShopCode,
                            entShopName,hbh):
    requests.packages.urllib3.disable_warnings()
    start_date = urllib.parse.quote(start_date, safe=":")
    end_date = urllib.parse.quote(end_date, safe=":")

    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/detailListGroup?arriveId='+arriveId+'&' \
          'interFlag='+interFlag+'&gmtCreatedBegin='+start_date+'&gmtCreatedEnd='+end_date+'&handoverObjectName=&' \
          'billName=&vehicleNo=&routeLevel='+selected_routeLevel+'&djType='+djType+'&statusJ='+selected_statusJ+'&areaOrOrgTypeData='+areaOrOrgTypeData+\
    '&entShopCode='+entShopCode+'&entOpOrgCode='+entOpOrgCode+'&exitShopCode='+exitShopCode+'&exitOpOrgCode='+exitOpOrgCode+'&entShopName='+entShopName+\
          '&entOpOrgName='+entOpOrgName+'&exitShopName='+exitShopName+'&exitOpOrgName='+exitOpOrgName
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list',
        'Sec-Ch-Ua': '"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    print(url)
    response = session.get(url, verify=False)

    html = response.text
    # print(html)
    # 创建一个 BeautifulSoup 对象，并找到表单元素
    soup = BeautifulSoup(html, 'html.parser')
    form = soup.find('form', id='openPositionSearchform')
    if form is None:
        # 处理未找到表单元素的情况
        print(form)
    else:
        # 构造表单数据
        data = {}
        for input_field in form.find_all('input', attrs={'name': True}):
            name = input_field['name']
            value = input_field['value']
            data[name] = value

        # 发送 POST 请求
        url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/getDetailData"
        response = session.post(url, headers=headers, data=data, verify=False)
        print(response.text)
        jsonObj = json.loads(response.text)
        for line in jsonObj:
            dataOutput["交接对象"].append(jjdx)
            dataOutput["航班号"].append(hbh)
            dataOutput["号码"].append(line.get('waybillNo', ''))
            dataOutput["封发局"].append(line.get('originOrgName', ''))
            dataOutput["寄达局"].append(line.get('destinationOrgName', ''))
            dataOutput["总包种类"].append(line.get('mailbagClassName', ''))
            dataOutput["数量"].append(line.get('mailNum', ''))
            dataOutput["重量(kg)"].append("{:.2f}".format(round(float(line.get('weight', '0')), 2)))
            dataOutput["状态"].append(line.get('affirmFlag', ''))
            dataOutput["勾核时间"].append(line.get('affirmTimeStr', ''))
            dataOutput["堆位(拖车)"].append(line.get('containerCode', ''))
            dataOutput["数据来源"].append(line.get('dataSource', ''))
# 循环结束后释放session资源
    #session.close()
    return dataOutput
def gotoLinks(id,interFlag,djType,start_date,end_date,selected_routeLevel,selected_statusJ,jjdx, exitOpOrgName,
                            exitOpOrgCode ,
                            exitShopCode ,
                            exitShopName,
                            entOpOrgName,
                            entOpOrgCode,
                            entShopCode,
                            entShopName,parent,hbh):
    requests.packages.urllib3.disable_warnings()
    start_date = urllib.parse.quote(start_date,safe=":")
    end_date = urllib.parse.quote(end_date,safe=":")

    url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/detailList?id='+id+'&interFlag='+interFlag+'&' \
          'gmtCreatedBegin='+start_date+'&gmtCreatedEnd='+end_date+'&handoverObjectName='+ \
          '&billName=&vehicleNo=&routeLevel='+selected_routeLevel+'&djType='+djType+'&status='+selected_statusJ+'&statusJ='+selected_statusJ+'&' \
          '&entShopCode='+entShopCode+'&entOpOrgCode='+entOpOrgCode+'&exitShopCode='+exitShopCode+'&exitOpOrgCode='+exitOpOrgCode+'&entShopName='+entShopName+\
          '&entOpOrgName='+entOpOrgName+'&exitShopName='+exitShopName+'&exitOpOrgName='+exitOpOrgName+'&truckingNo=&billNo='


    #url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/detailListGroup'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/list',
        'Sec-Ch-Ua':'"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    # form_data = urllib.parse.urlencode(data)
    # print(form_data)
    # url=url+'?'+form_data
    #print(url)
    response = session.get(url,  verify=False)
    html=response.text
    #print(html)
    # 创建一个 BeautifulSoup 对象，并找到表单元素
    soup = BeautifulSoup(html, 'html.parser')
    form = soup.find('form', id='openPositionSearchform')
    if form is None:
    # 处理未找到表单元素的情况
        print(form)
    else:
        # 构造表单数据
        data = {}
        for input_field in form.find_all('input', attrs={'name': True}):
            name = input_field['name']
            value = input_field['value']
            data[name] = value

        # 发送 POST 请求
        url = "https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/getDetailData"
        response = session.post(url, headers=headers, data=data, verify=False)
        #print(response.text)
        if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in response.text:
            parent.after(0,tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员'))
            parent.after(0,tool.process_input('导出异常'))
        else:
            jsonObj = json.loads(response.text)
            if interFlag=='1':
                for line in jsonObj:
                    dataOutput["交接对象"].append(jjdx)

                    dataOutput["航班号"].append(hbh)
                    dataOutput["号码"].append(line.get('waybillNo', ''))
                    dataOutput["封发局"].append(line.get('originOrgName', ''))
                    dataOutput["寄达局"].append(line.get('destinationOrgName', ''))
                    dataOutput["总包种类"].append(line.get('mailbagClassName', ''))
                    dataOutput["数量"].append(line.get('num', ''))
                    dataOutput["重量(kg)"].append("{:.2f}".format(round(float(line.get('weight', '0')), 2)))
                    dataOutput["状态"].append(line.get('status', ''))
                    dataOutput["配发时间"].append(line.get('gmtCreatedStr2', ''))
                    dataOutput["操作员"].append(line.get('createUserName', ''))
                    dataOutput["数据来源"].append(line.get('dataSource', ''))
            else:
                if selected.get() == '解车超24h':

                    def process_line(line):
                        waybill_no = line.get('waybillNo', '')
                        if overtime(waybill_no):
                            return {
                                "jjdx": jjdx,
                                "航班号": hbh,
                                "号码": waybill_no,
                                "封发局": line.get('originOrgName', ''),
                                "寄达局": line.get('destinationOrgName', ''),
                                "总包种类": line.get('mailbagClassName', ''),
                                "数量": line.get('mailNum', ''),
                                "重量(kg)": "{:.2f}".format(round(float(line.get('weight', '0')), 2)),
                                "状态": line.get('affirmFlag', ''),
                                "勾核时间": line.get('affirmTimeStr', ''),
                                "堆位(拖车)": line.get('containerCode', ''),
                                "数据来源": line.get('dataSource', '')
                            }
                        return None


                    with concurrent.futures.ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
                        results = list(executor.map(process_line, jsonObj))

                    # 过滤掉 None 的结果，并将结果添加到 dataOutput
                    for result in results:
                        if result:
                            dataOutput["交接对象"].append(result["jjdx"])
                            dataOutput["航班号"].append(result["航班号"])
                            dataOutput["号码"].append(result["号码"])
                            dataOutput["封发局"].append(result["封发局"])
                            dataOutput["寄达局"].append(result["寄达局"])
                            dataOutput["总包种类"].append(result["总包种类"])
                            dataOutput["数量"].append(result["数量"])
                            dataOutput["重量(kg)"].append(result["重量(kg)"])
                            dataOutput["状态"].append(result["状态"])
                            dataOutput["勾核时间"].append(result["勾核时间"])
                            dataOutput["堆位(拖车)"].append(result["堆位(拖车)"])
                            dataOutput["数据来源"].append(result["数据来源"])
                else:
                    for line in jsonObj:
                        #print(line)
                        dataOutput["交接对象"].append(jjdx)
                        dataOutput["航班号"].append(hbh)
                        dataOutput["号码"].append(line.get('waybillNo', ''))
                        dataOutput["封发局"].append(line.get('originOrgName', ''))
                        dataOutput["寄达局"].append(line.get('destinationOrgName', ''))
                        dataOutput["总包种类"].append(line.get('mailbagClassName', ''))
                        dataOutput["数量"].append(line.get('mailNum', ''))
                        dataOutput["重量(kg)"].append("{:.2f}".format(round(float(line.get('weight', '0')), 2)))
                        dataOutput["状态"].append(line.get('affirmFlag', ''))
                        dataOutput["勾核时间"].append(line.get('affirmTimeStr', ''))
                        dataOutput["堆位(拖车)"].append(line.get('containerCode', ''))
                        dataOutput["数据来源"].append(line.get('dataSource', ''))

# 循环结束后释放session资源
    #session.close()
    return dataOutput


def overtime(mailno):
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid2 + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_no': mailno,
        'numType': 15,
        'limit': 20

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session2.post(url, headers=headers, params=data, verify=False)


    r = response.text
    #print(r)
    # 初始化一个变量来保存符合条件的时间
    matched_time = None
    jsonObj = json.loads(r)
    for index, line in enumerate(jsonObj):
        if '处理中心解车' in line["opName"] and '51000061' in line["opOrgCode"]:
            # 将字符串时间转换为datetime对象
            matched_time = datetime.datetime.strptime(line["opTime"], '%Y-%m-%d %H:%M:%S')

            # 如果之前找到了符合条件的记录，并且当前不是最后一个元素
            if matched_time is not None and index < len(jsonObj) - 1:
                # 获取后一条数据
                prev_line = jsonObj[index + 1]

                # 将字符串时间转换为datetime对象
                prev_op_time = datetime.datetime.strptime(prev_line["opTime"], '%Y-%m-%d %H:%M:%S')
                # 计算两个时间之间的差值，转换为小时
                time_difference_hours = (matched_time - prev_op_time).seconds / 3600

                # 如果时间差超过24小时，则返回True
                if time_difference_hours > 24:
                    return True
            elif index == len(jsonObj) - 1:
                return True
    # 如果循环结束都没有满足条件的情况，根据实际情况决定是否需要返回False或者其他操作
    return False
def date_range(start_date, end_date):
    start = datetime.datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.datetime.strptime(end_date, '%Y-%m-%d')
    delta = datetime.timedelta(days=1)
    current_date = start
    while current_date <= end:
        yield current_date.strftime('%Y-%m-%d')
        current_date += delta

 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def run(title,parent):
    try:
        global username, password, session, jdptid,session2, jdptid2, L,dataOutput
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")

        # 构造Session
        session = requests.Session()
        session2 = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            session2.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")

        # 保存账号和密码
        tool.save_data()

        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()


        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')

        tool.process_input('路单开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/pcs-tc-web/a/pcs/queryandprint/waybillquerymaint/tolist'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        if selected.get() == '解车超24h':

            tool.process_input('轨迹开始登录新一代')
            tool.process_input('第1次尝试登录')
            url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
            result = tool.getck(username, password, session2, url)
            jdptid2 = result[0]
            userName = result[1]

            while (jdptid2 == '0'):
                # print ('第'+str(i)+'次尝试登录')
                tool.process_input('第' + str(i) + '次尝试登录')
                result = tool.getck(username, password, session2, url)
                jdptid2 = result[0]
                userName = result[1]
                i += 1
                if i > 5:
                    tool.process_input('超10次登录失败,请检查账号密码正确后重试')
                    break
        tool.postlog(username, userName, title, ip_address)
        #routeLevel()
        tool.process_input('当前线程:' + threads_combobox.get())
        #pool = Pool(int(threads_combobox.get()))

        if interFlag_combobox.get() == '出口':

            if selected.get()=='默认':
                merged_data= {
                        "类型": [],
                        "交接对象": [],
                        "派车单号": [],
                        "航班/车牌号": [],
                        "车厢/车牌号": [],
                        "车厢码": [],
                        "路单流水号": [],
                        "邮件总数": [],
                        "总数": [],
                        "重量(kg)": [],
                        "进口机构": [],
                        "已勾核数量": [],
                        "未勾核数量": [],
                        "操作员": [],
                        "封车时间": [],
                        "状态": [],
                        "装车时间": [],
                        "装车完毕时间": [],
                        "装车时长": [],
                        "进局时间": [],
                        "解车时间": []
                    }

                start_date = str(start_date_entry.get_date())
                end_date = str(end_date_entry.get_date())
                dates = list(date_range(start_date, end_date))
                tool.process_input('查询类型:' + interFlag_combobox.get())
                tool.process_input('邮路级别:' + routeLevel_combobox.get())
                tool.process_input('状态:' + statusJ_combobox.get())
                tool.process_input('车间:' + workshop_combobox.get())
                tool.process_input('交接对象:' + input_textbox.get())
                for date in dates:
                    results=getckld(date)
                    for key, value in results.items():
                        merged_data[key].extend(value)
            else:

                dataOutput = {
                    "交接对象": [],
                    "航班号": [],
                    "号码": [],
                    "封发局": [],
                    "寄达局": [],
                    "总包种类": [],
                    "数量": [],
                    "重量(kg)": [],
                    "状态": [],
                    "配发时间": [],
                    "操作员": [],
                    "数据来源": []

                }

                merged_data=getmail(parent)



            tool.process_input('爬取完毕')
            tool.process_input('正在写入Excel')
            # 定义当前时间
            currentTime = datetime.datetime.now()

            dataForm = pandas.DataFrame(merged_data)
            row = 1048570
            length = len(dataForm)
            number = length // row
            for i in range(number + 1):
                dataForm[i * row:(i + 1) * row].to_excel(interFlag_combobox.get()+routeLevel_combobox.get()+statusJ_combobox.get()+"路单查询-" +
                                                         currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                         index=False)

            tool.process_input("写入完成共" + str(number + 1) + "个文件")


            end = time.perf_counter()
            runTime = end - start
            # 计算时分秒
            hour = runTime // 3600
            minute = (runTime - 3600 * hour) // 60
            second = runTime - 3600 * hour - 60 * minute
            # 输出
            # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
            tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
            # 程序未在运行中，启用按钮
            submit_button.configure(state="normal")
            back_button.configure(state="normal")

        else:
            if selected.get() == '默认':
                if not os.path.exists(output_folder):  # 检查文件夹是否存在，如果不存在则创建
                    os.makedirs(output_folder)
                start_date = str(start_date_entry.get_date())
                end_date = str(end_date_entry.get_date())
                dates = list(date_range(start_date, end_date))
                tool.process_input('查询类型:' + interFlag_combobox.get())
                tool.process_input('邮路级别:' + routeLevel_combobox.get())
                tool.process_input('状态:' + statusJ_combobox.get())
                tool.process_input('车间:' + workshop_combobox.get())
                tool.process_input('交接对象:' + input_textbox.get())
                for date in dates:
                    getjkld(date)
                tool.process_input('爬取完毕')
                tool.process_input('写入Excel完毕')
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
            else:
                dataOutput = {
                    "交接对象": [],
                    "航班号": [],
                    "号码": [],
                    "封发局": [],
                    "寄达局": [],
                    "总包种类": [],
                    "数量": [],
                    "重量(kg)": [],
                    "状态": [],
                    "勾核时间": [],
                    "堆位(拖车)": [],
                    "数据来源": []

                }
                merged_data = getmail(parent)

                tool.process_input('爬取完毕')
                tool.process_input('正在写入Excel')
                # 定义当前时间
                currentTime = datetime.datetime.now()

                dataForm = pandas.DataFrame(merged_data)
                row = 1048570
                length = len(dataForm)
                number = length // row
                for i in range(number + 1):
                    dataForm[i * row:(i + 1) * row].to_excel(
                        interFlag_combobox.get() + routeLevel_combobox.get() + statusJ_combobox.get() + "路单查询-" +
                        currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                        index=False)

                tool.process_input("写入完成共" + str(number + 1) + "个文件")

                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")

    except Exception as e:
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")

# 回调函数修改状态下拉框的选项列表
def on_interFlag_change():
    global statusJ_options
    selected_interFlag = interFlag_combobox.get()
    if selected_interFlag == "出口":
        statusJ_options = {
            "未封车": "0",
            "已封车": "1",
            "预封车": "2"
        }
        statusJ_combobox.configure(values=list(statusJ_options.keys()))
        statusJ_combobox.set("未封车")
    else:
        statusJ_options = {
            "未进局": "3",
            "进局未解车": "4",
            "已解车": "5"
        }
        statusJ_combobox.configure(values=list(statusJ_options.keys()))
        statusJ_combobox.set("未进局")

def on_organization_change():
    global workshop_options
    selected_organization = organization_combobox.get()
    print(selected_organization)
    if selected_organization == "国际":
        workshop_options = {
            "默认车间": "SD51040034",
            "函件车间": "SD51043407",
            "国际进出": "SD51043408",
            "专封车间": "SD51043404",
            "海运车间": "SD51043406",
            "佛山前置": "SD5104003402",
            "清运车间": "SD51043405",
            "香港渠道": "SD5104003403",
            "石井前置": "SD5104003401",
            "重货车间": "SD51045100",
            "YD客户车间": "SD51043410",
            "ZH客户车间": "SD51043409",

        }
        workshop_combobox.configure(values=list(workshop_options.keys()))
        workshop_combobox.set("默认车间")
    else:
        workshop_options = {
            "国际作业": "SD51000190",
            "华南集散": "SD51001111",
            "默认车间": "SD51000061",
            "进口作业": "SD51000400",
            "航空转运": "SD51000003",
            "槎头": "SD51000192",
            "空侧转运中心": "SD51000815",
            "录取通知书专用车间": "SD51000166",
            "苹果专用车间": "SD51000920",

        }
        workshop_combobox.configure(values=list(workshop_options.keys()))
        workshop_combobox.set("航空转运")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session,session2, jdptid,jdptid2, L,  input_textbox, output_textbox, root, submit_button, button_clear, \
        start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,interFlag_options,interFlag_combobox,\
    routeLevel_options, routeLevel_combobox,statusJ_options,statusJ_combobox,dataOutput,starthour_var,startminute_var,endhour_var,endminute_var,\
    starthour_combobox,startminute_combobox,endhour_combobox,endminute_combobox,tool,back_button,organization_combobox,startsecond_var,startsecond_combobox,endsecond_var,endsecond_combobox, \
    workshop_combobox, workshop_options,selected



    # 构造Session
    session = requests.Session()
    session2 = requests.Session()
    # 创建主窗口
    #root = tk.Tk()

    today = datetime.datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["国际", "国内"])

    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)


    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=1, column=0)

    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=0, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(date_container, maxdate=today.date())
    start_date_entry.grid(row=0, column=1, padx=10, pady=10)
    # start_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=0, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    starthours = [str(h).zfill(2) for h in range(24)]
    startminutes = [str(m).zfill(2) for m in range(60)]
    startseconds = [str(s).zfill(2) for s in range(60)]

    starthour_var = StringVar()
    startminute_var = StringVar()
    startsecond_var = StringVar()

    starthour_combobox = ttk.Combobox(date_container, textvariable=starthour_var, values=starthours, width=5)
    starthour_combobox.set(starthours[0])
    starthour_combobox.grid(row=0, column=3, padx=5, pady=5)

    startminute_combobox = ttk.Combobox(date_container, textvariable=startminute_var, values=startminutes, width=5)
    startminute_combobox.set(startminutes[0])
    startminute_combobox.grid(row=0, column=4, padx=5, pady=5)
    # startminute_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())

    startsecond_combobox = ttk.Combobox(date_container, textvariable=startsecond_var, values=startseconds, width=5)
    startsecond_combobox.set(startseconds[0])
    startsecond_combobox.grid(row=0, column=5, padx=5, pady=5)

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=1, column=0, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(date_container, maxdate=today.date())
    end_date_entry.grid(row=1, column=1, padx=10, pady=10)
    # end_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=1, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    endhours = [str(h).zfill(2) for h in range(24)]
    endminutes = [str(m).zfill(2) for m in range(60)]
    endseconds = [str(s).zfill(2) for s in range(60)]

    endhour_var = StringVar()
    endminute_var = StringVar()
    endsecond_var = StringVar()

    endhour_combobox = ttk.Combobox(date_container, textvariable=endhour_var, values=endhours, width=5)
    endhour_combobox.set(endhours[23])
    endhour_combobox.grid(row=1, column=3, padx=5, pady=5)

    endminute_combobox = ttk.Combobox(date_container, textvariable=endminute_var, values=endminutes, width=5)
    endminute_combobox.set(endminutes[59])
    endminute_combobox.grid(row=1, column=4, padx=5, pady=5)

    endsecond_combobox = ttk.Combobox(date_container, textvariable=endsecond_var, values=endseconds, width=5)
    endsecond_combobox.set(endseconds[59])
    endsecond_combobox.grid(row=1, column=5, padx=5, pady=5)


    # 添加查询类型下拉框
    interFlag_label = ttk.Label(date_container, text="查询类型:")
    interFlag_label.grid(row=2, column=0, padx=5, pady=10)

    interFlag_options = {
        "出口": "1",
        "进口": "0"
    }
    interFlag_combobox = ttk.Combobox(date_container, values=list(interFlag_options.keys()))
    interFlag_combobox.grid(row=2, column=1, padx=5, pady=10)
    interFlag_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())
    interFlag_combobox.set("出口")

    # 添加邮路级别下拉框
    routeLevel_label = ttk.Label(date_container, text="邮路级别:")
    routeLevel_label.grid(row=2, column=2, padx=5, pady=10)

    routeLevel_options = {
        "全部": "",
        "一干": "1",
        "二干": "2",
        "邮区": "3"

    }
    routeLevel_combobox = ttk.Combobox(date_container, values=list(routeLevel_options.keys()))
    routeLevel_combobox.grid(row=2, column=3, padx=5, pady=10)
    routeLevel_combobox.set("全部")

    # 添加状态下拉框
    statusJ_label = ttk.Label(date_container, text="状态:")
    statusJ_label.grid(row=3, column=0, padx=5, pady=10)

    statusJ_options = {
        "未封车": "0",
        "已封车": "1",
        "预封车": "2"
    }
    statusJ_combobox = ttk.Combobox(date_container, values=list(statusJ_options.keys()))
    statusJ_combobox.grid(row=3, column=1, padx=5, pady=10)
    statusJ_combobox.set("未封车")

    # 添加车间下拉框
    workshop_label = ttk.Label(date_container, text="车间:")
    workshop_label.grid(row=3, column=2, padx=5, pady=10)
    workshop_options = {
        "默认车间": "SD51040034",
        "函件车间": "SD51043407",
        "国际进出": "SD51043408",
        "专封车间": "SD51043404",
        "海运车间": "SD51043406",
        "佛山前置": "SD5104003402",
        "清运车间": "SD51043405",
        "香港渠道": "SD5104003403",
        "石井前置": "SD5104003401",
        "重货车间": "SD51045100",
        "YD客户车间": "SD51043410",
        "ZH客户车间": "SD51043409",

    }
    workshop_combobox = ttk.Combobox(date_container, values=list(workshop_options.keys()))
    workshop_combobox.grid(row=3, column=3, padx=5, pady=10)
    workshop_combobox.set("默认车间")

    input_label = ttk.Label(date_container, text="交接对象:")
    input_label.grid(row=4, column=0, padx=10, pady=10)

    # input_label.pack()
    input_textbox = tk.Entry(date_container, width=30)
    input_textbox.grid(row=4, column=1, padx=10, pady=10, columnspan=1)

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=2, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='默认')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=1, column=0, padx=5, pady=5)
    radio_button1 = ttk.Radiobutton(input_label_container, text="默认", value="默认", variable=selected)
    radio_button1.grid(row=1, column=1)

    radio_button2 = ttk.Radiobutton(input_label_container, text="下钻邮件", value="下钻邮件", variable=selected)
    radio_button2.grid(row=1, column=2)

    radio_button3 = ttk.Radiobutton(input_label_container, text="解车超24h", value="解车超24h", variable=selected)
    radio_button3.grid(row=1, column=3)
    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()
    organization_combobox.bind("<<ComboboxSelected>>", lambda _: on_organization_change(),lambda _:tool.read_data())
    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()