import urllib.request
import os
import sys
import tkinter as tk
import subprocess
from tkinter import ttk
from urllib.parse import quote

# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

def reporthook(block_num, block_size, total_size, progressbar):
    """
    A callback function to show the download progress in Tkinter window.
    """
    downloaded_size = block_num * block_size
    progress = min(1.0, downloaded_size / total_size)
    percent = progress * 100
    progressbar["value"] = percent
    progressbar.update()

def update_program():
    # 关闭原程序
    current_exe_path = os.path.join(current_dir, '新一代神器.exe')
    #current_exe_path = '新一代神器.exe'
    #os.system(f'taskkill /f /im {current_exe_path}')
    command = f'C:\\Windows\\System32\\taskkill.exe /f /im {current_exe_path}'
    os.system(command)
    # 下载新版本 exe 文件
    #download_url = 'http://************:42300/新一代神器.exe'
    # 下载URL中的非ASCII字符进行编码
    download_url = 'http://************:42300/' + quote('新一代神器.exe')
    #new_version_path = os.path.join(os.path.dirname(__file__), 'new_version.exe')
    new_version_path = 'new_version.exe'
    # 发起请求
    with urllib.request.urlopen(download_url) as response:
        # 获取文件总大小
        total_size = int(response.info().get('Content-Length').strip())
        # 初始化已下载大小
        downloaded_size = 0

        # 创建 Tkinter 窗口
        progress_window = tk.Tk()
        progress_window.title("下载进度")
        progressbar = ttk.Progressbar(progress_window, orient="horizontal", length=300, mode="determinate")
        progressbar.pack(pady=20)

        # 循环下载文件并写入本地
        with open(new_version_path, 'wb') as new_version_file:
            while True:
                chunk = response.read(1024 * 1024)  # 每次下载 1MB
                if not chunk:
                    break

                new_version_file.write(chunk)
                downloaded_size += len(chunk)

                # 更新下载进度
                reporthook(downloaded_size // 1024, 1024, total_size, progressbar)

        print("\nDownload complete!")

    # 替换主程序

    if os.path.exists(current_exe_path):
        os.remove(current_exe_path)

    os.rename(new_version_path, current_exe_path)

    # 启动主程序
    os.execv(current_exe_path, sys.argv)

if __name__ == "__main__":
    # 执行更新
    update_program()
