<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=us-ascii" />
<meta http-equiv="Content-Style-Type" content="text/css" />
<meta http-equiv="Content-Script-Type" content="text/javascript" />
<title>Table of Contents</title>
<meta name="generator" content="Oracle DARB XHTML Converter (Mode = browser help) - Version 5.1.2 Build 708" />
<meta name="date" content="2013-03-15T11:44:59Z" />
<meta name="robots" content="noarchive" />
<meta name="doctitle" content="Table of Contents" />
<meta name="relnum" content="" />
<meta name="partnum" content="" />
<link rel="copyright" href="cpyr.htm" title="Copyright" type="text/html" />
<link rel="stylesheet" href="blafdoc.css" title="Oracle BLAFDoc" type="text/css" />
<link rel="contents" href="toc.htm" title="Contents" type="text/html" />
<link rel="next" href="sqora.htm" title="Next" type="text/html" />
</head>
<body>
<div class="header"><a id="top" name="top"></a>
<div class="zz-skip-header"><a href="#BEGIN">Skip Headers</a></div>
<table class="simple oac_no_warn" summary="" cellspacing="0" cellpadding="0" width="100%">
<tr>
<td align="left" valign="top"><br />
<br /></td>
<td valign="bottom" align="right">
<table class="simple oac_no_warn" summary="" cellspacing="0" cellpadding="0">
<tr>
<td>&nbsp;</td>
</tr>
</table>
</td>
</tr>
</table>
<hr />
<table class="simple oac_no_warn" summary="" cellspacing="0" cellpadding="0" width="100">
<tr>
<td align="center"><a href="sqora.htm"><br />
<span class="icon">Next</span></a></td>
<td>&nbsp;</td>
</tr>
</table>
<a name="BEGIN" id="BEGIN"></a></div>
<!-- class="header" -->
<div class="ind"><!-- End Header -->
<script type="text/javascript">
<!-- // <![CDATA[
window.name='sqora'
// ]]> -->
</script> <script type="text/javascript">
// <![CDATA[
function footdisplay(footnum,footnote) {
    var msg = window.open('', 'NewWindow' + footnum,
        'directories=no,height=120,location=no,menubar=no,resizable=yes,' +
        'scrollbars=yes,status=no,toolbar=no,width=598');
    msg.document.open('text/html');
    msg.document.write('<!DOCTYPE html ');
    msg.document.write('PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" ');

    msg.document.write('"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');
    msg.document.write('<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head><title>');
    msg.document.write('Footnote ' + footnum);
    msg.document.write('<\/title><meta http-equiv="Content-Type" ');
    msg.document.write('content="text/html; charset=utf-8" />');
    msg.document.write('<meta http-equiv="Content-Script-Type" ');
    msg.document.write('content="text/javascript" />');
    msg.document.write('<style type="text/css"> <![CDATA[ ');
    msg.document.write('h1 {text-align: center; font-size: 14pt;}');
    msg.document.write('fieldset {border: none;}');
    msg.document.write('form {text-align: center;}');
    msg.document.write(' ]]\u003e <\/style>');
    msg.document.write('<\/head><body><h1>Footnote ' + footnum + '<\/h1><p>');
    msg.document.write(footnote);
    msg.document.write('<\/p><form action="" method="post"><fieldset>');
    msg.document.write('<input type="button" value="OK" ');
    msg.document.write('onclick="window.close();" />');
    msg.document.write('<\/fieldset><\/form><\/body><\/html>');
    msg.document.close();
    msg.focus();
}
// ]]>
</script> <noscript>
<p>The script content on this page is for navigation purposes only and does not alter the content in any way.</p>
</noscript>
<h1 style="text-align: center;" class="chapter">Oracle ODBC Driver Help</h1> <span style="font-weight: bold;">Version 12.1</span>
<h1 class="toc">Contents</h1>
<h2 class="tocheader"><a href="sqora.htm"></a></h2>
<h2 class="tocheader"><a href="sqora.htm#BABGJEHF">Using the Oracle ODBC Driver</a></h2>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABEHFCD">About Oracle ODBC Driver</a></li>
<li><a href="sqora.htm#BABIBGJI">For All Users</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABGEDCB">Oracle ODBC Driver</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABJFIEC">What is the Oracle ODBC Driver</a></li>
<li><a href="sqora.htm#BABCGECE">New and Changed Features</a></li>
<li><a href="sqora.htm#BABBDJAA">Features Not Supported</a></li>
<li><a href="sqora.htm#BABHEGHC">Files Created by the Installation</a></li>
<li><a href="sqora.htm#BABECBDH">Driver Conformance Levels</a></li>
<li><a href="sqora.htm#BABCICCF">Known Limitations</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABGJJAF">Configuration Tasks</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABDFDJB">Configuring Oracle Net Services</a></li>
<li><a href="sqora.htm#BABBCBHH">Configuring the Data Source</a></li>
<li><a href="sqora.htm#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABHABJB">Modifying the oraodbc.ini File</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABEGGIB">Reducing Lock Timeout</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABDBDJG">Connecting to a Data Source</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABDCHIG">Connecting to an Oracle Data Source</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABCHDJE">Troubleshooting</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABCGCCI">Using the Oracle ODBC Driver for the First Time</a></li>
<li><a href="sqora.htm#BABGAIIC">Expired Password</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="sqora.htm#BABGFHBE">For Advanced Users</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABCHHFC">Creating Oracle ODBC Driver TNS Service Names</a></li>
<li><a href="sqora.htm#BABIDECD">SQL Statements</a></li>
<li><a href="sqora.htm#BABCIACJ">Data Types</a></li>
<li><a href="sqora.htm#BABBAEEH">Implementation of Data Types (Advanced)</a></li>
<li><a href="sqora.htm#BABBJIBH">Limitations on Data Types</a></li>
<li><a href="sqora.htm#BABCCDFB">Error Messages</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABICHJC">For Programmers</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABIJAGI">Format of the Connection String</a></li>
<li><a href="sqora.htm#BABIDEDG">SQLDriverConnect Implementation</a></li>
<li><a href="sqora.htm#BABCEBFI">Reducing Lock Timeout in a Program</a></li>
<li><a href="sqora.htm#BABJJDBG">Linking with odbc32.lib (Windows) or libodbc.so (UNIX)</a></li>
<li><a href="sqora.htm#BABCEAIA">Obtaining Information About rowids</a></li>
<li><a href="sqora.htm#BABBBDEJ">Rowids in a WHERE Clause</a></li>
<li><a href="sqora.htm#BABHGBFJ">Enabling Result Sets</a></li>
<li><a href="sqora.htm#BABJCDGE">Enabling EXEC Syntax</a></li>
<li><a href="sqora.htm#BABGJGBA">Enabling Event Notification for Connection Failures in an Oracle RAC Environment</a></li>
<li><a href="sqora.htm#BABDDGBE">Using Implicit Results Feature Through ODBC</a></li>
<li><a href="sqora.htm#BABBCCDD">Supported Functionality</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABBCJCB">API Conformance</a></li>
<li><a href="sqora.htm#BABHEGBH">Implementation of ODBC API Functions</a></li>
<li><a href="sqora.htm#BABIDAGF">Implementation of the ODBC SQL Syntax</a></li>
<li><a href="sqora.htm#BABEJGEG">Implementation of Data Types (Programming)</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABIFIGA">Unicode Support</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABDBAFE">Unicode Support Within the ODBC Environment</a></li>
<li><a href="sqora.htm#BABCICFJ">Unicode Support in ODBC API</a></li>
<li><a href="sqora.htm#BABFEHCB">Unicode Functions in the Driver Manager</a></li>
<li><a href="sqora.htm#BABECFEA">SQLGetData Performance</a></li>
<li><a href="sqora.htm#BABEADIA">Unicode Samples</a></li>
</ul>
</li>
<li><a href="sqora.htm#BABIIAIH">Performance and Tuning</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABBEJFD">General ODBC Programming Tips</a></li>
<li><a href="sqora.htm#BABFHDGC">Data Source Configuration Options</a></li>
<li><a href="sqora.htm#BABDHDBB">DATE and TIMESTAMP Data Types</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="sqora.htm#BABJHHHH">Glossary</a></li>
<li><a href="sqora.htm#BABCEBCD">Acknowledgements</a>
<ul style="list-style-type:none">
<li><a href="sqora.htm#BABIICBI">Copyright and Trademark Acknowledgements</a></li>
<li><a href="sqora.htm#BABIFHIA">Accessibility Statements</a></li>
</ul>
</li>
</ul>
</div>
<!-- class="ind" -->
<!-- Start Footer -->
<div class="footer">
<hr />
<table class="simple oac_no_warn" summary="" cellspacing="0" cellpadding="0" width="100%">
<col width="6%" />
<col width="6%" />
<col width="*" />
<col width="6%" />
<tr>
<td align="center"><a href="sqora.htm"><br />
<span class="icon">Next</span></a></td>
<td align="center"><span class="copyrightlogo">Copyright&nbsp;&copy;&nbsp;1993,&nbsp;2013,&nbsp;Oracle&nbsp;and/or&nbsp;its&nbsp;affiliates.&nbsp;All&nbsp;rights&nbsp;reserved.</span><br />
<a href="cpyr.htm"><span class="copyrightlogo">Legal Notices</span></a></td>
</tr>
</table>
</div>
<!-- class="footer" -->
</body>
</html>
