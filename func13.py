import io
import os
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from urllib.parse import quote

from tool import Tool
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from bs4 import BeautifulSoup
import requests, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading
import socket
from os import path



# 修改全局变量 dataOutput 为线程安全的队列

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'
dataOutput = {
                "邮件号": [],
                "业务类型": [],
                "接收时间": [],
                "入库时间": [],
                "出库时间": [],
                "入库状态": [],
                "短信发送状态": [],
                "海关回执代码": [],
                "仓库名称": [],
                "仓库区域": [],
                "货架号": [],
                "是否逾期": [],
                "是否外事": [],
                "是否分运行李": [],
                "是否发验": [],
                "异常类型": [],
                "面单打印时间": [],
                "面单邮件号": [],
                "姓名": [],
                "手机号码": [],
                "客户状态": [],
                "地址": [],
                "批译地址": [],
                "邮件来源": [],
                "修改人员": [],
                "海关回执信息": [],
                "出库申请时间": [],
                "一催日期": [],
                "二催日期": [],
                "逾期日期": []
            }
# 以下为定义一个字典，以及需要获取的字段名称

def getmail(parent,mailno):
    global L
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {
        'pageNo': 1,
        'pageSize': 50,
        'searchCodes': mailno+',',
        'itemStoreStatus': '',
        'storehouseCode': '',
        'storageAreaCode': '',
        'shelfNo': '',
        'notifyStatus': '',
        'recvstartTime': '',
        'recvendTime': '',
        'inputstartTime': '',
        'inputendTime': '',
        'firstNoticeDateSel': '',
        'workSect': '',
        'sepaBaggage': '',
        'isForeign': '',
        'isVn': '',
        'anomalyType': '',
        'custReceiptCode': '',
        'noticeType': '',
        'ifPhoneValid': '',
        'addresseePhone': '',
        'outWarehoseStartTime': '',
        'outWarehoseEndTime': '',
        'outApplyStartTime': '',
        'outApplyEndTime': '',
        'custReceiptInfo': '',
        'itemClientStatus': '',
        'sendSms': '',
        'itemNo': mailno
    }

    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    # print(r)
    #html=BeautifulSoup(r,'html.parser')
    html = BeautifulSoup(r, 'lxml')
    #print(r)
    input_element = html.find('tbody')
    #print(input_element)
    if input_element:
        tds = input_element.find_all('td')
        dataOutput = {
            "邮件号": [],
            "业务类型": [],
            "接收时间": [],
            "入库时间": [],
            "出库时间": [],
            "入库状态": [],
            "短信发送状态": [],
            "海关回执代码": [],
            "仓库名称": [],
            "仓库区域": [],
            "货架号": [],
            "是否逾期": [],
            "是否外事": [],
            "是否分运行李": [],
            "是否发验": [],
            "异常类型": [],
            "面单打印时间": [],
            "面单邮件号": [],
            "姓名": [],
            "手机号码": [],
            "客户状态": [],
            "地址": [],
            "批译地址": [],
            "邮件来源": [],
            "修改人员": [],
            "海关回执信息": [],
            "出库申请时间": [],
            "一催日期": [],
            "二催日期": [],
            "逾期日期": []
        }
        if len(tds) >= 13:

            dataOutput['邮件号'].append(tds[2].get_text())
            dataOutput['业务类型'].append(tds[3].get_text())
            dataOutput['接收时间'].append(tds[4].get_text())
            dataOutput['入库时间'].append(tds[5].get_text())
            dataOutput['出库时间'].append(tds[6].get_text())
            dataOutput['入库状态'].append(tds[7].get_text())
            dataOutput['短信发送状态'].append(tds[8].get_text())
            dataOutput['海关回执代码'].append(tds[9].get_text())
            dataOutput['仓库名称'].append(tds[10].get_text())
            dataOutput['仓库区域'].append(tds[11].get_text())
            dataOutput['货架号'].append(tds[12].get_text())
            dataOutput['是否逾期'].append(tds[13].get_text())
            dataOutput['是否外事'].append(tds[14].get_text())
            dataOutput['是否分运行李'].append(tds[15].get_text())
            dataOutput['是否发验'].append(tds[16].get_text())
            dataOutput['异常类型'].append(tds[17].get_text())
            dataOutput['面单打印时间'].append(tds[18].get_text())
            dataOutput['面单邮件号'].append(tds[19].get_text())
            dataOutput['姓名'].append(tds[20].get_text())
            dataOutput['手机号码'].append(tds[21].get_text())
            dataOutput['客户状态'].append(tds[22].get_text())
            dataOutput['地址'].append(tds[23].get_text())
            dataOutput['批译地址'].append(tds[24].get_text())
            dataOutput['邮件来源'].append(tds[25].get_text())
            dataOutput['修改人员'].append(tds[26].get_text())
            dataOutput['海关回执信息'].append(tds[27].get_text())
            dataOutput['出库申请时间'].append(tds[28].get_text())
            dataOutput['一催日期'].append(tds[29].get_text())
            dataOutput['二催日期'].append(tds[30].get_text())
            dataOutput['逾期日期'].append(tds[31].get_text())
            L += 1
            if L % 1000 == 0:
                # 添加适当的同步机制，例如使用 threading.Lock

                # 模拟一些耗时操作
                time.sleep(1)
                # 在主线程中调度更新UI
                parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    session.close()
    return dataOutput

def getmail2(parent):
    global L
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {
        'pageNo': 1,
        'pageSize': 10000,
        'searchCodes': '',
        'itemStoreStatus': '',
        'storehouseCode': '',
        'storageAreaCode': '',
        'shelfNo': '',
        'notifyStatus': '',
        'recvstartTime': '',
        'recvendTime': '',
        'inputstartTime': '',
        'inputendTime': '',
        'firstNoticeDateSel': '',
        'workSect': '',
        'sepaBaggage': '',
        'isForeign': '',
        'isVn': '',
        'anomalyType': '',
        'custReceiptCode': '',
        'noticeType': '',
        'ifPhoneValid': '',
        'addresseePhone': '',
        'outWarehoseStartTime': '',
        'outWarehoseEndTime': '',
        'outApplyStartTime': '',
        'outApplyEndTime': '',
        'custReceiptInfo': '',
        'itemClientStatus': '',
        'sendSms': '',
        'itemNo': ''
    }

    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    # print(r)
    #html=BeautifulSoup(r,'html.parser')
    html = BeautifulSoup(r, 'lxml')
    #print(r)
    # 匹配数字部分
    match = re.search(r'共\s*(\d+)页', str(html))

    if match:
        page_number = int(match.group(1))
        print("提取的数字为:", page_number)
    else:
        print(html)
        print("未找到匹配的数字")

    posi = []

    for i in range(1, page_number + 1):
        print('第' + str(i) + '页')
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Length': '336',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'DNT': '1',
            'Host': '**********',
            'Origin': 'https://**********',
            'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
        }
        data = {
            'pageNo': i,
            'pageSize': 10000,
            'searchCodes': '',
            'itemStoreStatus': '',
            'storehouseCode': '',
            'storageAreaCode': '',
            'shelfNo': '',
            'notifyStatus': '',
            'recvstartTime': '',
            'recvendTime': '',
            'inputstartTime': '',
            'inputendTime': '',
            'firstNoticeDateSel': '',
            'workSect': '',
            'sepaBaggage': '',
            'isForeign': '',
            'isVn': '',
            'anomalyType': '',
            'custReceiptCode': '',
            'noticeType': '',
            'ifPhoneValid': '',
            'addresseePhone': '',
            'outWarehoseStartTime': '',
            'outWarehoseEndTime': '',
            'outApplyStartTime': '',
            'outApplyEndTime': '',
            'custReceiptInfo': '',
            'itemClientStatus': '',
            'sendSms': '',
            'itemNo': ''
        }

        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

        response = session.post(url, headers=headers, data=data, verify=False)
        r = response.text
        # print(r)
        # html=BeautifulSoup(r,'html.parser')
        html = BeautifulSoup(r, 'lxml')
        input_element = html.find('tbody')

        if input_element:
            trs = input_element.find_all('tr')

            for tr in trs:
                tds = tr.find_all('td')
                print(*(td.get_text().strip() for td in tds[2:32]), sep=", ")
                dataOutput['邮件号'].append(tds[2].get_text().strip())
                dataOutput['业务类型'].append(tds[3].get_text().strip())
                dataOutput['接收时间'].append(tds[4].get_text().strip())
                dataOutput['入库时间'].append(tds[5].get_text().strip())
                dataOutput['出库时间'].append(tds[6].get_text().strip())
                dataOutput['入库状态'].append(tds[7].get_text().strip())
                dataOutput['短信发送状态'].append(tds[8].get_text().strip())
                dataOutput['海关回执代码'].append(tds[9].get_text().strip())
                dataOutput['仓库名称'].append(tds[10].get_text().strip())
                dataOutput['仓库区域'].append(tds[11].get_text().strip())
                dataOutput['货架号'].append(tds[12].get_text().strip())
                dataOutput['是否逾期'].append(tds[13].get_text().strip())
                dataOutput['是否外事'].append(tds[14].get_text().strip())
                dataOutput['是否分运行李'].append(tds[15].get_text().strip())
                dataOutput['是否发验'].append(tds[16].get_text().strip())
                dataOutput['异常类型'].append(tds[17].get_text().strip())
                dataOutput['面单打印时间'].append(tds[18].get_text().strip())
                dataOutput['面单邮件号'].append(tds[19].get_text().strip())
                dataOutput['姓名'].append(tds[20].get_text().strip())
                dataOutput['手机号码'].append(tds[21].get_text().strip())
                dataOutput['客户状态'].append(tds[22].get_text().strip())
                dataOutput['地址'].append(tds[23].get_text().strip())
                dataOutput['批译地址'].append(tds[24].get_text().strip())
                dataOutput['邮件来源'].append(tds[25].get_text().strip())
                dataOutput['修改人员'].append(tds[26].get_text().strip())
                dataOutput['海关回执信息'].append(tds[27].get_text().strip())
                dataOutput['出库申请时间'].append(tds[28].get_text().strip())
                dataOutput['一催日期'].append(tds[29].get_text().strip())
                dataOutput['二催日期'].append(tds[30].get_text().strip())
                dataOutput['逾期日期'].append(tds[31].get_text().strip())
                # L += 1
                # if L % 1000 == 0:
                #     # 添加适当的同步机制，例如使用 threading.Lock
                #     with lock:
                #         # 模拟一些耗时操作
                #         time.sleep(1)
                #         # 在主线程中调度更新UI
                #         parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    session.close()
    return dataOutput

def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data

        merged_data = {
            "邮件号": [],
            "业务类型": [],
            "接收时间": [],
            "入库时间": [],
            "出库时间": [],
            "入库状态": [],
            "短信发送状态": [],
            "海关回执代码": [],
            "仓库名称": [],
            "仓库区域": [],
            "货架号": [],
            "是否逾期": [],
            "是否外事": [],
            "是否分运行李": [],
            "是否发验": [],
            "异常类型": [],
            "面单打印时间": [],
            "面单邮件号": [],
            "姓名": [],
            "手机号码": [],
            "客户状态": [],
            "地址": [],
            "批译地址": [],
            "邮件来源": [],
            "修改人员": [],
            "海关回执信息": [],
            "出库申请时间": [],
            "一催日期": [],
            "二催日期": [],
            "逾期日期": []
        }
        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************'==ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()
        password = password_entry.get()

        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')

        url='https://**********/intcubr-web/a/intcubr/storehousemanage/tolist'
        result=tool.getck(username,password,session,url)
        jdptid = result[0]
        userName=result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName,title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        if not datalist:

            merged_data = getmail2(parent)
        else:
            # 并发执行并获取结果
            #results = pool.map(lambda mailno: getmail(mailno, parent), datalist)
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getmail, parent)
            results = pool.map(getmailtrace_bound, datalist)
            # 合并字典数据
            def merge_dicts(dict1, dict2):
                for key in dict1:
                    dict1[key].extend(dict2[key])
                return dict1

            merged_data = reduce(merge_dicts, results, merged_data)
        tool.process_input('处理完毕')
        if bool(merged_data):
            tool.process_input('正在写入Excel')

            # 定义当前时间
            currentTime = datetime.datetime.now()
            dataForm = pandas.DataFrame(merged_data)
            row = 1048570
            length = len(dataForm)
            number = length // row
            for i in range(number + 1):
                dataForm[i * row:(i + 1) * row].to_excel("邮件扣仓数据-" +
                                                         currentTime.strftime("%Y%m%d%H%M%S")  + "-bylhx.xlsx",
                                                         index=False)

            tool.process_input("写入完成共" + str(number + 1) + "个文件")
        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        #del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()




def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox, \
        submit_button, button_clear, start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, \
        business_label, business_combobox, business_options, selected, jlyy_textbox,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建主窗口
    #root = tk.Tk()

    #today = datetime.datetime.today()
    #yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)


    # 创建代理功能勾选框的变量
    #proxy_enabled = tk.BooleanVar()

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行"])
    organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项


    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=0, column=1, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=0, column=2, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行",command=lambda: handle_input(title,func_window))
    submit_button.grid(row=1, column=1, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=2, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)
    # 创建工具类实例
    tool = Tool(output_textbox, account_entry,password_entry,func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单", command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
