import pandas as pd

# 替换为你的Excel文件路径
excel_file_path = '2024年1月二干盘驳和加车发运登记.xlsx'

# 使用 pd.ExcelFile 读取所有Sheet
xls = pd.ExcelFile(excel_file_path)

# 初始化一个空的 DataFrame 用于保存汇总数据
total_df = pd.DataFrame()

# 遍历每个 Sheet
for sheet_name in xls.sheet_names:
    # 忽略 "Total" Sheet
    if sheet_name.lower() == 'total':
        continue

    # 读取当前 Sheet 的数据
    df = pd.read_excel(excel_file_path, sheet_name=sheet_name, engine='openpyxl')

    # 根据"交接对象"列进行汇总
    grouped_df = df.groupby('交接对象').agg({'总数': 'sum', '重量(kg)': 'sum'}).reset_index()

    # 合并到总的 DataFrame 中
    total_df = pd.concat([total_df, grouped_df])

# 根据"交接对象"列再次进行汇总，将相同"交接对象"的行合并
final_total_df = total_df.groupby('交接对象').agg({'总数': 'sum', '重量(kg)': 'sum'}).reset_index()

# 将汇总数据写入一个新的 Excel 文件
new_excel_path = 'Total.xlsx'
with pd.ExcelWriter(new_excel_path, engine='openpyxl') as writer:
    final_total_df.to_excel(writer, sheet_name='Total', index=False)
