
var JDPT_CAS_LOGIN_KEY = "B+oQ52IuAt9wbMxw";

/**
 * 加密（需要先加载lib/aes/aes.min.js文件）
 * @param word
 * @returns {*}
 */
function encrypt(word, aKey){
    var key = CryptoJS.enc.Utf8.parse(aKey);
    var srcs = CryptoJS.enc.Utf8.parse(word);
    var encrypted = CryptoJS.AES.encrypt(srcs, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
    return encrypted.toString();
}

/**
 * 解密
 * @param word
 * @returns {*}
 */
function decrypt(word, aKey){
    var key = CryptoJS.enc.Utf8.parse(aKey);
    var decrypt = CryptoJS.AES.decrypt(word, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
    return CryptoJS.enc.Utf8.stringify(decrypt).toString();
}


// 功能：第三方跳转AES加密
function third_encrypt(content) {
	return encrypt(content, JDPT_CAS_LOGIN_KEY);
}
// 功能：第三方跳转AES解密
function third_decrypt(miwen) {
	return decrypt(miwen, JDPT_CAS_LOGIN_KEY);
}


//功能：基础服务HIDE-JS
function jdptBicService(documentObj, ctxPath) {
	// ...
}

