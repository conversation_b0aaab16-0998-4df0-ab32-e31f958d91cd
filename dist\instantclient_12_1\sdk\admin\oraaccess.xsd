<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://xmlns.oracle.com/oci/oraaccess" 
           elementFormDefault="qualified" 
           xmlns:oci="http://xmlns.oracle.com/oci/oraaccess"
           xmlns="http://xmlns.oracle.com/oci/oraaccess">

     <!-- This file is auto generated. Do not modify. -->


    <!-- define memory size type. valid suffixes are k/K/m/M and no suffix -->
    <xs:simpleType name="memSizeType">
        <xs:restriction base="xs:string">
          <xs:pattern value="[0-9]+[k|K|m|M]|[0-9]+"/>
            <xs:whiteSpace value="collapse"/>
        </xs:restriction>
    </xs:simpleType>

    <!--  percentType -->
    <xs:simpleType name="percentType">
        <xs:restriction base="xs:double">
            <xs:minInclusive value="0.000"/>
            <xs:maxInclusive value="100.000"/>
        </xs:restriction>
    </xs:simpleType>

    <!--  TraceEvents -->
    <xs:simpleType name="traceEventNumberType"> 
    <!-- allowed values -->
       <xs:restriction base="xs:unsignedInt">
         <xs:enumeration value="10883"/>
       </xs:restriction>
   </xs:simpleType>

   <xs:simpleType name="fanSubsFailureActionType"> <!-- allowed values -->
       <xs:restriction base="xs:string">
         <xs:enumeration value="error"/>
         <xs:enumeration value="trace"/>
         <xs:whiteSpace value="collapse"/>
       </xs:restriction>
   </xs:simpleType>

 <xs:element name="oraaccess"> <!-- root element -->
        <xs:complexType>
            <xs:sequence> 
                <xs:element ref="oci:default_parameters" minOccurs="0"/>
                <xs:element ref="oci:config_descriptions" minOccurs="0"/>
                <xs:element ref="oci:connection_configs" minOccurs="0"/>
            </xs:sequence>
            <xs:attribute name="schemaLocation" use="required"/>
        </xs:complexType>
    </xs:element>
    <!-- structure of default_parameters -->

    <xs:element name="default_parameters">
        <xs:complexType>
            <xs:sequence> <!-- default parameters -->
                <xs:element ref="oci:prefetch" minOccurs="0"/>
                <xs:element ref="oci:statement_cache" minOccurs="0"/>
                <xs:element ref="oci:auto_tune" minOccurs="0"/>
                <xs:element ref="oci:fan" minOccurs="0"/>
                <xs:element ref="oci:ons" minOccurs="0"/>
                <xs:element name="events" type="xs:boolean"
                  minOccurs="0"/>
                <xs:element ref="oci:result_cache" minOccurs="0"/>
                <xs:element ref="oci:diag" minOccurs="0"/>
           </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- definition/structure of result_cache -->
    <xs:element name="result_cache">
        <xs:complexType>
            <xs:sequence>
              <xs:element name="max_rset_rows" type="xs:unsignedInt"
                  minOccurs="0"/>
                <xs:element name="max_rset_size" type="memSizeType" 
                    minOccurs="0"/>
                <xs:element name="max_size" type="memSizeType"
                    minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- structure/definition of diag prameters -->
    <xs:element name="diag">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="adr_enabled" type="xs:boolean" 
                    minOccurs="0"/>
                <xs:element name="dde_enabled" type="xs:boolean"
                    minOccurs="0"/>
                <xs:element name="adr_base" type="xs:string"
                    minOccurs="0"/>
                <xs:element name="sighandler_enabled" type="xs:boolean" 
                    minOccurs="0"/>
                <xs:element name="restricted" type="xs:boolean"
                    minOccurs="0"/>
                <xs:element ref="oci:trace_events" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- trace_events -->
    <xs:element name="trace_events">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="trace_event" maxOccurs="unbounded">
                 <xs:complexType>
                   <xs:sequence>
                     <xs:element name="number" type="traceEventNumberType"
                         minOccurs="1"/>
                     <xs:element name="level" type="xs:unsignedInt" 
                         minOccurs="1"/>
                   </xs:sequence>
                 </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- prefetch -->
    <xs:element name="prefetch">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="rows" type="xs:unsignedInt"
                    minOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- structure of auto_tune -->
    <xs:element name="auto_tune">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="enable" type="xs:boolean"
                    minOccurs="0"/>
                <xs:element name="ram_threshold" type="percentType"
                    minOccurs="0"/>
                <xs:element name="memory_target" type="memSizeType" 
                    minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- statement_cache -->
    <xs:element name="statement_cache">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="size" type="xs:unsignedInt"
                    minOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- fan -->
    <xs:element name="fan">
        <xs:complexType>
            <xs:sequence>
            <xs:element name="subscription_failure_action"
                type="fanSubsFailureActionType" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- structure/definition of ons parameters  -->
    <xs:element name="ons">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="subscription_wait_timeout"
                   type="xs:unsignedInt" minOccurs="0"/>
                <xs:element name="auto_config" type="xs:boolean"
                    minOccurs="0"/>
                <xs:element name="thread_stack_size" type="memSizeType"
                    minOccurs="0"/>
                <xs:element name="debug" type="xs:boolean"
                    minOccurs="0"/>
                <xs:element ref="oci:servers" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- node structure of servers -->
    <xs:element name="servers">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="oci:address_list" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

   <!-- definition/structure of address_list -->
    <xs:element name="address_list">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="name" type="xs:string" minOccurs="1"/>
                <xs:element name="max_connections" type="xs:unsignedInt"
                    minOccurs="0"/>
                <xs:element name="hosts" type="xs:string" minOccurs="1"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <!-- config_descriptions -->
    <xs:element name="config_descriptions">
        <xs:complexType>
            <xs:sequence>
              <xs:element ref="oci:config_description"
                  maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

   <xs:element name="config_description">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="config_alias" type="xs:string"
                    minOccurs="0"/>
                <xs:element ref="oci:parameters" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

 <!-- connection specific 'parameters' -->
    <xs:element name="parameters">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="oci:prefetch" minOccurs="0"/>
                <xs:element ref="oci:statement_cache" minOccurs="0"/>
                <xs:element ref="oci:auto_tune" minOccurs="0"/>
                <xs:element ref="oci:fan" minOccurs="0"/>
                <xs:element ref="oci:ons" minOccurs="0"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

   <xs:element name="connection_configs">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="oci:connection_config"
                    maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
 
  <xs:element name="connection_config">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="connection_string" type="xs:string"/>
                <xs:element name="config_alias" type="xs:string"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
