
import io
import os
import socket
import sys
import threading
import tkinter as tk
from tkinter import *
import traceback
from tkinter import ttk, scrolledtext
from tkinter.ttk import Label, Entry

from os import path
import requests,json, time
from itertools import product
from concurrent.futures import ThreadPoolExecutor, as_completed
from tool import Tool

def getmailtrace(mailno, jdptid, session,parent):

    #print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryCurrentTraceByTrace_nos'
    #url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryCurrentTraceByTrace_nos'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_nos': mailno,
        'numType': 15,
        'limit': 20
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    #print(r)
    jsonObj = json.loads(r)
    # 获取input2_textbox的值
    input2_value = input2_textbox.get()
    # datalist=[]
    # 用#分割每个值
    values_list = input2_value.split('#')
    if jsonObj:
        #print(mailno)
        if 'receivePlace' in jsonObj[0]:
            #print(mailno + jsonObj[0]["receivePlace"]+jsonObj[0]['opOrgCode'])
            #print(mailno+jsonObj[0]["receivePlace"])
            # datalist.append(mailno)
            if '默认' in selected.get():
                if ('51000061' in jsonObj[0]['opOrgCode']):
                    print('在广航'+mailno + jsonObj[0]["receivePlace"])
                    #tool.process_input(mailno + jsonObj[0]["receivePlace"])
                    # 在主线程中调度更新UI
                    parent.after(0, tool.process_input(mailno + jsonObj[0]["receivePlace"]))
                else:
                    print('不在广航' + mailno + jsonObj[0]["receivePlace"])
            else:
                if ('51000061' in jsonObj[0]['opOrgCode'] or jsonObj[0]['opOrgCode'] in values_list):
                    print('在广航' + mailno + jsonObj[0]["receivePlace"])
                    # tool.process_input(mailno + jsonObj[0]["receivePlace"])
                    # 在主线程中调度更新UI
                    parent.after(0, tool.process_input(mailno + jsonObj[0]["receivePlace"]))

    # 循环结束后释放session资源
    #session.close()
    # return datalist



# def worker(mailno_pattern, jdptid, session, entry,parent):
#     mailno = mailno_pattern.format(*entry)
#     getmailtrace(mailno, jdptid, session,parent)


# 启动线程池并发执行任务
# def run_threads(mailno_pattern, jdptid, session, num_threads,parent):
#     c = mailno_pattern.count("*")
#     entries = product("0123456789", repeat=c)
#     num_entries = len(list(entries))
#     chunk_size = num_entries // num_threads
#     futures = []
#     with ThreadPoolExecutor(max_workers=num_threads) as executor:
#         for i in range(num_threads):
#             start_idx = i * chunk_size
#             end_idx = (i + 1) * chunk_size if i != num_threads - 1 else num_entries
#             pattern = mailno_pattern.replace("*", "{}")
#             for entry in product("0123456789", repeat=c):
#                 if start_idx <= i <= end_idx:
#                     f = executor.submit(worker, pattern, jdptid, session, entry,parent)
#                     futures.append(f)
#
#     # # 等待所有任务执行完毕
#     for future in as_completed(futures):
#         pass
#
#     print("爬取完毕")
def worker(mailno_pattern, jdptid, session, entries, parent):
    for entry in entries:
        mailno = mailno_pattern.format(*entry)
        getmailtrace(mailno, jdptid, session, parent)


# def worker(mailno_pattern, jdptid, session, entries, parent):
#     with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
#         futures = []
#         for entry in entries:
#             mailno = mailno_pattern.format(*entry)
#             futures.append(executor.submit(getmailtrace, mailno, jdptid, session, parent))
#
#         for future in as_completed(futures):
#             try:
#                 future.result()
#             except Exception as e:
#                 print(f"任务执行时出错: {e}")
def run_threads(mailno_pattern, jdptid, session, num_threads, parent):
    c = mailno_pattern.count("*")
    entries = list(product("0123456789", repeat=c))
    chunk_size = len(entries) // num_threads
    futures = []

    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        for i in range(num_threads):
            start_idx = i * chunk_size
            end_idx = (i + 1) * chunk_size if i != num_threads - 1 else len(entries)
            entries_chunk = entries[start_idx:end_idx]
            pattern = mailno_pattern.replace("*", "{}")
            f = executor.submit(worker, pattern, jdptid, session, entries_chunk, parent)
            futures.append(f)

    for future in as_completed(futures):
        pass

    print("爬取完毕")

def run(title,parent):
    try:
        global username, password, session, jdptid, L

        # 构造Session
        session = requests.Session()

        L = 1
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get()

        # # 将文本按行分割并去除空行
        # lines = text.splitlines()
        # lines = [line for line in lines if line.strip()]
        # datalist = lines

        i = 2

        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        # 启动线程池并发执行任务
        num_threads = int(threads_combobox.get())
        tool.process_input('当前线程:' + threads_combobox.get())

        run_threads(text, jdptid, session, num_threads,parent)

        tool.process_input('爬取完毕')
        
        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')

        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()

def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    func_window.title(title+" Power by LHX ")
    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)
    # 设置功能2界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, root, submit_button, button_clear,\
        account_entry, password_entry, threads_combobox,tool,back_button,organization_combobox,selected,input2_textbox

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号(未知用*代替):")
    input_label.grid(row=0, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Entry(input_label_container, width=30)
    input_textbox.grid(row=0, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 创建单选按钮
    selected = tk.StringVar(value='默认')
    radio2_label = ttk.Label(input_label_container, text="查找模式:")
    radio2_label.grid(row=1, column=0, padx=10, pady=10)
    radio2_button1 = ttk.Radiobutton(input_label_container, text="默认", value='默认', variable=selected)
    radio2_button1.grid(row=1, column=1, padx=5, pady=10)

    radio2_button2 = ttk.Radiobutton(input_label_container, text="追加", value='追加', variable=selected)
    radio2_button2.grid(row=1, column=2, padx=5, pady=10)

    # 添加指定处理动作
    input2_label = ttk.Label(input_label_container, text="追加其他单位代码(多个代码用#分隔):")
    input2_label.grid(row=2, column=0, padx=10, pady=10)
    # input_label.pack()
    input2_textbox = tk.Entry(input_label_container, width=30)
    input2_textbox.grid(row=2, column=1, padx=10, pady=10, columnspan=1)


    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=3, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=3, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)
    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()
    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单", command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
