<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head>
<meta http-equiv="Content-Type" content="text/html; charset=us-ascii" />
<meta http-equiv="Content-Style-Type" content="text/css" />
<meta http-equiv="Content-Script-Type" content="text/javascript" />

<title>Using the Oracle ODBC Driver</title><meta name="generator" content="Oracle DARB XHTML Converter (Mode = browser help) - Version 5.1.2 Build 708" />
<meta name="date" content="2013-03-15T11:44:58Z" />
<meta name="robots" content="noarchive" />
<meta name="doctitle" content="Using the Oracle ODBC Driver" />
<meta name="relnum" content="" />
<meta name="partnum" content="" />
<link rel="copyright" href="cpyr.htm" title="Copyright" type="text/html" />
<link rel="stylesheet" href="blafdoc.css" title="Oracle BLAFDoc" type="text/css" />
<link rel="contents" href="toc.htm" title="Contents" type="text/html" />
<link rel="prev" href="toc.htm" title="Previous" type="text/html" /></head>

<body>
<div class="header"><a id="top" name="top"></a>
<div class="zz-skip-header"><a href="#BEGIN">Skip Headers</a></div>
<table class="simple oac_no_warn" summary="" cellpadding="0" cellspacing="0" width="100%">
<tbody><tr>
<td align="left" valign="top"><br />
<br /></td>
<td align="right" valign="bottom">
<table class="simple oac_no_warn" summary="" cellpadding="0" cellspacing="0">
<tbody><tr>
<td>&nbsp;</td>
<td align="right" valign="top"><a href="toc.htm"><br />
<span class="icon">Contents</span></a></td>
</tr>
</tbody></table>
</td>
</tr>
</tbody></table>
<hr />
<table class="simple oac_no_warn" summary="" cellpadding="0" cellspacing="0" width="100">
<tbody><tr>
<td align="center"><a href="toc.htm"><br />
<span class="icon">Previous</span></a>&nbsp;</td>
<td>&nbsp;</td>
</tr>
</tbody></table>
<a name="BEGIN" id="BEGIN"></a></div>
<!-- class="header" -->
<div class="ind"><!-- End Header -->
<script type="text/javascript">
<!-- // <![CDATA[
window.name='sqora'
// ]]> -->
</script> <script type="text/javascript">
// <![CDATA[
function footdisplay(footnum,footnote) {
    var msg = window.open('', 'NewWindow' + footnum,
        'directories=no,height=120,location=no,menubar=no,resizable=yes,' +
        'scrollbars=yes,status=no,toolbar=no,width=598');
    msg.document.open('text/html');
    msg.document.write('<!DOCTYPE html ');
    msg.document.write('PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" ');

    msg.document.write('"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');
    msg.document.write('<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head><title>');
    msg.document.write('Footnote ' + footnum);
    msg.document.write('<\/title><meta http-equiv="Content-Type" ');
    msg.document.write('content="text/html; charset=utf-8" />');
    msg.document.write('<meta http-equiv="Content-Script-Type" ');
    msg.document.write('content="text/javascript" />');
    msg.document.write('<style type="text/css"> <![CDATA[ ');
    msg.document.write('h1 {text-align: center; font-size: 14pt;}');
    msg.document.write('fieldset {border: none;}');
    msg.document.write('form {text-align: center;}');
    msg.document.write(' ]]\u003e <\/style>');
    msg.document.write('<\/head><body><h1>Footnote ' + footnum + '<\/h1><p>');
    msg.document.write(footnote);
    msg.document.write('<\/p><form action="" method="post"><fieldset>');
    msg.document.write('<input type="button" value="OK" ');
    msg.document.write('onclick="window.close();" />');
    msg.document.write('<\/fieldset><\/form><\/body><\/html>');
    msg.document.close();
    msg.focus();
}
// ]]>
</script> <noscript>
<p>The script content on this page is for navigation purposes only and does not alter the content in any way.</p>
</noscript><a id="BABGJEHF" name="BABGJEHF"></a><a id="ADFNS1112" name="ADFNS1112"></a>
<h1 style="text-align: center;" class="chapter">Oracle ODBC Driver Help</h1> <span style="font-weight: bold;">Version 12.1</span>
<h1 class="chapter">Using the Oracle ODBC Driver</h1>
<p>This Oracle ODBC Driver information contains the following sections:</p>
<ul>
<li>
<p><a href="#BABEHFCD">About Oracle ODBC Driver</a></p>
</li>
<li>
<p><a href="#BABIBGJI">For All Users</a></p>
</li>
<li>
<p><a href="#BABGFHBE">For Advanced Users</a></p>
</li>
<li>
<p><a href="#BABICHJC">For Programmers</a></p>
</li>
<li>
<p><a href="#BABJHHHH">Glossary</a></p>
</li>
<li>
<p><a href="#BABCEBCD">Acknowledgements</a></p>
</li>
</ul>
<a id="BABEHFCD" name="BABEHFCD"></a><a id="ADFNS1113" name="ADFNS1113"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">About Oracle ODBC Driver</h2>
<a id="ADFNS1114" name="ADFNS1114"></a>
<p class="subhead2">What is ODBC?</p>
<p>Open Database Connectivity (<a href="#BABFCGBI">ODBC</a>) provides a
standard interface that allows one application to access many different
data sources. The application's source code does not have to be
recompiled for each data source. A database driver links the
application to a specific data source. A database driver is a
dynamic-link library that an application can invoke on demand to gain
access to a particular data source. Therefore, the application can
access any data source for which a database driver exists.</p>
<p>The ODBC interface defines the following:</p>
<ul>
<li>
<p>A library of ODBC function calls that allows an application to
connect to a data source, execute SQL statements, and retrieve results.</p>
</li>
<li>
<p>SQL syntax based on the SQL-99 specification.</p>
</li>
<li>
<p>A standard set of error codes.</p>
</li>
<li>
<p>A standard way to connect to and log in to a data source.</p>
</li>
<li>
<p>A standard representation for data types.</p>
</li>
</ul>
<p><a href="#BABEIGEE">Figure 1-1</a> shows the components of the ODBC
model. The model begins with an ODBC application making a call to the
Driver Manager through the ODBC API. The Driver Manager can be either
the Microsoft Driver Manager or the unixODBC Driver Manager. Still
using the ODBC API, the Driver Manager makes a call to the ODBC Driver.
The ODBC Driver accesses the database over a network communications
link using the database API. <a href="#BABEIGEE">Figure 1-1</a> shows an ODBC application accessing three separate databases.</p>
<div class="figure"><a id="BABEIGEE" name="BABEIGEE"></a><a id="ADFNS1115" name="ADFNS1115"></a>
<p class="titleinfigure">Figure 1-1 Components of the ODBC Model</p>
<img src="img/odbcmodel.gif" alt="Description of Figure 1-1 follows" longdesc="img_text/odbcmodel.htm" /><br />
<a id="sthref1" name="sthref1" href="img_text/odbcmodel.htm">Description of "Figure 1-1 Components of the ODBC Model"</a><br />
<br /></div>
<!-- class="figure" -->
<a id="ADFNS1116" name="ADFNS1116"></a>
<p class="subhead2">Related Topic</p>
<p><a href="#BABJFIEC">What is the Oracle ODBC Driver</a></p>
</div>
<!-- class="sect1" -->
<a id="BABIBGJI" name="BABIBGJI"></a><a id="ADFNS1117" name="ADFNS1117"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">For All Users</h2>
<ul>
<li>
<p><a href="#BABGEDCB">Oracle ODBC Driver</a></p>
</li>
<li>
<p><a href="#BABGJJAF">Configuration Tasks</a></p>
</li>
<li>
<p><a href="#BABHABJB">Modifying the oraodbc.ini File</a></p>
</li>
<li>
<p><a href="#BABDBDJG">Connecting to a Data Source</a></p>
</li>
<li>
<p><a href="#BABCHDJE">Troubleshooting</a></p>
</li>
</ul>
<a id="BABGEDCB" name="BABGEDCB"></a><a id="ADFNS1118" name="ADFNS1118"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Oracle ODBC Driver</h3>
<ul>
<li>
<p><a href="#BABJFIEC">What is the Oracle ODBC Driver</a></p>
</li>
<li>
<p><a href="#BABCGECE">New and Changed Features</a></p>
</li>
<li>
<p><a href="#BABBDJAA">Features Not Supported</a></p>
</li>
<li>
<p><a href="#BABHEGHC">Files Created by the Installation</a></p>
</li>
<li>
<p><a href="#BABECBDH">Driver Conformance Levels</a></p>
</li>
<li>
<p><a href="#BABCICCF">Known Limitations</a></p>
</li>
</ul>
<a id="BABJFIEC" name="BABJFIEC"></a><a id="ADFNS1119" name="ADFNS1119"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">What is the Oracle ODBC Driver</h4>
<p>The Oracle ODBC Driver enables ODBC applications on Microsoft
Windows, as well as UNIX platforms like Linux, Solaris, and AIX read
and write access to Oracle&#174; databases through the ODBC interface using <a href="#BABFDACF">Oracle Net Services</a> software.</p>
<p>The Oracle ODBC Driver uses the <a href="#BABGCEAA">Oracle Call Interface</a>
(OCI) client and server software to submit requests to and receive
responses from the data source. Oracle Net Services communications
protocol is used for communications between the OCI client and the
Oracle server.</p>
<p>The Oracle ODBC Driver translates ODBC <a href="#BABJABEH">SQL</a>
syntax into syntax that can be used to access the data source. When the
results are returned from the data source, the Oracle ODBC Driver
translates them back to ODBC SQL syntax.</p>
<p><a href="#BABJEDJJ">Figure 1-2</a> shows the Oracle ODBC Driver architecture as described in the preceding paragraphs.</p>
<div class="figure"><a id="BABJEDJJ" name="BABJEDJJ"></a><a id="ADFNS1120" name="ADFNS1120"></a>
<p class="titleinfigure">Figure 1-2 Oracle ODBC Driver Architecture</p>
<img src="img/odbcdrvarch.gif" alt="Description of Figure 1-2 follows" longdesc="img_text/odbcdrvarch.htm" /><br />
<a id="sthref2" name="sthref2" href="img_text/odbcdrvarch.htm">Description of "Figure 1-2 Oracle ODBC Driver Architecture"</a><br />
<br /></div>
<!-- class="figure" -->
<p>* The Oracle ODBC Resource <a href="#BABIIHGJ">DLL</a> file (<code>sqres</code><code><span class="codeinlineitalic">xx</span></code><code>.dll</code>), where <code><span class="codeinlineitalic">xx</span></code> represents the language abbreviation, contains all pertinent language information; the default resource file used is <code>sqresus.dll</code>.</p>
<p>For more information about the OCI client and server software, refer to the OCI documentation.</p>
<a id="ADFNS1121" name="ADFNS1121"></a>
<p class="subhead2">Related Topics</p>
<p><a href="#BABBCBHH">Configuring the Data Source</a></p>
<p><a href="#BABDBDJG">Connecting to a Data Source</a></p>
<p><a href="#BABECBDH">Driver Conformance Levels</a></p>
<p><a href="#BABCGECE">New and Changed Features</a></p>
<p><a href="#BABHEGHC">Files Created by the Installation</a></p>
</div>
<!-- class="sect3" -->
<a id="BABCGECE" name="BABCGECE"></a><a id="ADFNS1122" name="ADFNS1122"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">New and Changed Features</h4>
<ul>
<li>
<p><a href="#BABEDIHI">New Features for Release ********.0</a></p>
</li>
<li>
<p><a href="#BABBFBBG">New Features for Release 11.2.0.1.0</a></p>
</li>
<li>
<p><a href="#BABHBGGB">New Features for Release 11.1.0.1.0</a></p>
</li>
<li>
<p><a href="#BABDABCJ">New Features for Release ********.0</a></p>
</li>
<li>
<p><a href="#BABCCDCJ">Changes for Release ********.0</a></p>
</li>
</ul>
<a id="BABEDIHI" name="BABEDIHI"></a><a id="ADFNS1123" name="ADFNS1123"></a>
<p class="subhead2">New Features for Release ********.0</p>
<p>Features of the Oracle ODBC Driver Release ********.0 software for
the Microsoft Windows Server 2008, Windows Server 2008 R2, Windows 7,
Windows 8, Windows Server 2012, Linux X86-64 (32/64 bit), Sun Solaris
SPARC64 (32,64 bit), IBM AIX 5L (32,64 bit), Sun Solaris X64 (32,64
bit), HPUX IA64 (32,64 bit), ZLinux (32,64 bit) operating systems are
described as follows:</p>
<ul>
<li>
<p>Oracle ODBC Driver now supports 32 KB data columns with <code>VARCHAR2</code>, <code>NVARCHAR2</code> and <code>RAW</code> data. See <a href="olink:LNPLS99943"><span class="italic">Oracle Database PL/SQL Language Reference</span></a> and <a href="olink:SQLRF55623"><span class="italic">Oracle Database SQL Language Reference</span></a> for information about creating 32 KB columns.</p>
</li>
<li>
<p>New parameters in the <code>odbc.ini</code> file or connection level attributes:</p>
<ul>
<li>
<p>SQL_TRANSLATE_ERRORS= T/F [Default is F (false)]</p>
<p>Any migrated third party ODBC application, which is using the
Babelfish feature, expects that errors returned by the server to be in
their native database format, then users can register their translation
of errors with the SQL Translation Profile in Oracle Database running
in Babelfish mode. Once error translation is registered, then ODBC
application users can enable this option, <code>SQLTranslateErrors = T</code>, to receive native errors as per their registration.</p>
<p>For more information on SQL Translation Framework, see <span class="italic">Oracle Database Migration Guide</span>, in particular information about <a href="olink:DRDAA131">SQL Translation Framework Architecture and Overview</a>, <a href="olink:DRDAA132">Translation Framework installation and configuration</a>, and <a href="olink:DRDAA133">migration examples</a>.</p>
</li>
</ul>
<p>See <a href="#BABEFBFB">Table 1-5</a> for more information.</p>
</li>
<li>
<p>Oracle ODBC driver now supports executing a stored procedure, which can return implicit results without using <code>RefCursor</code>.
This support eases any third party ODBC application, which migrated to
Oracle and wants to use this same functionality that was provided by
their previous vendors.</p>
<p>See <a href="olink:DRDAA230"><span class="italic">Oracle Database Migration Guide</span></a> for more information about implicit results support by Oracle Database.</p>
</li>
<li><a id="BABHDJED" name="BABHDJED"></a>
<p>Extended support of SQLColAttribute() field identifiers to support
Oracle Database auto increment feature. You can use this feature by
including Oracle ODBC driver specific header file <code>sqora.h</code> in the application. See <a href="olink:LNOCI16468"><span class="italic">Oracle Call Interface Programmer's Guide</span></a> for more information about auto increment:</p>
<ul>
<li>
<p><code>SQL_COLUMN_AUTO_INCREMENT</code></p>
<p>Starting from Oracle Database Release 12<span class="italic">c</span>
Release 1 (12.1), Oracle supports auto increment columns so the Oracle
ODBC Driver has extended the same support through the existing
SQLColAttribute() identifier <code>SQL_COLUMN_AUTO_INCREMENT</code>. This property is read only and returns <code>SQL_TRUE</code> if the column is auto increment; otherwise, it returns <code>SQL_FALSE</code>.</p>
</li>
<li>
<p><code>SQL_ORCLATTR_COLUMN_PROP</code></p>
<p>Starting from Oracle Database Release 12<span class="italic">c</span> Release 1 (12.1), Oracle ODBC Driver supports a new driver specific field identifier <code>SQL_ORCLATTR_COLUMN_PROP</code>, which returns the attributes of the column. This identifier returns <code>SQLULEN</code> value, which has all the column properties, shown as follows:</p>
<pre xml:space="preserve" class="oac_no_warn">+-----------------------------------------+<br />| 32 |...| 10 | 9 | 8 |......| 3 | 2 | 1  |<br />+-----------------------------------------+<br />                               |   |   |<br />                               |   |   |-&gt; Column is auto-increment?<br />                               |   |-&gt; Auto value is always generated?<br />                               |-&gt; If generated by default when null?<br /></pre></li>
</ul>
</li>
<li>
<p>ODBC APIs supported in Oracle Database Release 12<span class="italic">c</span> Release 1 (12.1)</p>
<ul>
<li>
<p>SQLMoreResults()</p>
<p>Implements ODBC support for implicit results. See <a href="#BABIIHAC">Table 1-7</a> in <a href="#BABHEGBH">Implementation of ODBC API Functions</a> and <code><a href="http://msdn.microsoft.com/en-us/library/ms714673%28v=VS.85%29.aspx">http://msdn.microsoft.com/en-us/library/ms714673(v=VS.85).aspx</a></code> for more information.</p>
</li>
</ul>
</li>
</ul>
<a id="BABBFBBG" name="BABBFBBG"></a><a id="ADFNS1124" name="ADFNS1124"></a>
<p class="subhead2">New Features for Release 11.2.0.1.0</p>
<p>Features of the Oracle ODBC Driver Release 11.2.0.1.0 software for
the Microsoft Windows XP, Microsoft Windows 2003 Server, Microsoft
Windows Vista, Linux X86-32 (RHEL AS 4,5), Linux X86-64 (RHEL AS 4,5)
(32/64 bit), Sun Solaris SPARC64 (9,10) (32,64 bit), IBM AIX 5L 5.2
(32,64 bit), Linux IA64 (64 bit), Linux on Power (32,64 bit), Sun
Solaris X64 (64 bit), Hewlett Packard Itanium (32,64 bit) operating
systems are described as follows:</p>
<ul>
<li>
<p>Prefetching of LONG/ LONG RAW data</p>
<p>Oracle ODBC driver is enhanced to prefetch <code>LONG</code> or <code>LONG RAW</code> data to improve performance of ODBC applications. To do this, the maximum size of <code>LONG</code> data (<code>MaxLargeData</code>) must be set in the registry on Windows (you also need to add the registry key <code>MaxLargeData</code> in the DSN), and set this manually in the <code>odbc.ini</code> file on UNIX platforms. This enhancement improves the performance of Oracle ODBC driver up to 10 times, depending on the <code>MaxLargeData</code> size set by the user. The default value of <code>MaxLargeData</code> is 0. The maximum value for <code>MaxLargeData</code> that you can set is 64KB (65536 bytes).</p>
<p>If the value of <code>MaxLargeData</code> is set to some value greater than 65536, the data fetched will only be 65536 bytes. If you have <code>LONG</code> or <code>LONG RAW</code> data in the database that is greater that 65536 bytes, <code>MaxLargeData</code> should be set to 0 (the default value), which will result in single row fetch and complete <code>LONG</code> data can be fetched. If you pass a buffer size less than the <code>MaxLargeData</code> size in non-polling mode, a data truncation error will occur if the <code>LONG</code> data size in the database is greater than the buffer size.</p>
</li>
<li>
<p>Option for using <code>OCIDescribeAny()</code> for fetching metadata</p>
<p>When an application makes heavy calls to small packaged procedures that return <code>REF CURSORS</code>, a performance improvement can be made by forcing the driver to use <code>OCIDescribeAny()</code>. To enable this option, set the value of <code>UseOCIDescribeAny</code> in <code>odbc.ini</code> to <code>T</code> (True), default value is <code>F</code> (False), on UNIX platforms, and through the registry on Windows.</p>
</li>
</ul>
<a id="BABHBGGB" name="BABHBGGB"></a><a id="ADFNS1125" name="ADFNS1125"></a>
<p class="subhead2">New Features for Release 11.1.0.1.0</p>
<p>Features of the Oracle ODBC Driver Release 11.1.0.1.0 software for
the Windows XP, Linux, Solaris, and AIX operating systems are described
as follows:</p>
<ul>
<li>
<p>Disable Rule Hint (DRH Connect String)</p>
<p>Added the new connection option, Disable RULE Hint that allows user
to specify the option to select whether to use RULE Hint in catalog
APIs. The change has been done to increase the performance of ODBC
driver for catalog APIs. The default value for the option is <code>TRUE</code> which means that RULE Hint will not be used in catalog APIs by default.</p>
</li>
<li>
<p>Bind Number As Float (BNF Connect String)</p>
<p>Added the new connection option, Bind Number As Float. By introducing Column Binding for <code>NUMBER</code> Column as <code>FLOAT</code> when column contains float data speeds up the query execution that uses bind variables as <code>FLOAT</code>.</p>
</li>
<li>
<p>Statement Caching</p>
<p>Added support for OCI statement caching feature that provides and
manages a cache of statements for each session. By implementing the
support for OCI Statement Caching option, Oracle ODBC Driver will see
the increase in performance where user have to parse the same statement
multiple times in the same connection. The default value for the
statement cache flag is <code>FALSE</code>.</p>
</li>
</ul>
<a id="BABDABCJ" name="BABDABCJ"></a><a id="ADFNS1126" name="ADFNS1126"></a>
<p class="subhead2">New Features for Release ********.0</p>
<p>Features of the Oracle ODBC Driver Release ********.0 software for
the Windows 98, Windows 2000, Windows XP, and Windows NT X86 operating
systems are described as follows:</p>
<ul>
<li>
<p>Bind <code>TIMESTAMP</code> as <code>DATE</code> (BTD Connect String)</p>
<p>Added the new connection option, Bind <code>TIMESTAMP</code> as <code>DATE</code>, that allows you to bind the ODBC driver <code>SQL_TIMESTAMP</code> data type to the Oracle <code>DATE</code> data type instead of to the Oracle <code>TIMESTAMP</code> data type (which is the default).</p>
</li>
<li>
<p><code>MONTHNAME (exp)</code> Function</p>
<p>Added support for the <code>MONTHNAME (exp)</code> function which returns the name of the month represented by the date expression. For example, 'April'.</p>
</li>
<li>
<p><code>DAYNAME (exp)</code> Function</p>
<p>Added support for the <code>DAYNAME (exp)</code> function which returns the name of the day represented by the date expression. For example, 'Tuesday'.</p>
</li>
<li>
<p>Instant Client Configuration</p>
<p>Added support for the Instant Client mode configuration.</p>
</li>
</ul>
<a id="BABCCDCJ" name="BABCCDCJ"></a><a id="ADFNS1127" name="ADFNS1127"></a>
<p class="subhead2">Changes for Release ********.0</p>
<p>Changed or deprecated features of the Oracle ODBC Driver Release ********.0 include:</p>
<ul>
<li>
<p>Disable Microsoft Transaction Server</p>
<p>Changed the default setting for the Disable Microsoft Transaction Server (MTS) from <code>FALSE</code> to <code>TRUE</code>. By default, MTS support is disabled.</p>
</li>
<li>
<p>Floating Point Data Types</p>
<p>Changed the mapping of the Oracle data types, <code>BINARY_FLOAT</code> and <code>BINARY_DOUBLE</code>, to map to the ODBC data types, <code>SQL_REAL</code> and <code>SQL_DOUBLE</code>, respectively.</p>
</li>
<li>
<p>SQLGetData Extensions (GDE Connect String)</p>
<p>Deprecated the <code>SQLGetData</code> Extensions connection in this release. The functionality of this feature is always enabled.</p>
</li>
<li>
<p>Force Retrieval of Longs (FRL Connect String)</p>
<p>Deprecated the Force Retrieval of Longs connection option in this release. The functionality of this feature is always enabled.</p>
</li>
<li>
<p>Translation Options Configuration Tab</p>
<p>Deprecated the Translation Options tab previously found on the Oracle ODBC Driver Configuration dialog box in this release.</p>
</li>
<li>
<p>Release Notes</p>
<p>Renamed the Release Notes file from <code>ODBCRelnotes.wri</code> to <code>ODBCRelnotesUS.htm</code>.</p>
</li>
</ul>
</div>
<!-- class="sect3" -->
<a id="BABBDJAA" name="BABBDJAA"></a><a id="ADFNS1128" name="ADFNS1128"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Features Not Supported</h4>
<p>The Oracle ODBC Driver does not support the following ODBC 3.0 features:</p>
<ul>
<li>
<p>Interval data types</p>
</li>
<li>
<p><code>SQL_C_UBIGINT</code> and <code>SQL_C_SBIGINT</code> C data type identifiers</p>
</li>
<li>
<p>Shared connections</p>
</li>
<li>
<p>Shared environments</p>
</li>
<li>
<p>The <code>SQL_LOGIN_TIMEOUT</code> attribute of <code>SQLSetConnectAttr</code></p>
</li>
</ul>
<div class="tblformal"><a id="ADFNS1129" name="ADFNS1129"></a><a id="sthref3" name="sthref3"></a><a id="BABDEGAI" name="BABDEGAI"></a>
<p class="titleintable">Table 1-1 SQL Functions Not Supported by the Oracle ODBC Driver</p>
<table class="Formal" title="SQL Functions Not Supported by the Oracle ODBC Driver" summary="The table presents functions that are not supported by the Oracle ODBC Driver as three coumns of functions by function group name (string functions, numeric functions, and Time, Date, and Interval functions)." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="27%" />
<col width="27%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t2" align="left" valign="bottom">String Functions</th>
<th id="r1c2-t2" align="left" valign="bottom">Numeric Functions</th>
<th id="r1c3-t2" align="left" valign="bottom">Time, Date, and Interval Functions</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t2" headers="r1c1-t2" align="left">
<p><code>BIT_LENGTH</code></p>
</td>
<td headers="r2c1-t2 r1c2-t2" align="left">
<p><code>ACOS</code></p>
</td>
<td headers="r2c1-t2 r1c3-t2" align="left">
<p><code>CURRENT_DATE</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t2" headers="r1c1-t2" align="left">
<p><code>CHAR_LENGTH</code></p>
</td>
<td headers="r3c1-t2 r1c2-t2" align="left">
<p><code>ASIN</code></p>
</td>
<td headers="r3c1-t2 r1c3-t2" align="left">
<p><code>CURRENT_TIME</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t2" headers="r1c1-t2" align="left">
<p><code>CHARACTER_LENGTH</code></p>
</td>
<td headers="r4c1-t2 r1c2-t2" align="left">
<p><code>ATAN</code></p>
</td>
<td headers="r4c1-t2 r1c3-t2" align="left">
<p><code>CURRENT_TIMESTAMP</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t2" headers="r1c1-t2" align="left">
<p><code>DIFFERENCE</code></p>
</td>
<td headers="r5c1-t2 r1c2-t2" align="left">
<p><code>ATAN2</code></p>
</td>
<td headers="r5c1-t2 r1c3-t2" align="left">
<p><code>EXTRACT</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r6c1-t2" headers="r1c1-t2" align="left">
<p><code>OCTET_LENGTH</code></p>
</td>
<td headers="r6c1-t2 r1c2-t2" align="left">
<p><code>COT</code></p>
</td>
<td headers="r6c1-t2 r1c3-t2" align="left">
<p><code>TIMESTAMPDIFF</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r7c1-t2" headers="r1c1-t2" align="left">
<p><code>POSITION</code></p>
</td>
<td headers="r7c1-t2 r1c2-t2" align="left">
<p><code>DEGREES</code></p>
</td>
<td headers="r7c1-t2 r1c3-t2" align="left"><br /></td>
</tr>
<tr align="left" valign="top">
<td id="r8c1-t2" headers="r1c1-t2" align="left"><br /></td>
<td headers="r8c1-t2 r1c2-t2" align="left">
<p><code>RADIANS</code></p>
</td>
<td headers="r8c1-t2 r1c3-t2" align="left"><br /></td>
</tr>
<tr align="left" valign="top">
<td id="r9c1-t2" headers="r1c1-t2" align="left"><br /></td>
<td headers="r9c1-t2 r1c2-t2" align="left">
<p><code>RAND</code></p>
</td>
<td headers="r9c1-t2 r1c3-t2" align="left"><br /></td>
</tr>
<tr align="left" valign="top">
<td id="r10c1-t2" headers="r1c1-t2" align="left"><br /></td>
<td headers="r10c1-t2 r1c2-t2" align="left">
<p><code>ROUND</code></p>
</td>
<td headers="r10c1-t2 r1c3-t2" align="left"><br /></td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" --></div>
<!-- class="sect3" -->
<a id="BABHEGHC" name="BABHEGHC"></a><a id="ADFNS1130" name="ADFNS1130"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Files Created by the Installation</h4>
<div class="tblformal"><a id="ADFNS1131" name="ADFNS1131"></a><a id="sthref4" name="sthref4"></a><a id="BABFJJBD" name="BABFJJBD"></a>
<p class="titleintable">Table 1-2 Files Installed by the Oracle ODBC Driver Kit</p>
<table class="Formal" title="Files Installed by the Oracle ODBC Driver Kit" summary="This table describes the files installed by the Oracle ODBC Driver kit for the UNIX and Windows platforms and a brief description of what each file does." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="32%" />
<col width="32%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t3" align="left" valign="bottom">Description</th>
<th id="r1c2-t3" align="left" valign="bottom">File Name for Windows Installation</th>
<th id="r1c3-t3" align="left" valign="bottom">File Name for UNIX Installation</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Database Access DLL</p>
</td>
<td headers="r2c1-t3 r1c2-t3" align="left">
<p><code>sqora32.dll</code></p>
</td>
<td headers="r2c1-t3 r1c3-t3" align="left">
<p>libsqora.so.12.1</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver Setup DLL</p>
</td>
<td headers="r3c1-t3 r1c2-t3" align="left">
<p><code>sqoras32.dll</code></p>
</td>
<td headers="r3c1-t3 r1c3-t3" align="left">
<p>None</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Resource DLL</p>
</td>
<td headers="r4c1-t3 r1c2-t3" align="left">
<p><code>sqresus.dll</code></p>
</td>
<td headers="r4c1-t3 r1c3-t3" align="left">
<p>None</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Resource DLL for Japanese</p>
</td>
<td headers="r5c1-t3 r1c2-t3" align="left">
<p><code>sqresja.dll</code></p>
</td>
<td headers="r5c1-t3 r1c3-t3" align="left">
<p>None</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r6c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver message file</p>
</td>
<td headers="r6c1-t3 r1c2-t3" align="left">
<p><code>oraodbcus.msb</code></p>
</td>
<td headers="r6c1-t3 r1c3-t3" align="left">
<p><code>oraodbcus.msb</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r7c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver message file for Japanese</p>
</td>
<td headers="r7c1-t3 r1c2-t3" align="left">
<p><code>oraodbcja.msb</code></p>
</td>
<td headers="r7c1-t3 r1c3-t3" align="left">
<p><code>oraodbcja.msb</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r8c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver release notes</p>
</td>
<td headers="r8c1-t3 r1c2-t3" align="left">
<p><code>ODBCRelnotesUS.htm</code></p>
</td>
<td headers="r8c1-t3 r1c3-t3" align="left">
<p><code>ODBCRelnotesUS.htm</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r9c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver release notes for Japanese</p>
</td>
<td headers="r9c1-t3 r1c2-t3" align="left">
<p><code>ODBCRelnotesJA.htm</code></p>
</td>
<td headers="r9c1-t3 r1c3-t3" align="left">
<p><code>ODBCRelnotesJA.htm</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r10c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver Instant Client Release Notes</p>
</td>
<td headers="r10c1-t3 r1c2-t3" align="left">
<p><code>ODBC_IC_Readme_Win.html</code></p>
</td>
<td headers="r10c1-t3 r1c3-t3" align="left">
<p><code>ODBC_IC_Readme_Unix.html</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r11c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver help file</p>
</td>
<td headers="r11c1-t3 r1c2-t3" align="left">
<p><code>sqora.htm</code></p>
</td>
<td headers="r11c1-t3 r1c3-t3" align="left">
<p><code>sqora.htm</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r12c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver help file for Japanese</p>
</td>
<td headers="r12c1-t3 r1c2-t3" align="left">
<p><code>sqora.htm</code></p>
</td>
<td headers="r12c1-t3 r1c3-t3" align="left">
<p><code>sqora.htm</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r13c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver Instant Client install script</p>
</td>
<td headers="r13c1-t3 r1c2-t3" align="left">
<p><code>odbc_install.exe</code></p>
</td>
<td headers="r13c1-t3 r1c3-t3" align="left">
<p><code>odbc_update_ini.sh</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r14c1-t3" headers="r1c1-t3" align="left">
<p>Oracle ODBC Driver Instant Client un-install script</p>
</td>
<td headers="r14c1-t3 r1c2-t3" align="left">
<p><code>odbc_uninstall.exe</code></p>
</td>
<td headers="r14c1-t3 r1c3-t3" align="left">
<p>None</p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" -->
<a id="ADFNS1132" name="ADFNS1132"></a>
<p class="subhead2">Microsoft Driver Manager and Administrator Files</p>
<p>See the Microsoft ODBC 3.52 Software Development Kit and
Programmer's Reference for the list of files that are installed with
Microsoft's ODBC 3.52 Components.</p>
<p>The Microsoft ODBC components are packages in the Microsoft Data
Access Component (MDAC) kit. Oracle ODBC driver on Windows has been
tested using MDAC version 2.8. This can be downloaded from <code><a href="http://www.microsoft.com/download/en/search.aspx?q=ODBC+MDAC">http://www.microsoft.com/download/en/search.aspx?q=ODBC+MDAC</a></code></p>
<a id="ADFNS1133" name="ADFNS1133"></a>
<p class="subhead2">unixODBC Driver Manager and Administrator Files</p>
<p>See the unixODBC readme and INSTALL files for the list of files that are installed with unixODBC Driver Manager.</p>
<p>The unixODBC Driver Manager can be downloaded from <code><a href="http://www.unixodbc.org/download.html">http://www.unixodbc.org/download.html</a></code></p>
</div>
<!-- class="sect3" -->
<a id="BABECBDH" name="BABECBDH"></a><a id="ADFNS1134" name="ADFNS1134"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Driver Conformance Levels</h4>
<p>ODBC defines <a href="#BABJACEF">Conformance Levels</a> for drivers in two areas:</p>
<ul>
<li>
<p>ODBC application programming interface (<a href="#BABECBJJ">API</a>)</p>
</li>
<li>
<p>ODBC SQL-99 syntax</p>
</li>
</ul>
<p>The Oracle ODBC Driver supports all core API functionality and a limited set of Level 1 and Level 2 functionality. See <a href="#BABBCJCB">API Conformance</a> for more information.</p>
<p>The Oracle ODBC Driver is broadly compatible with the SQL-99 Core
specification which is a superset of the SQL-92 Entry Level
specification. Applications should call SQLGetInfo with the appropriate
information type to retrieve a list of SQL-99 supported features.</p>
</div>
<!-- class="sect3" -->
<a id="BABCICCF" name="BABCICCF"></a><a id="ADFNS1135" name="ADFNS1135"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Known Limitations</h4>
<p>The following are not supported by Oracle ODBC driver:</p>
<ul>
<li>
<p>ODBC ASYNC interface</p>
</li>
<li>
<p>Control-C to cancel execution in an application</p>
</li>
</ul>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABGJJAF" name="BABGJJAF"></a><a id="ADFNS1136" name="ADFNS1136"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Configuration Tasks</h3>
<ul>
<li>
<p><a href="#BABDFDJB">Configuring Oracle Net Services</a></p>
</li>
<li>
<p><a href="#BABBCBHH">Configuring the Data Source</a></p>
</li>
<li>
<p><a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a></p>
</li>
</ul>
<a id="BABDFDJB" name="BABDFDJB"></a><a id="ADFNS1137" name="ADFNS1137"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Configuring Oracle Net Services</h4>
<p>Before <a href="#BABBCBHH">Configuring the Data Source</a>, you must configure network database services so there is an entry for each TNS Service Name. To do this, use <a href="#BABFGCHB">Oracle Net Configuration Assistant (NETCA)</a>.</p>
<p>Using NETCA, you can create an entry in the tnsnames.ora file for
each TNS Service Name. NETCA is installed when you install Oracle Net
Services.</p>
</div>
<!-- class="sect3" -->
<a id="BABBCBHH" name="BABBCBHH"></a><a id="ADFNS1138" name="ADFNS1138"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Configuring the Data Source</h4>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
The following configuration steps are for Windows users. Unix users must use the <code>odbc_update_ini.sh</code> file to create a Data Source Name (DSN).</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<p>After installing the Oracle ODBC Driver and <a href="#BABDFDJB">Configuring Oracle Net Services</a>, and before using the Oracle ODBC Driver, you must configure the data source.</p>
<p>Before an application can communicate with the data source, you must
provide configuration information. The configuration information
informs the Oracle ODBC Driver as to which information you want to
access.</p>
<p>The data source consists of the data that you want to access, its
associated operating system, database management system, and network
platform used to access the database management system. The data source
for requests submitted by the Oracle ODBC Driver is an Oracle database
and supports transports available under Oracle Net Services.</p>
<a id="ADFNS1139" name="ADFNS1139"></a>
<p class="subhead2">To configure or add an Oracle data source:</p>
<p>After you have installed the Oracle ODBC Driver, use the ODBC Data
Source Administrator to configure or add an Oracle data source for each
of your Oracle databases. The Oracle ODBC Driver uses the information
you enter when you add the data source to access the data. Follow these
steps:</p>
<ol>
<li>
<p>From the start menu, select Programs, Administrative Tools, Data Sources (ODBC). A list of installed drivers is displayed.</p>
</li>
<li>
<p>Click <span class="bold">Add</span> in the Create New Data Source window and then select the Oracle ODBC Driver in the list of installed drivers.</p>
</li>
<li>
<p>Click <span class="bold">Finish</span>. The <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a>
is displayed. You must enter the Data Source Name and TNS Service Name.
You can provide the other information requested in the dialog box, or
you can leave the fields blank and provide the information when you run
the application.</p>
</li>
<li>
<p>After you have entered the data, click <span class="bold">OK</span> or click <span class="bold">Return</span>.</p>
</li>
</ol>
<p>You can change or delete a data source at any time. The following subtopics explain how to add, change, or delete a data source.</p>
<a id="ADFNS1140" name="ADFNS1140"></a>
<p class="subhead2">To modify an Oracle data source:</p>
<ol>
<li>
<p>From the start menu, select <span class="bold">Programs, Administrative Tools, Data Sources(ODBC)</span>.</p>
</li>
<li>
<p>In the ODBC Data Source Administrator dialog box, select the data source from the Data Sources list and click <span class="bold">Configure</span>. The Oracle ODBC Driver Configuration dialog box is displayed.</p>
</li>
<li>
<p>In the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a>, modify the option values as necessary and click <span class="bold">OK</span>.</p>
</li>
</ol>
<a id="ADFNS1141" name="ADFNS1141"></a>
<p class="subhead2">To delete an Oracle data source:</p>
<ol>
<li>
<p>From the start menu, select <span class="bold">Programs, Administrative Tools, Data Sources(ODBC)</span>.</p>
</li>
<li>
<p>In the ODBC Data Source Administrator dialog box, select the data source you want to delete from the Data Sources list.</p>
</li>
<li>
<p>Click <span class="bold">Remove</span>, and then click <span class="bold">Yes</span> to confirm the deletion.</p>
</li>
</ol>
<a id="ADFNS1142" name="ADFNS1142"></a>
<p class="subhead2">Related Topics</p>
<p><a href="#BABDCHIG">Connecting to an Oracle Data Source</a></p>
<p><a href="#BABCGCCI">Using the Oracle ODBC Driver for the First Time</a></p>
</div>
<!-- class="sect3" -->
<a id="BABEFEHG" name="BABEFEHG"></a><a id="ADFNS1143" name="ADFNS1143"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Oracle ODBC Driver Configuration Dialog Box</h4>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
The Oracle ODBC Driver Configuration dialog box is available for Microsoft Windows users only.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<div class="figure"><a id="BABHIHGI" name="BABHIHGI"></a><a id="ADFNS1144" name="ADFNS1144"></a>
<p class="titleinfigure">Figure 1-3 Oracle ODBC Driver Configuration Dialog Box</p>
<img src="img/setup_app.gif" alt="Description of Figure 1-3 follows" longdesc="img_text/setup_app.htm" /><br />
<a id="sthref5" name="sthref5" href="img_text/setup_app.htm">Description of "Figure 1-3 Oracle ODBC Driver Configuration Dialog Box"</a><br />
<br /></div>
<!-- class="figure" -->
<p>The following list is an explanation of the main setup options and
fields found on the Oracle ODBC Driver Configuration dialog box shown
in the preceding graphic. The tabs found on the lower half of this
dialog box are described in subsequent topics.</p>
<ul>
<li>
<p><span class="bold">Data Source Name</span> - The name used to identify the data source to ODBC. For example, "odbc-pc". You must enter a Data Source Name.</p>
</li>
<li>
<p><span class="bold">Description</span> - A description or comment
about the data in the data source. For example, "Hire date, salary
history, and current review of all employees." The Description field is
optional.</p>
</li>
<li>
<p><span class="bold">TNS Service Name</span> - The location of the Oracle database from which the ODBC driver will retrieve data. This is the same name entered in <a href="#BABDFDJB">Configuring Oracle Net Services</a> using the <a href="#BABFGCHB">Oracle Net Configuration Assistant (NETCA)</a>. For more information, see the NETCA documentation and <a href="#BABCGCCI">Using the Oracle ODBC Driver for the First Time</a>.
The TNS Service Name can be selected from a pull-down list of available
TNS names. For example, "ODBC-PC". You must enter a TNS Service Name.</p>
</li>
<li>
<p><span class="bold">User ID</span> - The user name of the account on the server used to access the data. For example, "scott". The User ID field is optional.</p>
</li>
</ul>
<p>You must enter the Data Source Name and the TNS Service Name. You
can provide the other information requested in the dialog box or you
can leave the fields blank and provide the information when you run the
application.</p>
<p>In addition to the main setup options previously described, there is
a Test Connection button available. The Test Connection button verifies
whether the ODBC environment is configured properly by connecting to
the database specified by the Data Source Name definition. When you
press the Test Connection button, you are prompted for the username and
password.</p>
<p>For an explanation of the Options tabs found on the lower half of
the Oracle ODBC Driver Configuration dialog box, click any of the
following links:</p>
<p><a href="#BABEJFGH">Application Options</a></p>
<p><a href="#BABFJIJI">Oracle Options</a></p>
<p><a href="#BABIFAAB">Workarounds Options</a></p>
<p><a href="#BABDIDEF">SQL Server Migration Options</a></p>
<a id="BABEJFGH" name="BABEJFGH"></a><a id="ADFNS1145" name="ADFNS1145"></a>
<p class="subhead2">Application Options</p>
<div class="figure"><a id="BABIDJBI" name="BABIDJBI"></a><a id="ADFNS1146" name="ADFNS1146"></a>
<p class="titleinfigure">Figure 1-4 The Application Options Tab of the Oracle ODBC Driver Configuration Dialog Box</p>
<img src="img/setup_app.gif" alt="Description of Figure 1-4 follows" longdesc="img_text/setup_app.htm" /><br />
<a id="sthref6" name="sthref6" href="img_text/setup_app.htm">Description of "Figure 1-4 The Application Options Tab of the Oracle ODBC Driver Configuration Dialog Box"</a><br />
<br /></div>
<!-- class="figure" -->
<p>The following list is an explanation of the fields found on the Application Options tab shown in the preceding graphic:</p>
<ul>
<li>
<p><span class="bold">Enable Result Sets</span> - Enables the
processing of Oracle Result Sets. If Result Sets are not required for
your application, Result Set support can be disabled. There is a small
performance penalty for procedures called from packages not containing
Result Sets. Result Sets are enabled by default.</p>
</li>
<li>
<p><span class="bold">Enable Query Timeout</span> - Enables query timeout for SQL queries. By default, the Oracle ODBC Driver supports the <code>SQL_ATTR_QUERY_TIMEOUT</code>
attribute for the SQLSetStmtAttr function. If this box is not checked,
the Oracle ODBC Driver responds with a "not capable" message. Query
Timeout is enabled by default.</p>
</li>
<li>
<p><span class="bold">Read-Only Connection</span> - Check this box to force read-only access. The default is write access.</p>
</li>
<li>
<p><span class="bold">Enable Closing Cursors</span> - Enables closing
cursors. By default, closing cursors is disabled (the field is empty),
meaning a call to close a cursor does not force the closing of OCI
cursors when this behavior is not desired because it can cause an
unnecessary performance hit. You should enable closing cursors when it
is desirable to force the closing of OCI cursors upon a call to close a
cursor.</p>
</li>
</ul>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
There is an impact on performance each time a cursor is closed.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<ul>
<li>
<p><span class="bold">Enable Thread Safety</span> - Thread safety can
be disabled for a data source. If thread safety is not required,
disabling this option eliminates the overhead of using thread safety.
By default, thread safety is enabled.</p>
</li>
<li>
<p><span class="bold">Batch Autocommit Mode</span> - By default, commit is executed only if all statements succeed.</p>
</li>
<li>
<p><span class="bold">Numeric Settings</span> - Allows you to choose
which numeric settings will be used to determine the decimal and group
separator characters when receiving and returning numeric data that is
bound as strings. This option allows you to choose Oracle NLS settings
(the default setting), Microsoft default regional settings (to provide
a way to mirror the Oracle OLE DB driver's behavior for greater
interoperability), or US numeric settings (which are necessary when
using MS Access or DAO (Database Access Objects) in non-US
environments).</p>
</li>
</ul>
<p>The main configuration setup options are described in the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a> topic.</p>
<a id="BABFJIJI" name="BABFJIJI"></a><a id="ADFNS1147" name="ADFNS1147"></a>
<p class="subhead2">Oracle Options</p>
<div class="figure"><a id="BABGBICJ" name="BABGBICJ"></a><a id="ADFNS1148" name="ADFNS1148"></a>
<p class="titleinfigure">Figure 1-5 The Oracle Options Tab of the Oracle ODBC Driver Configuration Dialog Box</p>
<img src="img/setup_ora.gif" alt="Description of Figure 1-5 follows" longdesc="img_text/setup_ora.htm" /><br />
<a id="sthref7" name="sthref7" href="img_text/setup_ora.htm">Description of "Figure 1-5 The Oracle Options Tab of the Oracle ODBC Driver Configuration Dialog Box"</a><br />
<br /></div>
<!-- class="figure" -->
<p>The following list is an explanation of the fields found on the Oracle Options tab shown in the preceding graphic:</p>
<ul>
<li>
<p><span class="bold">Fetch Buffer Size</span> - The amount of memory
used to determine how many rows of data the ODBC Driver will pre-fetch
at a time from an Oracle database regardless of the number of rows the
application program requests in a single query. However, the number of
pre-fetched rows depends on the width and number of columns specified
in a single query. Applications that typically fetch fewer than 20 rows
of data at a time will see an improvement in response time,
particularly over slow network connections or to heavily loaded
servers. Setting this too high can actually make response time worse or
consume large amounts of memory.</p>
</li>
</ul>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
When <code>LONG</code> and LOB data types
are present, the number of rows pre-fetched by the ODBC Driver is not
determined by the Fetch Buffer Size. The inclusion of the <code>LONG</code>
and LOB data types minimizes the performance improvement and could
result in excessive memory use. The ODBC Driver will disregard the
Fetch Buffer Size and only pre-fetch a set number of rows in the
presence of the <code>LONG</code> and LOB data types.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<ul>
<li>
<p><span class="bold">Enable LOBs</span> - Enables the writing of
Oracle LOBs. If writing Oracle LOBs is not required for your
application, LOB support can be disabled. There is a small performance
penalty for insert and update statements when LOBs are enabled. LOB
writing is enabled by default but disabled for Oracle databases that do
not support the LOB data type.</p>
</li>
<li>
<p><span class="bold">Enable Statement Caching</span> - Enables
statement caching feature, which will increase the performance of
parsing the query, in case the user has to parse the same text of query
and related parameters multiple times. The default is disabled.</p>
</li>
<li>
<p><span class="bold">Cache Buffer Size</span> - The statement cache has a maximum size (number of statements) that can be modified by an attribute on the service context, <code>OCI_ATTR_STMTCACHESIZE</code>.
The default cache buffer size is 20 that get used only if statement
caching option is enabled. Setting cache buffer size to 0 will disable
statement caching feature.</p>
</li>
<li>
<p><span class="bold">Max Token Size</span> - Sets the token size to
the nearest multiple of 1 KB (1024 bytes) beginning at 4 KB (4096
bytes). The default size is 8 KB (8192 bytes). The maximum value that
can be set is 128 KB (131068 bytes).</p>
</li>
<li>
<p><span class="bold">Translate ORA errors</span> - Any migrated third
party ODBC application, which is using the Babelfish feature, expects
that errors returned by the server to be in their native database
format, then users can enable this option to receive native errors
based on the error translation registered with SQL Translation Profile.</p>
</li>
<li>
<p><span class="bold">Convert Empty String</span> - Any third party
ODBC application that is migrated to Oracle Database requires handling
empty string data (Oracle Database does not handle empty string data in
table columns), then they can enable this option so that the
application can insert empty string data or retrieve empty string data.</p>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
This feature is not implemented for release 12.1.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
</li>
</ul>
<p>The Failover area of the Oracle Options tab contains the following fields:</p>
<ul>
<li>
<p><span class="bold">Enable Failover</span> - Enables Oracle Fail Safe
and Oracle Parallel Server failover retry. This option in an
enhancement to the failover capabilities of Oracle Fail Safe and Oracle
Parallel Server. Enable this option to configure additional failover
retries. The default is enabled.</p>
</li>
<li>
<p><span class="bold">Retry</span> - The number of times the connection failover will be attempted. The default is 10 attempts.</p>
</li>
<li>
<p><span class="bold">Delay</span> - The number of seconds to delay between failover attempts. The default is 10 seconds.</p>
</li>
</ul>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
See the Oracle Fail Safe and Oracle Parallel Server documentation on how to set up and use both of these products.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<p>The main configuration setup options are described in the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a> topic.</p>
<a id="BABIFAAB" name="BABIFAAB"></a><a id="ADFNS1149" name="ADFNS1149"></a>
<p class="subhead2">Workarounds Options</p>
<div class="figure"><a id="BABIGDJC" name="BABIGDJC"></a><a id="ADFNS1150" name="ADFNS1150"></a>
<p class="titleinfigure">Figure 1-6 The Workarounds Options Tab of the Oracle ODBC Driver Configuration Dialog Box</p>
<img src="img/setup_work.gif" alt="Description of Figure 1-6 follows" longdesc="img_text/setup_work.htm" /><br />
<a id="sthref8" name="sthref8" href="img_text/setup_work.htm">Description of "Figure 1-6 The Workarounds Options Tab of the Oracle ODBC Driver Configuration Dialog Box"</a><br />
<br /></div>
<!-- class="figure" -->
<p>The following list is an explanation of the fields found on the Workarounds Options tab shown in the preceding graphic:</p>
<ul>
<li>
<p><span class="bold">Bind TIMESTAMP as DATE</span> - Check this box to force the Oracle ODBC Driver to bind <code>SQL_TIMESTAMP</code> parameters as the Oracle <code>DATE</code> type instead of as the Oracle <code>TIMESTAMP</code> type (the default). For more information, see <a href="#BABBAEEH">Implementation of Data Types (Advanced)</a>.</p>
</li>
<li>
<p><span class="bold">Force SQL_WCHAR Support</span> - Check this box to enable SQLDescribeCol, SQLColumns, and SQLProcedureColumns to unconditionally return the data type of <code>SQL_WCHAR</code> for <code>SQL_CHAR</code> columns; <code>SQL_WVARCHAR</code> for <code>SQL_VARCHAR</code> columns; and <code>SQL_WLONGVARCHAR</code> for <code>SQL_LONGVARCHAR</code>
columns. This feature enables Unicode support in applications that rely
on the results of these ODBC calls (for example, ADO). This support is
disabled by default.</p>
</li>
<li>
<p><span class="bold">Disable Microsoft Transaction Server</span> - Clear the check in this box to enable Microsoft Transaction Server (MTS) support. By default, MTS support is disabled.</p>
</li>
<li>
<p><span class="bold">Set Metadata Id Default to SQL_TRUE</span> - Check this box to change the default value of the <code>SQL_ATTR_METADATA_ID</code> connection and statement attribute at connection time to be <code>SQL_TRUE</code>. Under normal circumstances, <code>SQL_ATTR_METADATA_ID</code> would default to <code>SQL_FALSE</code>.
ODBC calls made by the application to specifically change the value of
the attribute after connection time will be unaffected by this option
and will complete their functions as expected. By default, this option
is off. The <a href="#BABHEGBH">Implementation of ODBC API Functions</a> topic provides some additional information about the <code>SQL_ATTR_METADATA_ID</code> attribute.</p>
</li>
<li>
<p><span class="bold">Pre-fetch size for LONG column data</span> - Set this value to prefetch <code>LONG</code> or <code>LONG RAW</code>
data to improve performance of ODBC applications. This enhancement
improves the performance of Oracle ODBC driver up to 10 times,
depending on the pre-fetch size set by the user. The default value is
0. The maximum value that you can set is 64KB (65536 bytes).</p>
<p>If the value of pre-fetch size is set to some value greater than 65536, the data fetched will only be 65536 bytes. If you have <code>LONG</code> or <code>LONG RAW</code>
data in the database that is greater that 65536 bytes, the pre-fetch
size should be set to 0 (the default value), which will result in
single row fetch and complete <code>LONG</code> data can be fetched.
If you pass a buffer size less than the pre-fetch size in non-polling
mode, a data truncation error will occur if the <code>LONG</code> data size in the database is greater than the buffer size.</p>
</li>
<li>
<p><span class="bold">Disable SQLDescribeParam</span> - If the SQLDescribeParam function is enabled, the <code>SQL_VARCHAR</code> data type is returned for all parameters. If the Force SQL_WCHAR Support function is also enabled, the <code>SQL_WVARCHAR</code> data type is returned for all parameters. By default, this function is enabled.</p>
</li>
<li>
<p><span class="bold">Bind NUMBER as FLOAT</span> - Check this box to force the Oracle ODBC Driver to bind <code>NUMBER</code> column containing <code>FLOAT</code> data as Float instead of as the Binary Float (the default).</p>
</li>
<li>
<p><span class="bold">Disable RULE Hint</span> - Clear the check in this box to enable RULE Hint specified with catalogue queries. By default, RULE Hint option is disabled.</p>
</li>
<li>
<p><span class="bold">Use OCIDescribeAny</span> - Check this box to gain a performance improvement by forcing the driver to use <code>OCIDescribeAny()</code>when an application makes heavy calls to small packaged procedures that return <code>REF CURSORS</code>.</p>
</li>
</ul>
<p>The main configuration setup options are described in the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a> topic.</p>
<a id="BABDIDEF" name="BABDIDEF"></a><a id="ADFNS1151" name="ADFNS1151"></a>
<p class="subhead2">SQL Server Migration Options</p>
<div class="figure"><a id="BABIIBJE" name="BABIIBJE"></a><a id="ADFNS1152" name="ADFNS1152"></a>
<p class="titleinfigure">Figure 1-7 The SQL Server Migration Options Tab of the Oracle ODBC Driver Configuration Dialog Box</p>
<img src="img/setup_ssmig.gif" alt="Description of Figure 1-7 follows" longdesc="img_text/setup_ssmig.htm" /><br />
<a id="sthref9" name="sthref9" href="img_text/setup_ssmig.htm">Description of "Figure 1-7 The SQL Server Migration Options Tab of the Oracle ODBC Driver Configuration Dialog Box"</a><br />
<br /></div>
<!-- class="figure" -->
<p>The following list is an explanation of the fields found on the SQL Server Migration Options tab shown in the preceding graphic:</p>
<ul>
<li>
<p><span class="bold">EXEC Syntax Enabled</span> - enables support for
SQL Server EXEC syntax. A procedure (or function) call specified in an
EXEC statement is translated to its equivalent Oracle procedure (or
function) call before being processed by an Oracle database server. By
default this option is disabled.</p>
</li>
<li>
<p><span class="bold">Schema</span> - the translated Oracle procedure
(or function) is assumed to be defined in the user's default schema.
However, if all procedures (or functions) from the same SQL Server
database are migrated to the same Oracle schema with their database
name as the schema name, this field should be set to database. By the
same token, this field should be set to owner if all procedures (or
functions) owned by the same SQL Server user are defined in the same
Oracle schema. This field is empty by default.</p>
</li>
</ul>
<p>The main configuration setup options are described in the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a> topic.</p>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABHABJB" name="BABHABJB"></a><a id="ADFNS1153" name="ADFNS1153"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Modifying the oraodbc.ini File</h3>
<ul>
<li>
<p><a href="#BABEGGIB">Reducing Lock Timeout</a></p>
</li>
</ul>
<a id="BABEGGIB" name="BABEGGIB"></a><a id="ADFNS1154" name="ADFNS1154"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Reducing Lock Timeout</h4>
<p>An Oracle server waits indefinitely for lock conflicts between
transactions to be resolved. You can limit the amount of time that an
Oracle server waits for locks to be resolved by setting the Oracle ODBC
Driver's <code>LockTimeOut</code> entry in the <code>oraodbc.ini</code> file. The value you enter for the <code>LockTimeOut</code>
parameter is the number of seconds after which an Oracle server will
time out if it cannot obtain the requested locks. In the following
example, the Oracle server will time out after 60 seconds:</p>
<pre xml:space="preserve" class="oac_no_warn">[Oracle ODBC Driver Common]<br />LockTimeOut=60<br /></pre></div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABDBDJG" name="BABDBDJG"></a><a id="ADFNS1155" name="ADFNS1155"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Connecting to a Data Source</h3>
<ul>
<li>
<p><a href="#BABDCHIG">Connecting to an Oracle Data Source</a></p>
</li>
</ul>
<a id="BABDCHIG" name="BABDCHIG"></a><a id="ADFNS1156" name="ADFNS1156"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Connecting to an Oracle Data Source</h4>
<p>To connect to a <a href="#BABFICBD">Data Source</a>, the Oracle ODBC
Driver requires that the OCI client software be installed on your
computer and the corresponding listener be running on the Oracle
server. Oracle Net Services for Windows is a Dynamic Linked Library
(DLL) based application. For more information about Oracle Net
Services, see the Oracle Net Services documentation.</p>
<p>As part of the connection process, an application can prompt you for
information. If an application prompts you for information about an
Oracle data source, do the following:</p>
<ol>
<li>
<p>In the TNS Service Name box, enter the name of the TNS service.</p>
</li>
<li>
<p>In the User Name box, enter the name you use to access an Oracle Database.</p>
</li>
<li>
<p>In the Password box, enter the password you use to access an Oracle Database.</p>
</li>
<li>
<p>Click <span class="bold">OK</span>.</p>
</li>
</ol>
<p>An application must connect to a data source in order to access the
data in it. Different applications connect to data sources at different
times. For example, an application might connect to a data source only
at your request, or it might connect automatically when it starts. For
information about when an application connects to a data source, see
the documentation for that application.</p>
<p>For additional information, click any of the following links.</p>
<a id="ADFNS1157" name="ADFNS1157"></a>
<p class="subhead2">Related Topic for All Users</p>
<p><a href="#BABBCBHH">Configuring the Data Source</a></p>
<a id="ADFNS1158" name="ADFNS1158"></a>
<p class="subhead2">Related Topics for Programmers</p>
<p><a href="#BABIDEDG">SQLDriverConnect Implementation</a></p>
<p><a href="#BABFHDGC">Data Source Configuration Options</a></p>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABCHDJE" name="BABCHDJE"></a><a id="ADFNS1159" name="ADFNS1159"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Troubleshooting</h3>
<ul>
<li>
<p><a href="#BABCGCCI">Using the Oracle ODBC Driver for the First Time</a></p>
</li>
<li>
<p><a href="#BABGAIIC">Expired Password</a></p>
</li>
</ul>
<a id="BABCGCCI" name="BABCGCCI"></a><a id="ADFNS1160" name="ADFNS1160"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Using the Oracle ODBC Driver for the First Time</h4>
<p>Note: This information is for Microsoft Windows users only.</p>
<p>The following problems may occur when you use the Oracle ODBC Driver
for the first time and attempt to attach to a database or table:</p>
<ul>
<li>
<p><code><span class="codeinlineitalic">OracleHome</span></code><code>/bin</code> must be in the system path for the Oracle ODBC Driver to function successfully</p>
<p>This can be verified by typing <code>PATH</code> from a command prompt.</p>
</li>
<li>
<p>Your machine or server system is missing required software</p>
<br />
<span class="bold">Problem:</span> Oracle ODBC Driver fails to work.<br />
<span class="bold">Cause:</span> Either OCI software is not installed on your machine or Oracle database software is not installed on your server system.<br />
<span class="bold">Recommended Action:</span> Install the required OCI software on your client machine or Oracle database software on your server system or both.</li>
<li>
<p>Client/Server connectivity is incorrectly set up on your machine</p>
<br />
<span class="bold">Problem:</span> Cannot connect to the server system from your machine.<br />
<span class="bold">Cause:</span> Either the required transport software is not installed or is not configured correctly.<br />
<span class="bold">Recommended Action:</span> As a test, for example
when using TCP/IP, make sure that your machine can ping to the server
where the Oracle database resides. Use <code>tnsping.exe</code> located in the <code>/orant/bin</code> or <code>/Oracle/ora90/bin</code> directory to ensure connectivity to a specific database service. For example:<br />
<code>C:/ORANT/BIN&gt;tnsping database-service-name</code></li>
<li>
<p>TNS Service Name does not match the name that was entered in the Oracle Net Configuration Assistant (NETCA)</p>
<br />
<span class="bold">Problem:</span> The user is returned an error message about the TNS Service Name while attempting to set up the Oracle ODBC Driver.<br />
<span class="bold">Cause:</span> The TNS Service Name does not match the name entered in NETCA.<br />
<span class="bold">Recommended Action:</span> Change the TNS Service Name in the Oracle ODBC Driver setup window to match the TNS Service Name in NETCA.
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
Refer to the pull-down menu on the Datasource Configuration screen to
view a list of all valid TNS service names on the system as well as
names entered by users.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
</li>
<li>
<p>User does not have access to the database</p>
<br />
<span class="bold">Problem:</span> The user is returned an access
denied error message when attempting to gain access to an Oracle
database while using the Oracle ODBC Driver.<br />
<span class="bold">Cause:</span> You do not have access to an Oracle database.<br />
<span class="bold">Recommended Action:</span> Ensure the proper privileges are assigned for the user to gain access to the Oracle database.</li>
<li>
<p>System Error: 182 or 193 when trying to create an ODBC DSN</p>
<br />
<span class="bold">Problem:</span> You are trying to create a Data
Source Name with the ODBC Administrator but, after selecting the Oracle
ODBC Driver, you received a System Error 182 or System Error 193.<br />
<span class="bold">Cause:</span> This error is due to a mismatch in the <code>mfc42.dll</code> provided by Microsoft.<br />
<span class="bold">Recommended Action:</span> Verified that <code><span class="codeinlineitalic">OracleHome</span></code><code>/bin</code> is in your <code>PATH</code> and that you have no duplicate <code>oci.dll</code> outside the <code><span class="codeinlineitalic">OracleHome</span></code><code>/bin</code>. If you still receive the error, copy the <code>mfc42.dll</code> from a working machine to the machine with the problem.</li>
<li>
<p>Translator Library could not be loaded system error code 31</p>
<br />
<span class="bold">Problem:</span> On Windows with the Oracle ODBC
Driver in the Microsoft ODBC Administrator trying to Add or Delete an
Oracle ODBC DSN, you get the error "Translator Library could not be
loaded System error code 31."<br />
<span class="bold">Cause:</span> In some cases, Windows machines may contain an outdated version of the <code>mfc42.dll</code>. Oracle ODBC is built against the newer version of <code>mfc42.dll</code> (specifically, version 6.0.8665.0).<br />
<span class="bold">Recommended Action:</span> An outdated version of <code>mfc42.dll</code>
is causing this error to occur. Installing MDAC 2.5 SP1 does not take
care of this problem. You need to obtain a newer version of the <code>mfc42.dll</code> (version 6.0.8665.0 or higher) and replace the outdated <code>mfc42.dll</code> in the <code><span class="codeinlineitalic">%</span></code><code>WINNT</code><code>%/System32</code> directory by:<br />
-- Copying from another machine that works correctly<br />
-- Contact Microsoft to find out how to obtain the dll</li>
</ul>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
This dll is a Microsoft DLL.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
</div>
<!-- class="sect3" -->
<a id="BABGAIIC" name="BABGAIIC"></a><a id="ADFNS1161" name="ADFNS1161"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Expired Password</h4>
<p>This section contains information about expired passwords.</p>
<a id="ADFNS1162" name="ADFNS1162"></a>
<p class="subhead2">Expired Password Behavior</p>
<p>If you try to connect to the database and your password has expired,
you are prompted to change your password. Upon making a successful
password change, you will be connected to the database. However, if you
try to connect to the database with a <code>SQLDriverConnect</code> call with a <code>SQL_DRIVER_NOPROMPT</code>
parameter value, the Oracle ODBC Driver will not prompt you for the
password change. Instead, an error condition results, producing an
error message and number that indicates that the password has expired.</p>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" --></div>
<!-- class="sect1" -->
<a id="BABGFHBE" name="BABGFHBE"></a><a id="ADFNS1163" name="ADFNS1163"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">For Advanced Users</h2>
<ul>
<li>
<p><a href="#BABCHHFC">Creating Oracle ODBC Driver TNS Service Names</a></p>
</li>
<li>
<p><a href="#BABIDECD">SQL Statements</a></p>
</li>
<li>
<p><a href="#BABCIACJ">Data Types</a></p>
</li>
<li>
<p><a href="#BABBAEEH">Implementation of Data Types (Advanced)</a></p>
</li>
<li>
<p><a href="#BABBJIBH">Limitations on Data Types</a></p>
</li>
<li>
<p><a href="#BABCCDFB">Error Messages</a></p>
</li>
</ul>
<a id="BABCHHFC" name="BABCHHFC"></a><a id="ADFNS1164" name="ADFNS1164"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Creating Oracle ODBC Driver TNS Service Names</h3>
<p>To create Oracle ODBC Driver TNS Service Names with Oracle Net
Services, use the Oracle Net Configuration Assistant (NETCA), which is
installed when you install Oracle Net Services. NETCA creates Oracle
ODBC Driver TNS Service Name entries in the tnsnames.ora file.</p>
</div>
<!-- class="sect2" -->
<a id="BABIDECD" name="BABIDECD"></a><a id="ADFNS1165" name="ADFNS1165"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">SQL Statements</h3>
<p>The Oracle ODBC Driver is broadly compatible with the SQL-99 Core
specification which is a superset of the SQL-92 Entry Level
specification. In addition to Oracle's grammar, the vendor-specific
escape sequences outlined in Appendix C of the ODBC specifications are
also supported. In accordance with the design of ODBC, the Oracle ODBC
Driver will pass native SQL syntax to the Oracle database.</p>
<a id="ADFNS1166" name="ADFNS1166"></a>
<p class="subhead2">Related Topic for Advanced Users</p>
<p><a href="#BABCIACJ">Data Types</a></p>
<a id="ADFNS1167" name="ADFNS1167"></a>
<p class="subhead2">Related Topic for Programmers</p>
<p><a href="#BABIDAGF">Implementation of the ODBC SQL Syntax</a></p>
</div>
<!-- class="sect2" -->
<a id="BABCIACJ" name="BABCIACJ"></a><a id="ADFNS1168" name="ADFNS1168"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Data Types</h3>
<p>The Oracle ODBC Driver maps Oracle database data types to ODBC SQL data types.</p>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
All conversions in Appendix D of the <span class="italic">Microsoft ODBC 3.52 Software Development Kit and Programmer's Reference</span> are supported for the ODBC SQL data types listed from a call to <code>SQLGetInfo</code> with the appropriate information type.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<a id="ADFNS1169" name="ADFNS1169"></a>
<p class="subhead2">Related Topics for Advanced Users</p>
<p><a href="#BABBAEEH">Implementation of Data Types (Advanced)</a></p>
<p><a href="#BABBJIBH">Limitations on Data Types</a></p>
<p><a href="#BABIDECD">SQL Statements</a></p>
<a id="ADFNS1170" name="ADFNS1170"></a>
<p class="subhead2">Related Topic for Programmers</p>
<p><a href="#BABEJGEG">Implementation of Data Types (Programming)</a></p>
</div>
<!-- class="sect2" -->
<a id="BABBAEEH" name="BABBAEEH"></a><a id="ADFNS1171" name="ADFNS1171"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Implementation of Data Types (Advanced)</h3>
<ul>
<li>
<p><a href="#BABDCGGG">DATE and TIMESTAMP</a></p>
</li>
<li>
<p><a href="#BABBCEHB">Floating Point Data Types</a></p>
</li>
</ul>
<a id="BABDCGGG" name="BABDCGGG"></a><a id="ADFNS1172" name="ADFNS1172"></a>
<p class="subhead2">DATE and TIMESTAMP</p>
<p>The semantics of Oracle <code>DATE</code> and <code>TIMESTAMP</code> data types do not correspond exactly with the ODBC data types with the same names. The Oracle <code>DATE</code> data type contains both date and time information while the <code>SQL_DATE</code> data type contains only date information. The Oracle <code>TIMESTAMP</code>
data type also contains date and time information, but it has greater
precision in fractional seconds. The ODBC Driver reports the data types
of both Oracle <code>DATE</code> and <code>TIMESTAMP</code> columns as <code>SQL_TIMESTAMP</code> to prevent information loss. Similarly the ODBC Driver binds <code>SQL_TIMESTAMP</code> parameters as Oracle <code>TIMESTAMP</code> values.</p>
<a id="BABBCEHB" name="BABBCEHB"></a><a id="ADFNS1173" name="ADFNS1173"></a>
<p class="subhead2">Floating Point Data Types</p>
<p>When connected to a 10.1 or later Oracle server, the ODBC Driver maps the Oracle floating point data types <code>BINARY_FLOAT</code> and <code>BINARY_DOUBLE</code> to the ODBC data types <code>SQL_REAL</code> and <code>SQL_DOUBLE</code>, respectively. In previous releases, <code>SQL_REAL</code> and <code>SQL_DOUBLE</code> mapped to the generic Oracle numeric data type.</p>
<a id="ADFNS1174" name="ADFNS1174"></a>
<p class="subhead2">Related Topic</p>
<p><a href="#BABDHDBB">DATE and TIMESTAMP Data Types</a></p>
</div>
<!-- class="sect2" -->
<a id="BABBJIBH" name="BABBJIBH"></a><a id="ADFNS1175" name="ADFNS1175"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Limitations on Data Types</h3>
<p>The Oracle ODBC Driver and the Oracle database impose limitations on data types. <a href="#BABIJGDI">Table 1-3</a> describes these limitations.</p>
<div class="tblformal"><a id="ADFNS1176" name="ADFNS1176"></a><a id="sthref10" name="sthref10"></a><a id="BABIJGDI" name="BABIJGDI"></a>
<p class="titleintable">Table 1-3 Limitations Imposed on Data Types by the Oracle ODBC Driver and Oracle Database</p>
<table class="Formal" title="Limitations Imposed on Data Types by the Oracle ODBC Driver and Oracle Database" summary="This table describes the limitations on data types imposed by the Oracle ODBC Driver and Oracle database." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="31%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t13" align="left" valign="bottom">Limited Data Type</th>
<th id="r1c2-t13" align="left" valign="bottom">Description</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t13" headers="r1c1-t13" align="left">
<p>Literals</p>
</td>
<td headers="r2c1-t13 r1c2-t13" align="left">
<p>Oracle database limits literals in SQL statements to 4,000 bytes.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t13" headers="r1c1-t13" align="left">
<p>SQL_LONGVARCHAR and SQL_WLONGVARCHAR</p>
</td>
<td headers="r3c1-t13 r1c2-t13" align="left">
<p>Oracle's limit for <code>SQL_LONGVARCHAR</code> data where the column type is <code>LONG</code> is 2,147,483,647 bytes. Oracle's limit for the <code>SQL_LONGVARCHAR</code> data where the column type is <code>CLOB</code> is 4 gigabytes. The limiting factor is the client workstation memory.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t13" headers="r1c1-t13" align="left">
<p>SQL_LONGVARCHAR and SQL_LONGVARBINARY</p>
</td>
<td headers="r4c1-t13 r1c2-t13" align="left">
<p>Oracle database allows only a single long data column per table. The long data types are <code>SQL_LONGVARCHAR</code> (<code>LONG</code>) and <code>SQL_LONGVARBINARY</code> (<code>LONG RAW</code>). Oracle recommends you use <code>CLOB</code> and <code>BLOB</code> columns instead. There is no restriction on the number of <code>CLOB</code> and <code>BLOB</code> columns in a table.</p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" --></div>
<!-- class="sect2" -->
<a id="BABCCDFB" name="BABCCDFB"></a><a id="ADFNS1177" name="ADFNS1177"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Error Messages</h3>
<p>When an error occurs, the Oracle ODBC Driver returns the native error number, the <code>SQLSTATE</code>
(an ODBC error code), and an error message. The driver derives this
information both from errors detected by the driver and errors returned
by the Oracle server.</p>
<a id="ADFNS1178" name="ADFNS1178"></a>
<p class="subhead2">Native Error</p>
<p>For errors that occur in the data source, the Oracle ODBC Driver
returns the native error returned to it by the Oracle server. When the
Oracle ODBC Driver or the Driver Manager detects an error, the Oracle
ODBC Driver returns a native error of zero.</p>
<a id="ADFNS1179" name="ADFNS1179"></a>
<p class="subhead2">SQLSTATE</p>
<p>For errors that occur in the data source, the Oracle ODBC Driver maps the returned native error to the appropriate <code>SQLSTATE</code>. When the Oracle ODBC Driver detects an error, it generates the appropriate <code>SQLSTATE</code>. When the Driver Manager detects an error, it generates the appropriate <code>SQLSTATE</code>.</p>
<a id="ADFNS1180" name="ADFNS1180"></a>
<p class="subhead2">Error Message</p>
<p>For errors that occur in the data source, the Oracle ODBC Driver
returns an error message based on the message returned by the Oracle
server. For errors that occur in the Oracle ODBC Driver or the Driver
Manager, the Oracle ODBC Driver returns an error message based on the
text associated with the <code>SQLSTATE</code>.</p>
<p>Error messages have the following format:</p>
<pre xml:space="preserve" class="oac_no_warn">[vendor] [ODBC-component] [data-source] error-message<br /></pre>
<p>The prefixes in brackets ( [ ] ) identify the source of the error. <a href="#BABFICEJ">Table 1-4</a>
shows the values of these prefixes returned by the Oracle ODBC Driver.
When the error occurs in the data source, the [vendor] and
[ODBC-component] prefixes identify the vendor and name of the ODBC
component that received the error from the data source.</p>
<div class="tblformal"><a id="ADFNS1181" name="ADFNS1181"></a><a id="sthref11" name="sthref11"></a><a id="BABFICEJ" name="BABFICEJ"></a>
<p class="titleintable">Table 1-4 Error Message Values of Prefixes Returned by the Oracle ODBC Driver</p>
<table class="Formal" title="Error Message Values of Prefixes Returned by the Oracle ODBC Driver" summary="This table shows the values of prefixes returned by the Oracle ODBC Driver based on three possible error sources." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="33%" />
<col width="27%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t14" align="left" valign="bottom">Error Source</th>
<th id="r1c2-t14" align="left" valign="bottom">Prefix</th>
<th id="r1c3-t14" align="left" valign="bottom">Value</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t14" headers="r1c1-t14" align="left">
<p>Driver Manager</p>
</td>
<td headers="r2c1-t14 r1c2-t14" align="left">
<p>[vendor][ODBC-component][data-source]</p>
</td>
<td headers="r2c1-t14 r1c3-t14" align="left">
<p>[Microsoft/unixODBC][ODBC Driver Manager]N/A</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t14" headers="r1c1-t14" align="left">
<p>Oracle ODBC Driver</p>
</td>
<td headers="r3c1-t14 r1c2-t14" align="left">
<p>[vendor][ODBC-component][data-source]</p>
</td>
<td headers="r3c1-t14 r1c3-t14" align="left">
<p>[ORACLE][ODBC Driver]N/A</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t14" headers="r1c1-t14" align="left">
<p>Oracle server</p>
</td>
<td headers="r4c1-t14 r1c2-t14" align="left">
<p>[vendor][ODBC-component][data-source]</p>
</td>
<td headers="r4c1-t14 r1c3-t14" align="left">
<p>[ORACLE][ODBC Driver]N/A</p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" -->
<p>For example, if the error message does not contain the [Ora] prefix
shown in the following format, the error is an Oracle ODBC Driver error
and should be self-explanatory.</p>
<pre xml:space="preserve" class="oac_no_warn">[Oracle][ODBC]Error message text here <br /></pre>
<p>If the error message contains the [Ora] prefix shown in the
following format, it is not an Oracle ODBC Driver error. Note that
although the error message contains the [Ora] prefix, the actual error
may be coming from one of several sources.</p>
<pre xml:space="preserve" class="oac_no_warn">[Oracle][ODBC][Ora]Error message text here <br /></pre>
<p>If the error message text starts with the following prefix, you can
obtain more information about the error in the Oracle server
documentation.</p>
<pre xml:space="preserve" class="oac_no_warn">ORA-<br /></pre>
<p>Oracle Net Services errors and Trace logging are located under the <code><span class="codeinlineitalic">ORACLE_HOME</span></code><code>\NETWORK</code> directory on Windows systems or the <code><span class="codeinlineitalic">ORACLE_HOME</span></code><code>/NETWORK</code>
directory on UNIX systems where the OCI software is installed and
specifically in the log and trace directories respectively. Database
logging is located under the <code><span class="codeinlineitalic">ORACLE_HOME</span></code><code>\RDBMS</code> directory on Windows systems or the <code><span class="codeinlineitalic">ORACLE_HOME</span></code><code>/rdbms</code> directory on UNIX systems where the Oracle server software is installed.</p>
<p>See the Oracle server documentation for more information about server error messages.</p>
</div>
<!-- class="sect2" --></div>
<!-- class="sect1" -->
<a id="BABICHJC" name="BABICHJC"></a><a id="ADFNS1182" name="ADFNS1182"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">For Programmers</h2>
<ul>
<li>
<p><a href="#BABIJAGI">Format of the Connection String</a></p>
</li>
<li>
<p><a href="#BABIDEDG">SQLDriverConnect Implementation</a></p>
</li>
<li>
<p><a href="#BABCEBFI">Reducing Lock Timeout in a Program</a></p>
</li>
<li>
<p><a href="#BABJJDBG">Linking with odbc32.lib (Windows) or libodbc.so (UNIX)</a></p>
</li>
<li>
<p><a href="#BABCEAIA">Obtaining Information About rowids</a></p>
</li>
<li>
<p><a href="#BABBBDEJ">Rowids in a WHERE Clause</a></p>
</li>
<li>
<p><a href="#BABHGBFJ">Enabling Result Sets</a></p>
</li>
<li>
<p><a href="#BABJCDGE">Enabling EXEC Syntax</a></p>
</li>
<li>
<p><a href="#BABGJGBA">Enabling Event Notification for Connection Failures in an Oracle RAC Environment</a></p>
</li>
<li>
<p><a href="#BABDDGBE">Using Implicit Results Feature Through ODBC</a></p>
</li>
<li>
<p><a href="#BABBCCDD">Supported Functionality</a></p>
</li>
<li>
<p><a href="#BABIFIGA">Unicode Support</a></p>
</li>
<li>
<p><a href="#BABIIAIH">Performance and Tuning</a></p>
</li>
</ul>
<a id="BABIJAGI" name="BABIJAGI"></a><a id="ADFNS1183" name="ADFNS1183"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Format of the Connection String</h3>
<p><a href="#BABEFBFB">Table 1-5</a> describes keywords that can be included in the connection string argument of the <code>SQLDriverConnect</code>
function call. Missing keywords will be read from the Administrator
entry for the data source. Values specified in the connection string
will override those contained in the Administrator entry. See the <span class="italic">Microsoft ODBC 3.52 Software Development Kit and Programmer's Reference</span> for more information about the <code>SQLDriverConnect</code> function.</p>
<div class="tblformal"><a id="ADFNS1184" name="ADFNS1184"></a><a id="sthref12" name="sthref12"></a><a id="BABEFBFB" name="BABEFBFB"></a>
<p class="titleintable">Table 1-5 Keywords that Can Be Included in the Connection String Argument of the SQLDriverConnect Function Call</p>
<table class="Formal" title="Keywords that Can Be Included in the Connection String Argument of the SQLDriverConnect Function Call" summary="This table describes the keywords that can be included in the connection string of the SQLDrivedConnect function." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="13%" />
<col width="37%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t15" align="left" valign="bottom">Keyword</th>
<th id="r1c2-t15" align="left" valign="bottom">Meaning</th>
<th id="r1c3-t15" align="left" valign="bottom">Comments</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t15" headers="r1c1-t15" align="left">
<p>DSN</p>
</td>
<td headers="r2c1-t15 r1c2-t15" align="left">
<p>ODBC Data Source Name</p>
</td>
<td headers="r2c1-t15 r1c3-t15" align="left">
<p>User-supplied name.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t15" headers="r1c1-t15" align="left">
<p>DBQ</p>
</td>
<td headers="r3c1-t15 r1c2-t15" align="left">
<p>TNS Service Name</p>
</td>
<td headers="r3c1-t15 r1c3-t15" align="left">
<p>User-supplied name.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t15" headers="r1c1-t15" align="left">
<p>UID</p>
</td>
<td headers="r4c1-t15 r1c2-t15" align="left">
<p>User ID or User Name</p>
</td>
<td headers="r4c1-t15 r1c3-t15" align="left">
<p>User-supplied name.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t15" headers="r1c1-t15" align="left">
<p>PWD</p>
</td>
<td headers="r5c1-t15 r1c2-t15" align="left">
<p>Password</p>
</td>
<td headers="r5c1-t15 r1c3-t15" align="left">
<p>User-supplied password. Specify PWD=; for an empty password.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r6c1-t15" headers="r1c1-t15" align="left">
<p>DBA</p>
</td>
<td headers="r6c1-t15 r1c2-t15" align="left">
<p>Database Attribute</p>
</td>
<td headers="r6c1-t15 r1c3-t15" align="left">
<p>W=write access. R=read-only access.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r7c1-t15" headers="r1c1-t15" align="left">
<p>APA</p>
</td>
<td headers="r7c1-t15 r1c2-t15" align="left">
<p>Applications Attributes</p>
</td>
<td headers="r7c1-t15 r1c3-t15" align="left">
<p>T=Thread Safety Enabled. F=Thread Safety Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r8c1-t15" headers="r1c1-t15" align="left">
<p>RST</p>
</td>
<td headers="r8c1-t15 r1c2-t15" align="left">
<p>Result Sets</p>
</td>
<td headers="r8c1-t15 r1c3-t15" align="left">
<p>T=Result Sets Enabled. F=Result Sets Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r9c1-t15" headers="r1c1-t15" align="left">
<p>QTO</p>
</td>
<td headers="r9c1-t15 r1c2-t15" align="left">
<p>Query Timeout Option</p>
</td>
<td headers="r9c1-t15 r1c3-t15" align="left">
<p>T=Query Timeout Enabled. F=Query Timeout Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r10c1-t15" headers="r1c1-t15" align="left">
<p>CSR</p>
</td>
<td headers="r10c1-t15 r1c2-t15" align="left">
<p>Close Cursor</p>
</td>
<td headers="r10c1-t15 r1c3-t15" align="left">
<p>T=Close Cursor Enabled. F=Close Cursor Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r11c1-t15" headers="r1c1-t15" align="left">
<p>BNF</p>
</td>
<td headers="r11c1-t15 r1c2-t15" align="left">
<p>Bind <code>NUMBER</code> as <code>FLOAT</code></p>
</td>
<td headers="r11c1-t15 r1c3-t15" align="left">
<p>T=Bind <code>NUMBER</code> as <code>FLOAT</code>. F=Bind <code>NUMBER</code> as <code>NUMBER</code>.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r12c1-t15" headers="r1c1-t15" align="left">
<p>DRH</p>
</td>
<td headers="r12c1-t15 r1c2-t15" align="left">
<p>Disable Rule Hint</p>
</td>
<td headers="r12c1-t15 r1c3-t15" align="left">
<p>T=Disable Rule Hint. F=Enable Rule Hint.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r13c1-t15" headers="r1c1-t15" align="left">
<p>BAM</p>
</td>
<td headers="r13c1-t15 r1c2-t15" align="left">
<p>Batch Autocommit Mode</p>
</td>
<td headers="r13c1-t15 r1c3-t15" align="left">
<p>IfAllSuccessful=Commit only if all statements are successful (old
behavior). UpToFirstFailure=Commit up to first failing statement (V7
ODBC behavior). AllSuccessful=Commit all successful statements (only
when connected to an Oracle database; against other databases, same
behavior as V7).</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r14c1-t15" headers="r1c1-t15" align="left">
<p>FBS</p>
</td>
<td headers="r14c1-t15 r1c2-t15" align="left">
<p>Fetch Buffer Size</p>
</td>
<td headers="r14c1-t15 r1c3-t15" align="left">
<p>User-supplied numeric value (specify a value in bytes of 0 or greater). The default is 60,000 bytes.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r15c1-t15" headers="r1c1-t15" align="left">
<p>FEN</p>
</td>
<td headers="r15c1-t15 r1c2-t15" align="left">
<p>Failover</p>
</td>
<td headers="r15c1-t15 r1c3-t15" align="left">
<p>T=Failover Enabled. F=Failover Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r16c1-t15" headers="r1c1-t15" align="left">
<p>FRC</p>
</td>
<td headers="r16c1-t15 r1c2-t15" align="left">
<p>Failover Retry Count</p>
</td>
<td headers="r16c1-t15 r1c3-t15" align="left">
<p>User-supplied numeric value. The default is 10.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r17c1-t15" headers="r1c1-t15" align="left">
<p>FDL</p>
</td>
<td headers="r17c1-t15 r1c2-t15" align="left">
<p>Failover Delay</p>
</td>
<td headers="r17c1-t15 r1c3-t15" align="left">
<p>User-supplied numeric value. The default is 10.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r18c1-t15" headers="r1c1-t15" align="left">
<p>LOB</p>
</td>
<td headers="r18c1-t15 r1c2-t15" align="left">
<p>LOB Writes</p>
</td>
<td headers="r18c1-t15 r1c3-t15" align="left">
<p>T=LOBs Enabled. F=LOBs Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r19c1-t15" headers="r1c1-t15" align="left">
<p>MTS</p>
</td>
<td headers="r19c1-t15 r1c2-t15" align="left">
<p>Microsoft Transaction Server Support</p>
</td>
<td headers="r19c1-t15 r1c3-t15" align="left">
<p>T=Disabled. F=Enabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r20c1-t15" headers="r1c1-t15" align="left">
<p>FWC</p>
</td>
<td headers="r20c1-t15 r1c2-t15" align="left">
<p>Force <code>SQL_WCHAR</code> Support</p>
</td>
<td headers="r20c1-t15 r1c3-t15" align="left">
<p>T=Force <code>SQL_WCHAR</code> Enabled. F=Force <code>SQL_WCHAR</code> Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r21c1-t15" headers="r1c1-t15" align="left">
<p>EXC</p>
</td>
<td headers="r21c1-t15 r1c2-t15" align="left">
<p>EXEC Syntax</p>
</td>
<td headers="r21c1-t15 r1c3-t15" align="left">
<p>T=EXEC Syntax Enabled. F=EXEC Syntax Disabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r22c1-t15" headers="r1c1-t15" align="left">
<p>XSM</p>
</td>
<td headers="r22c1-t15 r1c2-t15" align="left">
<p>Schema Field</p>
</td>
<td headers="r22c1-t15 r1c3-t15" align="left">
<p>Default=Default. Database=Database Name. Owner=Owner Name.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r23c1-t15" headers="r1c1-t15" align="left">
<p>MDI</p>
</td>
<td headers="r23c1-t15 r1c2-t15" align="left">
<p>Set Metadata ID Default</p>
</td>
<td headers="r23c1-t15 r1c3-t15" align="left">
<p>T=<code>SQL_ATTR_METADATA_ID</code> defaults to <code>SQL_TRUE</code>. F=<code>SQL_ATTR_METADATA_ID</code> defaults to <code>SQL_FALSE</code>.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r24c1-t15" headers="r1c1-t15" align="left">
<p>DPM</p>
</td>
<td headers="r24c1-t15 r1c2-t15" align="left">
<p>Disable <code>SQLDescribeParam</code></p>
</td>
<td headers="r24c1-t15 r1c3-t15" align="left">
<p>T=<code>SQLDescribeParam</code> Disabled. F=<code>SQLDescribeParam</code> Enabled.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r25c1-t15" headers="r1c1-t15" align="left">
<p>BTD</p>
</td>
<td headers="r25c1-t15 r1c2-t15" align="left">
<p>Bind <code>TIMESTAMP</code> as <code>DATE</code></p>
</td>
<td headers="r25c1-t15 r1c3-t15" align="left">
<p>T=Bind <code>SQL_TIMESTAMP</code> as Oracle <code>DATE</code> F=Bind <code>SQL_TIMESTAMP</code> as Oracle <code>TIMESTAMP</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r26c1-t15" headers="r1c1-t15" align="left">
<p>NUM</p>
</td>
<td headers="r26c1-t15 r1c2-t15" align="left">
<p>Numeric Settings</p>
</td>
<td headers="r26c1-t15 r1c3-t15" align="left">
<p>NLS=Use Oracle NLS numeric settings (to determine the decimal and
group separator).MS=Use Microsoft regional settings.US=Use US settings.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r27c1-t15" headers="r1c1-t15" align="left">
<p>ODA</p>
</td>
<td headers="r27c1-t15 r1c2-t15" align="left">
<p>Use <code>OCIDescribeAny( )</code></p>
</td>
<td headers="r27c1-t15 r1c3-t15" align="left">
<p>T= Use <code>OCIDescribeAny( )</code> call to gain performance improvement when application makes heavy calls to small packaged procedures that return <code>REF CURSORS</code>.</p>
<p>F= Do not use <code>OCIDescribeAny( )</code>. By default, use <code>OCIDescribeAny( )</code> value is <code>FALSE</code>.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r28c1-t15" headers="r1c1-t15" align="left">
<p>STE</p>
</td>
<td headers="r28c1-t15 r1c2-t15" align="left">
<p>SQL Translate ORA Errors</p>
<p>Specifies whether or not the Oracle ODBC Driver is to translate the Oracle error codes</p>
</td>
<td headers="r28c1-t15 r1c3-t15" align="left">
<p>T=Translate ORA errors.</p>
<p>F=Do not translate any ORA error. By default, <code>SQLTranslateErrors</code> is set to <code>FALSE</code>.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r29c1-t15" headers="r1c1-t15" align="left">
<p>TSZ</p>
</td>
<td headers="r29c1-t15 r1c2-t15" align="left">
<p>Token Size</p>
</td>
<td headers="r29c1-t15 r1c3-t15" align="left">
<p>User-supplied numeric value.</p>
<p>Sets the token size to the nearest multiple of 1 KB (1024 bytes)
beginning at 4 KB (4096 bytes). The default size is 8 KB (8192 bytes).
The maximum value that can be set is 128 KB (131068 bytes).</p>
</td>
</tr>
<tr align="left" valign="top">
<td></td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" -->
<p>If the following keyword is specified in the connection string, the
Oracle ODBC Driver will not read any values defined from the
Administrator:</p>
<pre xml:space="preserve" class="oac_no_warn">DRIVER={Oracle ODBC Driver}<br /></pre>
<p>Examples of valid connection strings are:</p>
<pre xml:space="preserve" class="oac_no_warn">1) DSN=Personnel;UID=Kotzwinkle;PWD=;2) DRIVER={Oracle ODBC Driver};UID=Kotzwinkle;PWD=whatever;DBQ=instl_alias;DBA=W;<br /></pre>
<p>For additional information, click any of the following links.</p>
<a id="ADFNS1185" name="ADFNS1185"></a>
<p class="subhead2">Related Topic for All Users</p>
<p><a href="#BABDCHIG">Connecting to an Oracle Data Source</a></p>
<a id="ADFNS1186" name="ADFNS1186"></a>
<p class="subhead2">Related Topic for Programmers</p>
<p><a href="#BABIDEDG">SQLDriverConnect Implementation</a></p>
</div>
<!-- class="sect2" -->
<a id="BABIDEDG" name="BABIDEDG"></a><a id="ADFNS1188" name="ADFNS1188"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">SQLDriverConnect Implementation</h3>
<div class="tblformal"><a id="ADFNS1189" name="ADFNS1189"></a><a id="sthref13" name="sthref13"></a><a id="BABGEAEC" name="BABGEAEC"></a>
<p class="titleintable">Table 1-6 Keywords Required by the SQLDriverConnect Connection String</p>
<table class="Formal" title="Keywords Required by the SQLDriverConnect Connection String" summary="Thiis table describes the keywords required by the SQLDriverConnect connection string." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="31%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t16" align="left" valign="bottom">Keyword</th>
<th id="r1c2-t16" align="left" valign="bottom">Description</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t16" headers="r1c1-t16" align="left">
<p>DSN</p>
</td>
<td headers="r2c1-t16 r1c2-t16" align="left">
<p>The name of the data source.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t16" headers="r1c1-t16" align="left">
<p>DBQ</p>
</td>
<td headers="r3c1-t16 r1c2-t16" align="left">
<p>The TNS Service Name. See <a href="#BABCHHFC">Creating Oracle ODBC Driver TNS Service Names</a>. For more information, see the Oracle Net Services documentation.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t16" headers="r1c1-t16" align="left">
<p>UID</p>
</td>
<td headers="r4c1-t16 r1c2-t16" align="left">
<p>The user login ID or user name.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t16" headers="r1c1-t16" align="left">
<p>PWD</p>
</td>
<td headers="r5c1-t16 r1c2-t16" align="left">
<p>The user-specified password.</p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" --></div>
<!-- class="sect2" -->
<a id="BABCEBFI" name="BABCEBFI"></a><a id="ADFNS1190" name="ADFNS1190"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Reducing Lock Timeout in a Program</h3>
<p>The Oracle server waits indefinitely for lock conflicts between
transactions to be resolved. You can limit the amount of time that the
Oracle server waits for locks to be resolved by calling the ODBC <code>SQLSetConnectAttr</code> function before connecting to the data source. Specify a nonzero value for the <code>SQL_ATTR_QUERY_TIMEOUT</code> attribute in the ODBC <code>SQLSetStmtAttr function</code>.</p>
<p>If you specify a lock timeout value using the ODBC <code>SQLSetConnectAttr</code> function, it overrides any value specified in the oraodbc.ini file. Refer to <a href="#BABEGGIB">Reducing Lock Timeout</a> for more information on specifying a value in the <code>oraodbc.ini</code> file.</p>
</div>
<!-- class="sect2" -->
<a id="BABJJDBG" name="BABJJDBG"></a><a id="ADFNS1191" name="ADFNS1191"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Linking with odbc32.lib (Windows) or libodbc.so (UNIX)</h3>
<p>For Windows platforms, when you link your program, you must link it with the import library <code>odbc32.lib</code>.</p>
<p>For UNIX platforms, an ODBC application should be linked to <code>libodbc.so</code>.</p>
</div>
<!-- class="sect2" -->
<a id="BABCEAIA" name="BABCEAIA"></a><a id="ADFNS1192" name="ADFNS1192"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Obtaining Information About rowids</h3>
<p>The ODBC <code>SQLSpecialColumns</code> function returns information
about the columns in a table. When used with the Oracle ODBC Driver, it
returns information about the Oracle rowids associated with an Oracle
table.</p>
</div>
<!-- class="sect2" -->
<a id="BABBBDEJ" name="BABBBDEJ"></a><a id="ADFNS1193" name="ADFNS1193"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Rowids in a WHERE Clause</h3>
<p>Rowids may be used in the <code>WHERE</code> clause of an SQL statement. However, the rowid value must be presented in a parameter marker.</p>
</div>
<!-- class="sect2" -->
<a id="BABHGBFJ" name="BABHGBFJ"></a><a id="ADFNS1194" name="ADFNS1194"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Enabling Result Sets</h3>
<p>Oracle reference cursors (Result Sets) allow an application to
retrieve data using stored procedures and stored functions. The
following information identifies how to use reference cursors to enable
Result Sets through ODBC.</p>
<ul>
<li>
<p>The ODBC syntax for calling stored procedures must be used. Native
PL/SQL is not supported through ODBC. The following identifies how to
call the procedure or function without a package and within a package.
The package name in this case is RSET.</p>
<pre xml:space="preserve" class="oac_no_warn">Procedure call: <br />{CALL Example1(?)}<br />{CALL RSET.Example1(?)} <br />Function Call: <br />{? = CALL Example1(?)}<br />{? = CALL RSET.Example1(?)} <br /></pre></li>
<li>
<p>The PL/SQL reference cursor parameters are omitted when calling the
procedure. For example, assume procedure Example2 is defined to have
four parameters. Parameters 1 and 3 are reference cursor parameters and
parameters 2 and 4 are character strings. The call is specified as:</p>
<pre xml:space="preserve" class="oac_no_warn">{CALL RSET.Example2("Literal 1", "Literal 2")}<br /></pre></li>
</ul>
<p>The following example application shows how to return a Result Set using the Oracle ODBC Driver:</p>
<pre xml:space="preserve" class="oac_no_warn">/* <br />* Sample Application using Oracle reference cursors via ODBC <br />* <br />* Assumptions: <br />* <br />* 1) Oracle Sample database is present with data loaded for the EMP table. <br />* <br />* 2) Two fields are referenced from the EMP table ename and mgr. <br />* <br />* 3) A data source has been setup to access the sample database. <br />* <br />* <br />* Program Description: <br />* <br />* Abstract: <br />* <br />* This program demonstrates how to return result sets using <br />* Oracle stored procedures <br />* <br />* Details: <br />* <br />* This program: <br />* Creates an ODBC connection to the database. <br />* Creates a Packaged Procedure containing two result sets. <br />* Executes the procedure and retrieves the data from both result sets.<br />* Displays the data to the user.<br />* Deletes the package then logs the user out of the database. <br />* <br />* <br />* The following is the actual PL/SQL this code generates to <br />* create the stored procedures. <br />* <br />DROP PACKAGE ODBCRefCur; <br />CREATE PACKAGE ODBCRefCur AS <br />TYPE ename_cur IS REF CURSOR; <br />TYPE mgr_cur IS REF CURSOR; <br />PROCEDURE EmpCurs(Ename IN OUT ename_cur, Mgr IN OUT mgr_cur, pjob IN VARCHAR2); <br /> <br /> <br /> <br />END; <br />/ <br />CREATE PACKAGE BODY ODBCRefCur AS <br />PROCEDURE EmpCurs(Ename IN OUT ename_cur, Mgr IN OUT mgr_cur, pjob IN VARCHAR2) <br />AS <br />BEGIN <br />IF NOT Ename%ISOPEN <br />THEN <br />OPEN Ename for SELECT ename from emp; <br />END IF; <br /> <br /> <br /> <br />IF NOT Mgr%ISOPEN <br />THEN <br />OPEN Mgr for SELECT mgr from emp where job = pjob; <br />END IF; <br />END; <br />END; <br />/ <br /> <br /> <br /> <br />* <br />* End PL/SQL for Reference Cursor. <br />*/ <br /> <br /> <br /> <br />/* <br />* Include Files <br />*/ <br />#include &lt;windows.h&gt; <br />#include &lt;stdio.h&gt; <br />#include &lt;sql.h&gt; <br />#include &lt;sqlext.h&gt; <br /> <br /> <br /> <br />/* <br />* Defines <br />*/ <br />#define JOB_LEN 9 <br />#define DATA_LEN 100 <br />#define SQL_STMT_LEN 500 <br /> <br /> <br /> <br />/* <br />* Procedures <br />*/ <br />void DisplayError( SWORD HandleType, SQLHANDLE hHandle, char *Module ); <br /> <br />/* <br />* Main Program <br />*/ <br />int main() <br />{ <br />SQLHENV hEnv; <br />SQLHDBC hDbc; <br />SQLHSTMT hStmt; <br />SQLRETURN rc; <br />char *DefUserName ="scott"; <br />char *DefPassWord ="tiger"; <br />SQLCHAR ServerName[DATA_LEN]; <br />SQLCHAR *pServerName=ServerName; <br />SQLCHAR UserName[DATA_LEN]; <br />SQLCHAR *pUserName=UserName; <br />SQLCHAR PassWord[DATA_LEN]; <br />SQLCHAR *pPassWord=PassWord; <br />char Data[DATA_LEN]; <br />SQLINTEGER DataLen; <br />char error[DATA_LEN]; <br />char *charptr; <br />SQLCHAR SqlStmt[SQL_STMT_LEN]; <br />SQLCHAR *pSqlStmt=SqlStmt; <br />char *pSalesMan = "SALESMAN"; <br />SQLINTEGER sqlnts=SQL_NTS; <br /> <br /> <br /> <br />/* <br />* Allocate the Environment Handle <br />*/ <br />rc = SQLAllocHandle( SQL_HANDLE_ENV, SQL_NULL_HANDLE, &amp;hEnv ); <br />if (rc != SQL_SUCCESS) <br />{ <br />printf( "Cannot Allocate Environment Handle/n"); <br />printf( "/nHit Return to Exit/n"); <br />charptr = gets ((char *)error); <br />exit(1); <br />} <br /> <br /> <br /> <br />/* <br />* Set the ODBC Version <br />*/ <br />rc = SQLSetEnvAttr( hEnv, <br />SQL_ATTR_ODBC_VERSION, <br />(void *)SQL_OV_ODBC3, <br />0); <br />if (rc != SQL_SUCCESS) <br />{ <br />printf( "Cannot Set ODBC Version/n"); <br />printf( "/nHit Return to Exit/n"); <br />charptr = gets ((char *)error); <br />exit(1); <br />} <br /> <br /> <br /> <br />/* <br />* Allocate the Connection handle <br />*/ <br />rc = SQLAllocHandle( SQL_HANDLE_DBC, hEnv, &amp;hDbc ); <br />if (rc != SQL_SUCCESS) <br />{ <br />printf( "Cannot Allocate Connection Handle/n"); <br />printf( "/nHit Return to Exit/n"); <br />charptr = gets ((char *)error); <br />exit(1); <br />} <br /> <br /> <br /> <br />/* <br />* Get User Information <br />*/ <br />lstrcpy( (char *) pUserName, DefUserName ); <br />lstrcpy( (char *) pPassWord, DefPassWord ); <br /> <br /> <br /> <br />/* <br />* Data Source name <br />*/ <br />printf( "/nEnter the ODBC Data Source Name/n" ); <br />charptr = gets ((char *) ServerName); <br /> <br /> <br /> <br />/* <br />* User Name <br />*/ <br />printf ( "/nEnter User Name Default [%s]/n", pUserName); <br />charptr = gets ((char *) UserName); <br />if (*charptr == '/0') <br />{ <br />lstrcpy( (char *) pUserName, (char *) DefUserName ); <br />} <br /> <br /> <br /> <br />/* <br />* Password <br />*/ <br />printf ( "/nEnter Password Default [%s]/n", pPassWord); <br />charptr = gets ((char *)PassWord); <br />if (*charptr == '/0') <br />{ <br />lstrcpy( (char *) pPassWord, (char *) DefPassWord ); <br />} <br /> <br /> <br /> <br />/* <br />* Connection to the database <br />*/ <br />rc = SQLConnect( hDbc, <br />pServerName, <br />(SQLSMALLINT) lstrlen((char *)pServerName), <br />pUserName, <br />(SQLSMALLINT) lstrlen((char *)pUserName), <br />pPassWord, <br />(SQLSMALLINT) lstrlen((char *)pPassWord)); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_DBC, hDbc, "SQLConnect"); <br />} <br /> <br /> <br /> <br />/* <br />* Allocate a Statement <br />*/ <br />rc = SQLAllocHandle( SQL_HANDLE_STMT, hDbc, &amp;hStmt ); <br />if (rc != SQL_SUCCESS) <br />{ <br />printf( "Cannot Allocate Statement Handle/n"); <br />printf( "/nHit Return to Exit/n"); <br />charptr = gets ((char *)error); <br />exit(1); <br />} <br /> <br /> <br /> <br />/* <br />* Drop the Package <br />*/ <br />lstrcpy( (char *) pSqlStmt, "DROP PACKAGE ODBCRefCur"); <br />rc = SQLExecDirect(hStmt, pSqlStmt, lstrlen((char *)pSqlStmt)); <br /> <br /> <br /> <br />/* <br />* Create the Package Header <br />*/ <br />lstrcpy( (char *) pSqlStmt, "CREATE PACKAGE ODBCRefCur AS/n"); <br />lstrcat( (char *) pSqlStmt, " TYPE ename_cur IS REF CURSOR;/n"); <br />lstrcat( (char *) pSqlStmt, " TYPE mgr_cur IS REF CURSOR;/n/n"); <br />lstrcat( (char *) pSqlStmt, " PROCEDURE EmpCurs (Ename IN OUT ename_cur,"); <br />lstrcat( (char *) pSqlStmt, "Mgr IN OUT mgr_cur,pjob IN VARCHAR2);/n/n"); <br />lstrcat( (char *) pSqlStmt, "END;/n"); <br />rc = SQLExecDirect(hStmt, pSqlStmt, lstrlen((char *)pSqlStmt)); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLExecDirect"); <br />} <br /> <br /> <br /> <br />/* <br />* Create the Package Body <br />*/ <br />lstrcpy( (char *) pSqlStmt, "CREATE PACKAGE BODY ODBCRefCur AS/n"); <br />lstrcat( (char *) pSqlStmt, " PROCEDURE EmpCurs (Ename IN OUT ename_cur,"); <br />lstrcat( (char *) pSqlStmt, "Mgr IN OUT mgr_cur, pjob IN VARCHAR2)/n AS/n BEGIN/n"); <br />lstrcat( (char *) pSqlStmt, " IF NOT Ename%ISOPEN/n THEN/n"); <br />lstrcat( (char *) pSqlStmt, " OPEN Ename for SELECT ename from emp;/n"); <br />lstrcat( (char *) pSqlStmt, " END IF;/n/n"); <br />lstrcat( (char *) pSqlStmt, " IF NOT Mgr%ISOPEN/n THEN/n"); <br />lstrcat( (char *) pSqlStmt, " OPEN Mgr for SELECT mgr from emp where job = pjob;/n"); <br />lstrcat( (char *) pSqlStmt, " END IF;/n"); <br />lstrcat( (char *) pSqlStmt, " END;/n"); <br />lstrcat( (char *) pSqlStmt, "END;/n"); <br />rc = SQLExecDirect(hStmt, pSqlStmt, lstrlen((char *)pSqlStmt)); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLExecDirect"); <br />} <br /> <br /> <br /> <br />/* <br />* Bind the Parameter <br />*/ <br />rc = SQLBindParameter(hStmt, <br />1, <br />SQL_PARAM_INPUT, <br />SQL_C_CHAR, <br />SQL_CHAR, <br />JOB_LEN, <br />0, <br />pSalesMan, <br />0, <br />&amp;sqlnts); <br /> <br /> <br /> <br />/* <br />* Call the Store Procedure which executes the Result Sets <br />*/ <br />lstrcpy( (char *) pSqlStmt, "{CALL ODBCRefCur.EmpCurs(?)}"); <br />rc = SQLExecDirect(hStmt, pSqlStmt, lstrlen((char *)pSqlStmt)); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLExecDirect"); <br />} <br /> <br /> <br /> <br />/* <br />* Bind the Data <br />*/ <br />rc = SQLBindCol( hStmt, <br />1, <br />SQL_C_CHAR, <br />Data, <br />sizeof(Data), <br />&amp;DataLen); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLBindCol"); <br />} <br /> <br /> <br /> <br />/* <br />* Get the data for Result Set 1 <br />*/ <br />printf( "/nEmployee Names/n/n"); <br />while ( rc == SQL_SUCCESS ) <br />{ <br />rc = SQLFetch( hStmt ); <br />if ( rc == SQL_SUCCESS ) <br />{ <br />printf("%s/n", Data); <br />} <br />else <br />{ <br />if (rc != SQL_NO_DATA) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLFetch"); <br />} <br />} <br />} <br /> <br /> <br /> <br />printf( "/nFirst Result Set - Hit Return to Continue/n"); <br />charptr = gets ((char *)error); <br /> <br /> <br /> <br />/* <br />* Get the Next Result Set <br />*/ <br />rc = SQLMoreResults( hStmt ); <br />if (rc != SQL_SUCCESS) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLMoreResults"); <br />} <br /> <br /> <br /> <br />/* <br />* Get the data for Result Set 2 <br />*/ <br />printf( "/nManagers/n/n"); <br />while ( rc == SQL_SUCCESS ) <br />{ <br />rc = SQLFetch( hStmt ); <br />if ( rc == SQL_SUCCESS ) <br />{ <br />printf("%s/n", Data); <br />} <br />else <br />{ <br />if (rc != SQL_NO_DATA) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLFetch"); <br />} <br />} <br />} <br /> <br /> <br /> <br />printf( "/nSecond Result Set - Hit Return to Continue/n"); <br />charptr = gets ((char *)error); <br /> <br /> <br /> <br />/* <br />* Should Be No More Results Sets <br />*/ <br />rc = SQLMoreResults( hStmt ); <br />if (rc != SQL_NO_DATA) <br />{ <br />DisplayError(SQL_HANDLE_STMT, hStmt, "SQLMoreResults"); <br />} <br /> <br /> <br /> <br />/* <br />* Drop the Package <br />*/ <br />lstrcpy( (char *) pSqlStmt, "DROP PACKAGE ODBCRefCur"); <br />rc = SQLExecDirect(hStmt, pSqlStmt, lstrlen((char *)pSqlStmt)); <br /> <br /> <br /> <br />/* <br />* Free handles close connections to the database <br />*/ <br />SQLFreeHandle( SQL_HANDLE_STMT, hStmt ); <br />SQLDisconnect( hDbc ); <br />SQLFreeHandle( SQL_HANDLE_DBC, hDbc ); <br />SQLFreeHandle( SQL_HANDLE_ENV, hEnv ); <br /> <br /> <br /> <br />printf( "/nAll Done - Hit Return to Exit/n"); <br />charptr = gets ((char *)error); <br />return(0); <br />} <br /> <br /> <br /> <br />/* <br />* Display Error Messages <br />*/ <br />void DisplayError( SWORD HandleType, SQLHANDLE hHandle, char *Module ) <br />{ <br /> <br /> <br /> <br />SQLCHAR MessageText[255]; <br />SQLCHAR SQLState[80]; <br />SQLRETURN rc=SQL_SUCCESS; <br />LONG NativeError; <br />SWORD RetLen; <br />SQLCHAR error[25]; <br />char *charptr; <br /> <br /> <br /> <br />rc = SQLGetDiagRec(HandleType, <br />hHandle, <br />1, <br />SQLState, <br />&amp;NativeError, <br />MessageText, <br />255, <br />&amp;RetLen); <br /> <br /> <br /> <br />printf( "Failure Calling %s/n", Module ); <br />if (rc == SQL_SUCCESS || rc == SQL_SUCCESS_WITH_INFO) <br />{ <br />printf( "/t/t/t State: %s/n", SQLState); <br />printf( "/t/t/t Native Error: %d/n", NativeError ); <br />printf( "/t/t/t Error Message: %s/n", MessageText ); <br />} <br /> <br />printf( "/nHit Return to Exit/n"); <br />charptr = gets ((char *)error); <br /> <br /> <br /> <br />exit(1); <br />}<br /></pre></div>
<!-- class="sect2" -->
<a id="BABJCDGE" name="BABJCDGE"></a><a id="ADFNS1195" name="ADFNS1195"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Enabling EXEC Syntax</h3>
<p>If the syntax of your SQL Server <code>EXEC</code> statement can be
readily translated to an equivalent Oracle procedure call without
requiring any change to it, the Oracle ODBC Driver can translate it if
you enable this option.</p>
<p>The complete name of a SQL Server procedure consists of up to four identifiers:</p>
<ul>
<li>
<p>server name</p>
</li>
<li>
<p>database name</p>
</li>
<li>
<p>owner name</p>
</li>
<li>
<p>procedure name</p>
</li>
</ul>
<p>The format for the name is:</p>
<pre xml:space="preserve" class="oac_no_warn">[[[server.][database].][owner_name].]procedure_name<br /></pre>
<p>During the migration of the SQL Server database to Oracle, the
definition of each SQL Server procedure (or function) is converted to
its equivalent Oracle syntax and is defined in a schema in Oracle.
Migrated procedures are often reorganized (and created in schemas) in
one of the three following ways:</p>
<ul>
<li>
<p>All procedures are migrated to one schema (the default option).</p>
</li>
<li>
<p>All procedures defined in one SQL Server database are migrated to the schema named with that database name.</p>
</li>
<li>
<p>All procedures owned by one user are migrated to the schema named with that user's name.</p>
</li>
</ul>
<p>To support these three ways of organizing migrated procedures, you
may specify one of these schema name options for translating procedure
names. Object names in the translated Oracle procedure call are not
case-sensitive.</p>
</div>
<!-- class="sect2" -->
<a id="BABGJGBA" name="BABGJGBA"></a><a id="ADFNS1196" name="ADFNS1196"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Enabling Event Notification for Connection Failures in an Oracle RAC Environment</h3>
<p>If the <code>SQL_ORCLATTR_FAILOVER_CALLBACK</code> and <code>SQL_ORCLATTR_FAILOVER_HANDLE</code> attributes of the <code>SQLSetConnectAttr</code>
function are set when a connection failure occurs in an Oracle Real
Application Clusters (Oracle RAC) Database environment, event
notification is enabled. Both attributes are set using the <code>SQLSetConnectAttr</code> function. The symbols for the new attributes are defined in the file sqora.h.</p>
<p>The <code>SQL_ORCLATTR_FAILOVER_CALLBACK</code> attribute is used to specify the address of a routine to call when a failure event takes place.</p>
<p>The <code>SQL_ORCLATTR_FAILOVER_HANDLE</code> attribute is used to
specify a context handle which will be passed as one of the parameters
in the callback routine. This attribute is necessary in order for the
ODBC application to determine which connection the failure event is
taking place on.</p>
<p>The function prototype for the callback routine is:</p>
<pre xml:space="preserve" class="oac_no_warn">void failover_callback(void *handle, SQLINTEGER fo_code)<br /></pre>
<p>The 'handle' parameter is the value that was set by the <code>SQL_ORCLATTR_FAILOVER_HANDLE</code> attribute. Null is returned if the attribute has not been set.</p>
<p>The <code>fo_code</code> parameter identifies the failure event
which is taking place. The failure events map directly to the events
defined in the OCI programming interface. The list of possible events
is:</p>
<ul>
<li>
<p><code>ODBC_FO_BEGIN</code></p>
</li>
<li>
<p><code>ODBC_FO_ERROR</code></p>
</li>
<li>
<p><code>ODBC_FO_ABORT</code></p>
</li>
<li>
<p><code>ODBC_FO_REAUTH</code></p>
</li>
<li>
<p><code>ODBC_FO_END</code></p>
</li>
</ul>
<p>The following is a sample program which demonstrates using this feature:</p>
<pre xml:space="preserve" class="oac_no_warn">/*<br />  NAME<br />  ODBCCallbackTest<br /> <br />  DESCRIPTION<br />  Simple program to demonstrate the connection failover callback feature.<br /> <br />  PUBLIC FUNCTION(S)<br />  main<br /> <br />  PRIVATE FUNCTION(S)<br /> <br />  NOTES<br /> <br />  Command Line: ODBCCallbackTest filename [odbc-driver]<br /> <br />*/<br /> <br />#include &lt;windows.h&gt;<br />#include &lt;tchar.h&gt;<br />#include &lt;malloc.h&gt;<br />#include &lt;stdio.h&gt;<br />#include &lt;string.h&gt;<br />#include &lt;sql.h&gt;<br />#include &lt;sqlext.h&gt;<br />#include "sqora.h"<br /> <br />/*<br />** Function Prototypes<br />*/<br />void display_errors(SQLSMALLINT HandleType, SQLHANDLE Handle);<br />void failover_callback(void *Handle, SQLINTEGER fo_code);<br /> <br />/*<br />** Macros<br />*/<br />#define ODBC_STS_CHECK(sts) \<br />  if (sts != SQL_SUCCESS) \<br />{ \<br />  display_errors(SQL_HANDLE_ENV, hEnv); \<br />  display_errors(SQL_HANDLE_DBC, hDbc); \<br />  display_errors(SQL_HANDLE_STMT, hStmt); \<br />  return FALSE; \<br />}<br /> <br />/*<br />** ODBC Handles<br />*/<br />SQLHENV *hEnv = NULL; // ODBC Environment Handle<br />SQLHANDLE *hDbc = NULL; // ODBC Connection Handle<br />SQLHANDLE *hStmt = NULL; // ODBC Statement Handle<br /> <br />/*<br />** Connection Information<br />*/<br />TCHAR *dsn = _T("odbctest");<br />TCHAR *uid = _T("scott");<br />TCHAR *pwd = _T("tiger");<br />TCHAR *szSelect = _T("select * from emp");<br /> <br />/*<br />** MAIN Routine<br />*/<br />main(int argc, char **argv)<br />{<br />  SQLRETURN rc;<br /> <br />  /*<br />  ** Allocate handles<br />  */<br />  rc = SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, (SQLHANDLE *)&amp;hEnv);<br />  ODBC_STS_CHECK(rc)<br /> <br />  rc = SQLSetEnvAttr(hEnv, SQL_ATTR_ODBC_VERSION, (SQLPOINTER)SQL_OV_ODBC3, 0);<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLAllocHandle(SQL_HANDLE_DBC, hEnv, (SQLHANDLE *)&amp;hDbc);<br />  ODBC_STS_CHECK(rc);<br /> <br />  /*<br />  ** Connect to the database<br />  */<br />  rc = SQLConnect(hDbc, dsn, (SQLSMALLINT)_tcslen(dsn),<br />  uid, (SQLSMALLINT)_tcslen(uid),<br />  pwd, (SQLSMALLINT)_tcslen(pwd));<br />  ODBC_STS_CHECK(rc);<br /> <br />  /*<br />  ** Set the connection failover attributes<br />  */<br />  rc = SQLSetConnectAttr(hDbc, SQL_ORCLATTR_FAILOVER_CALLBACK, &amp;failover_callback, 0);<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLSetConnectAttr(hDbc, SQL_ORCLATTR_FAILOVER_HANDLE, hDbc, 0);<br />  ODBC_STS_CHECK(rc);<br /> <br />  /*<br />  ** Allocate the statement handle<br />  */<br />  rc = SQLAllocHandle(SQL_HANDLE_STMT, hDbc, (SQLHANDLE *)&amp;hStmt);<br />  ODBC_STS_CHECK(rc);<br /> <br />  /*<br />  ** Wait for connection failovers<br />  */<br />  while (TRUE)<br />  {<br />  Sleep(5000);<br /> <br />  rc = SQLExecDirect(hStmt,szSelect, _tcslen(szSelect));<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLFreeStmt(hStmt, SQL_CLOSE);<br />  ODBC_STS_CHECK(rc);<br />  }<br /> <br />  /*<br />  ** Free up the handles and close the connection<br />  */<br />  rc = SQLFreeHandle(SQL_HANDLE_STMT, hStmt);<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLDisconnect(hDbc);<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLFreeHandle(SQL_HANDLE_DBC, hDbc);<br />  ODBC_STS_CHECK(rc);<br /> <br />  rc = SQLFreeHandle(SQL_HANDLE_ENV, hEnv);<br />  ODBC_STS_CHECK(rc);<br /> <br />  return TRUE;<br />}<br /> <br />/*<br />** Failover Callback Routine<br />*/<br />void failover_callback(void *Handle, SQLINTEGER fo_code)<br />{<br />  switch (fo_code) {<br /> <br />  case ODBC_FO_BEGIN:<br />  printf("ODBC_FO_BEGIN recevied\n");<br />  break;<br /> <br />  case ODBC_FO_ERROR:<br />  printf("ODBC_FO_ERROR recevied\n");<br />  break;<br /> <br />  case ODBC_FO_ABORT:<br />  printf("ODBC_FO_ABORT recevied\n");<br />  break;<br /> <br />  case ODBC_FO_REAUTH:<br />  printf("ODBC_FO_REAUTH recevied\n");<br />  break;<br /> <br />  case ODBC_FO_END:<br />  printf("ODBC_FO_END recevied\n");<br />  break;<br /> <br />  default:<br />  printf("Invalid or unknown ODBC failover code recevied\n");<br />  break;<br /> <br />  };<br /> <br />  return;<br /> <br />}<br /> <br />/*<br />** Retrieve the errors associated with the handle passed<br />** and display them.<br />*/<br />void display_errors(SQLSMALLINT HandleType, SQLHANDLE Handle)<br />{<br />  SQLTCHAR MessageText[256];<br />  SQLTCHAR SqlState[5+1];<br />  SQLSMALLINT i=1;<br />  SQLINTEGER NativeError;<br />  SQLSMALLINT TextLength;<br />  SQLRETURN sts = SQL_SUCCESS;<br /> <br />  if (Handle == NULL) return;<br /> <br />  /* Make sure all SQLState text is null terminated */<br />  SqlState[5] = '\0';<br /> <br />  /*<br />  ** Fetch and display all diagnostic records that exist for this handle<br />  */<br />  while (sts == SQL_SUCCESS)<br />  {<br />  NativeError = 0;<br />  TextLength = 0;<br />  sts = SQLGetDiagRec(HandleType, Handle, i, SqlState, &amp;NativeError,<br />  (SQLTCHAR *)&amp;MessageText, sizeof(MessageText),<br />  &amp;TextLength);<br /> <br />  if (sts == SQL_SUCCESS)<br />  {<br />  printf("[%s]%s\n", SqlState, MessageText);<br />  if (NativeError != 0)<br />  {<br />  printf("Native Error Code: %d\n", NativeError);<br />  }<br />  i++;<br />  }<br />  }<br /> <br />  return;<br />}<br /></pre></div>
<!-- class="sect2" -->
<a id="BABDDGBE" name="BABDDGBE"></a><a id="ADFNS315" name="ADFNS315"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Using Implicit Results Feature Through ODBC</h3>
<p>Use this option when you migrate any third party ODBC application to
Oracle Database and you want to use implicit results functionality as
supported by the previous vendor. Oracle ODBC driver supports implicit
results with stored procedures or an anonymous PL/SQL block. For the
current release, implicit results are returned only for <code>SELECT</code> statements.</p>
<p>The following code example shows an example ODBC test case using an anonymous SQL script for implicit results.</p>
<pre xml:space="preserve" class="oac_no_warn">const char *query1="declare \<br />                  c1 sys_refcursor; \<br />                  c2 sys_refcursor; \<br />                  begin \<br />                  open c1 for select empno,ename from emp where rownum&lt;=3; \<br />                  dbms_sql.return_result(c1); \<br />                  open c2 for select empno,ename from emp where rownum&lt;=3; \<br />                  dbms_sql.return_result(c2); end; ";<br /> <br />int main( )<br />{<br />  ...<br />   ...<br /> //Allocate all required handles and establish a connection to the database.<br /> <br /> //Prepare and execute the above anonymous PL/SQL block<br />    SQLPrepare (hstmt, (SQLCHAR *) query1, SQL_NTS);<br />    SQLExecute(hstmt);<br /> <br /> //Bind the columns for the results from the first SELECT statement in an anonymous block.<br />    SQLBindCol (hstmt, 1, SQL_C_ULONG, &amp;eno, 0, &amp;jind);<br />    SQLBindCol (hstmt, 2, SQL_C_CHAR, empname, sizeof (empname),&amp;enind);<br /> <br /> //Fetch implicit results through the SQLFetch( ) call.<br />    while((retCode = SQLFetch(hstmt)) != SQL_NO_DATA)<br />    {<br /> //Do whatever you want to do with the data.<br />    }<br /> <br />     retCode = SQLMoreResults(hstmt);<br /> <br />     if(retCode == SQL_SUCCESS)<br />    {<br />      printf("SQLMoreResults returned with SQL_SUCCESS\n");<br /> <br /> //Bind the columns for the results from the second SELECT statement in an anonymous block.<br />    SQLBindCol (hstmt, 1, SQL_C_ULONG, &amp;eno, 0, &amp;jind);<br />    SQLBindCol (hstmt, 2, SQL_C_CHAR, empname, sizeof (empname),&amp;enind);<br /> <br /> //Fetch implicit results through the SQLFetch( ) call.<br />    while((retCode = SQLFetch(hstmt)) != SQL_NO_DATA)<br />    {<br /> //Do whatever you want to do with data.<br />    }<br />  }<br />}<br /></pre></div>
<!-- class="sect2" -->
<a id="BABBCCDD" name="BABBCCDD"></a><a id="ADFNS1197" name="ADFNS1197"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Supported Functionality</h3>
<ul>
<li>
<p><a href="#BABBCJCB">API Conformance</a></p>
</li>
<li>
<p><a href="#BABHEGBH">Implementation of ODBC API Functions</a></p>
</li>
<li>
<p><a href="#BABIDAGF">Implementation of the ODBC SQL Syntax</a></p>
</li>
<li>
<p><a href="#BABEJGEG">Implementation of Data Types (Programming)</a></p>
</li>
</ul>
<a id="BABBCJCB" name="BABBCJCB"></a><a id="ADFNS1198" name="ADFNS1198"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">API Conformance</h4>
<p>Oracle ODBC Driver release 9.2.0.0.0 and higher supports all Core, Level 2, and Level 1 functions.</p>
<p>In addition, Oracle ODBC Driver release 9.2.0.0.0 and higher supports translation DLLs.</p>
<p>The following topics describe the ODBC API functions implemented by the Oracle ODBC Driver.</p>
<a id="ADFNS1199" name="ADFNS1199"></a>
<p class="subhead2">Related Topic for Advanced Users</p>
<p><a href="#BABCCDFB">Error Messages</a></p>
<a id="ADFNS1200" name="ADFNS1200"></a>
<p class="subhead2">Related Topic for Programmers</p>
<p><a href="#BABHEGBH">Implementation of ODBC API Functions</a></p>
</div>
<!-- class="sect3" -->
<a id="BABHEGBH" name="BABHEGBH"></a><a id="ADFNS1212" name="ADFNS1212"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Implementation of ODBC API Functions</h4>
<div class="tblformal"><a id="ADFNS1213" name="ADFNS1213"></a><a id="sthref14" name="sthref14"></a><a id="BABIIHAC" name="BABIIHAC"></a>
<p class="titleintable">Table 1-7 How Oracle ODBC Driver Implements Specific Functions</p>
<table class="Formal" title="How Oracle ODBC Driver Implements Specific Functions" summary="This table describes how Oracle ODBC Driver implements specific functions." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="31%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t17" align="left" valign="bottom">Function</th>
<th id="r1c2-t17" align="left" valign="bottom">Description</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t17" headers="r1c1-t17" align="left">
<p><code>SQLConnect</code></p>
</td>
<td headers="r2c1-t17 r1c2-t17" align="left">
<p>SQLConnect requires only a DBQ, user ID, and password.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t17" headers="r1c1-t17" align="left">
<p><code>SQLDriverConnect</code></p>
</td>
<td headers="r3c1-t17 r1c2-t17" align="left">
<p>SQLDriverConnect uses the DSN, DBQ, UID, and PWD keywords.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t17" headers="r1c1-t17" align="left">
<p><code>SQLMoreResults</code></p>
</td>
<td headers="r4c1-t17 r1c2-t17" align="left">
<p>Implements ODBC support for implicit results. This is a new API implemented for release 12.1. See <code><a href="http://msdn.microsoft.com/en-us/library/ms714673%28v=VS.85%29.aspx">http://msdn.microsoft.com/en-us/library/ms714673(v=VS.85).aspx</a></code> for more information.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t17" headers="r1c1-t17" align="left">
<p><code>SQLSpecialColumns</code></p>
</td>
<td headers="r5c1-t17 r1c2-t17" align="left">
<p>If <code>SQLSpecialColumns</code> is called with the <code>SQL_BEST_ROWID</code> attribute, it always returns the rowid column.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r6c1-t17" headers="r1c1-t17" align="left">
<p><code>SQLProcedures</code> and<code>SQLProcedureColumns</code></p>
</td>
<td headers="r6c1-t17 r1c2-t17" align="left">
<p>See the information that follows.</p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r7c1-t17" headers="r1c1-t17" align="left">
<p>All catalog functions</p>
</td>
<td headers="r7c1-t17 r1c2-t17" align="left">
<p>If the <code>SQL_ATTR_METADATA_ID</code> statement attribute is set to <code>SQL_TRUE</code>,
a string argument is treated as an identifier argument, and its case is
not significant. In this case, the underscore ("_") and the percent
sign ("%") will be treated as the actual character, not as a search
pattern character. On the other hand, if this attribute is set to <code>SQL_FALSE</code>, it is either an ordinary argument or a pattern value argument and is treated literally, and its case is significant.</p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" --></div>
<!-- class="sect3" -->
<a id="BABIDAGF" name="BABIDAGF"></a><a id="ADFNS1214" name="ADFNS1214"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Implementation of the ODBC SQL Syntax</h4>
<p>If a comparison predicate has a parameter marker as the second
expression in the comparison and the value of that parameter is set to <code>SQL_NULL_DATA</code> with <code>SQLBindParameter</code>, the comparison will fail. This is consistent with the null predicate syntax in ODBC SQL.</p>
</div>
<!-- class="sect3" -->
<a id="BABEJGEG" name="BABEJGEG"></a><a id="ADFNS1215" name="ADFNS1215"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Implementation of Data Types (Programming)</h4>
<p>For programmers, the noteworthy part of the implementation of the data types concerns the <code>CHAR</code>, <code>VARCHAR</code>, and <code>VARCHAR2</code> data types.</p>
<p>For an fSqlType value of <code>SQL_VARCHAR</code>, <code>SQLGetTypeInfo</code> returns the Oracle database data type <code>VARCHAR2</code>. For an fSqlType value of <code>SQL_CHAR</code>, <code>SQLGetTypeInfo</code> returns the Oracle database data type <code>CHAR</code>.</p>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABIFIGA" name="BABIFIGA"></a><a id="ADFNS1216" name="ADFNS1216"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Unicode Support</h3>
<ul>
<li>
<p><a href="#BABDBAFE">Unicode Support Within the ODBC Environment</a></p>
</li>
<li>
<p><a href="#BABCICFJ">Unicode Support in ODBC API</a></p>
</li>
<li>
<p><a href="#BABFEHCB">Unicode Functions in the Driver Manager</a></p>
</li>
<li>
<p><a href="#BABECFEA">SQLGetData Performance</a></p>
</li>
<li>
<p><a href="#BABEADIA">Unicode Samples</a></p>
</li>
</ul>
<a id="BABDBAFE" name="BABDBAFE"></a><a id="ADFNS1217" name="ADFNS1217"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Unicode Support Within the ODBC Environment</h4>
<p>The Microsoft or unixODBC ODBC Driver Manager (Driver Manager) makes
all ODBC drivers, regardless if they support Unicode, appear as if they
are Unicode compliant. This allows ODBC applications to be written
independent of the Unicode capabilities of underlying ODBC drivers.</p>
<p>The extent to which the Driver Manager can emulate Unicode support
for ANSI ODBC drivers is limited by the conversions possible between
the Unicode data and the local code page. Data loss is possible when
the Driver Manager is converting from Unicode to the local code page.
Full Unicode support is not possible unless the underlying ODBC driver
supports Unicode. The Oracle ODBC Driver provides full Unicode support.</p>
</div>
<!-- class="sect3" -->
<a id="BABCICFJ" name="BABCICFJ"></a><a id="ADFNS1218" name="ADFNS1218"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Unicode Support in ODBC API</h4>
<p>The ODBC API supports both Unicode and ANSI entry points using the
"W" and "A" suffix convention. An ODBC application developer does not
need to explicitly call entry points with the suffix. An ODBC
application that is compiled with the UNICODE and _UNICODE preprocessor
definitions will generate the appropriate calls. For example, a call to
<code>SQLPrepare</code> will be compiled as <code>SQLPrepareW</code>.</p>
<p>The C data type, <code>SQL_C_WCHAR</code>, was added to the ODBC
interface to allow applications to specify that an input parameter is
encoded as Unicode or to request column data returned as Unicode. The
macro <code>SQL_C_TCHAR</code> is useful for applications that need to be built as both Unicode and ANSI. The <code>SQL_C_TCHAR</code> macro compiles as <code>SQL_C_WCHAR</code> for Unicode applications and as <code>SQL_C_CHAR</code> for ANSI applications.</p>
<p>The SQL data types, <code>SQL_WCHAR</code>, <code>SQL_WVARCHAR</code>, and <code>SQL_WLONGVARCHAR</code>,
have been added to the ODBC interface to represent columns defined in a
table as Unicode. Potentially, these values are returned from calls to <code>SQLDescribeCol</code>, <code>SQLColAttribute</code>, <code>SQLColumns</code>, and <code>SQLProcedureColumns</code>.</p>
<p>Unicode encoding is supported for SQL column types <code>NCHAR</code>, <code>NVARCHAR2</code>, and <code>NCLOB</code>. In addition, Unicode encoding is also supported for SQL column types <code>CHAR</code> and <code>VARCHAR2</code> if the character semantics are specified in the column definition.</p>
<p>The ODBC Driver supports these SQL column types and maps them to ODBC SQL data types.</p>
<div class="tblformal"><a id="ADFNS1219" name="ADFNS1219"></a><a id="sthref15" name="sthref15"></a><a id="BABJAEBG" name="BABJAEBG"></a>
<p class="titleintable">Table 1-8 Supported SQL Data Types and the Equivalent ODBC SQL Data Type</p>
<table class="Formal" title="Supported SQL Data Types and the Equivalent ODBC SQL Data Type" summary="This table describes the supported SQL data types and their equivalent ODBC Driver data type." border="1" cellpadding="3" cellspacing="0" dir="ltr" frame="hsides" rules="groups" width="100%">
<col width="31%" />
<col width="1*" />
<thead>
<tr align="left" valign="top">
<th id="r1c1-t18" align="left" valign="bottom">SQL Data Types</th>
<th id="r1c2-t18" align="left" valign="bottom">ODBC SQL Data Types</th>
</tr>
</thead>
<tbody>
<tr align="left" valign="top">
<td id="r2c1-t18" headers="r1c1-t18" align="left">
<p><code>CHAR</code></p>
</td>
<td headers="r2c1-t18 r1c2-t18" align="left">
<p><code>SQL_CHAR</code> or <code>SQL_WCHAR</code> <a id="sthref16" name="sthref16" href="#sthref16" onclick='footdisplay(1,"\u003ccode\u003eCHAR\u003c/code\u003e maps to \u003ccode\u003eSQL_WCHAR\u003c/code\u003e if the character semantics were specified in the column definition and if the character set for the database is Unicode. ")'><sup class="tablefootnote">Foot&nbsp;1&nbsp;</sup></a></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r3c1-t18" headers="r1c1-t18" align="left">
<p><code>VARCHAR2</code></p>
</td>
<td headers="r3c1-t18 r1c2-t18" align="left">
<p><code>SQL_VARCHAR</code> or <code>SQL_WVARCHAR</code> <a id="sthref17" name="sthref17" href="#sthref17" onclick='footdisplay(2,"\u003ccode\u003eVARCHAR2\u003c/code\u003e maps to \u003ccode\u003eSQL_WVARCHAR\u003c/code\u003e if the character semantics were specified in the column definition and if the character set for the database is Unicode.")'><sup class="tablefootnote">Foot&nbsp;2&nbsp;</sup></a></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r4c1-t18" headers="r1c1-t18" align="left">
<p><code>NCHAR</code></p>
</td>
<td headers="r4c1-t18 r1c2-t18" align="left">
<p><code>SQL_WCHAR</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r5c1-t18" headers="r1c1-t18" align="left">
<p><code>NVARCHAR2</code></p>
</td>
<td headers="r5c1-t18 r1c2-t18" align="left">
<p><code>SQL_WVARCHAR</code></p>
</td>
</tr>
<tr align="left" valign="top">
<td id="r6c1-t18" headers="r1c1-t18" align="left">
<p><code>NCLOB</code></p>
</td>
<td headers="r6c1-t18 r1c2-t18" align="left">
<p><code>SQL_WLONGVARCHAR</code></p>
</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="tblformal" -->
<p class="tablefootnote"><sup class="tablefootnote">Footnote&nbsp;1&nbsp;</sup><code>CHAR</code> maps to <code>SQL_WCHAR</code> if the character semantics were specified in the column definition and if the character set for the database is Unicode.</p>
<p class="tablefootnote"><sup class="tablefootnote">Footnote&nbsp;2&nbsp;</sup><code>VARCHAR2</code> maps to <code>SQL_WVARCHAR</code> if the character semantics were specified in the column definition and if the character set for the database is Unicode.</p>
</div>
<!-- class="sect3" -->
<a id="BABFEHCB" name="BABFEHCB"></a><a id="ADFNS1220" name="ADFNS1220"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Unicode Functions in the Driver Manager</h4>
<p>The Driver Manager performs the following functions when it detects that the underlying ODBC driver does not support Unicode:</p>
<ul>
<li>
<p>Convert Unicode function calls to ANSI function calls before calling
the ANSI ODBC driver. String arguments will be converted from Unicode
to the local code page. For example, a call to <code>SQLPrepareW</code> is converted to call <code>SQLPrepare</code>. The text of the SQL statement parameter is converted from Unicode to the local code page.</p>
</li>
<li>
<p>Convert return parameters that are character data from the local
code page to Unicode. For example, returning the column name through <code>SQLColAttribute</code>.</p>
</li>
<li>
<p>Convert data from the local code page to Unicode for columns bound as <code>SQL_C_WCHAR</code>.</p>
</li>
<li>
<p>Convert data from Unicode to the local code page for input parameters bound as <code>SQL_C_WCHAR</code>.</p>
</li>
</ul>
</div>
<!-- class="sect3" -->
<a id="BABECFEA" name="BABECFEA"></a><a id="ADFNS1221" name="ADFNS1221"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">SQLGetData Performance</h4>
<p>The <code>SQLGetData</code> function allows an ODBC application to
specify the data type to receive a column as after the data has been
fetched. OCI requires the Oracle ODBC Driver to specify the data type
before it is fetched. In this case, the Oracle ODBC Driver uses the
knowledge it has about the data type of the column as defined in the
database to determine how to best default to fetching the column
through OCI.</p>
<p>If a column that contains character data is not bound by <code>SQLBindCol</code>,
the Oracle ODBC Driver needs to determine if it should fetch the column
as Unicode or as the local code page. The driver could always default
to receiving the column as Unicode, however, this may result in as many
as two unnecessary conversions. For example, if the data were encoded
in the database as ANSI, there would be an ANSI to Unicode conversion
to fetch the data into the Oracle ODBC Driver. If the ODBC application
then requested the data as <code>SQL_C_CHAR</code>, there would be an additional conversion to revert the data back to its original encoding.</p>
<p>The default encoding of the Oracle client will be used when fetching
data. However, an ODBC application may overwrite this default and fetch
the data as Unicode by binding the column or the parameter as the <code>WCHAR</code> data type.</p>
</div>
<!-- class="sect3" -->
<a id="BABEADIA" name="BABEADIA"></a><a id="ADFNS1222" name="ADFNS1222"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Unicode Samples</h4>
<p>As the Oracle ODBC Driver itself was implemented using TCHAR macros,
it is recommended that ODBC application programs use TCHAR in order to
take advantage of the driver.</p>
<p>The following links are program examples showing how to use TCHAR, which becomes the <code>WCHAR</code> data type in case you compile with UNICODE and _UNICODE.</p>
<ul>
<li>
<p><a href="#BABHJIII">Example 1: Connection to Database</a></p>
</li>
<li>
<p><a href="#BABEDAGC">Example 2: Simple Retrieval</a></p>
</li>
<li>
<p><a href="#BABDCEJH">Example 3: Retrieval Using SQLGetData (Binding After Fetch)</a></p>
</li>
<li>
<p><a href="#BABCJHID">Example 4: Simple Update</a></p>
</li>
<li>
<p><a href="#BABJAAJH">Example 5: Update and Retrieval for Long Data (CLOB)</a></p>
</li>
</ul>
<a id="BABHJIII" name="BABHJIII"></a><a id="ADFNS1223" name="ADFNS1223"></a>
<p class="subhead2">Example 1: Connection to Database</p>
<p>No difference other than specifying Unicode literals for <code>SQLConnect</code>.</p>
<pre xml:space="preserve" class="oac_no_warn">HENV envHnd;<br />HDBC conHnd<br />;<br />HSTMT stmtHnd;<br />RETCODE rc;<br /><br />rc = SQL_SUCCESS;<br /><br /> <br />// ENV is allocated<br />rc = SQLAllocEnv(&amp;envHnd);<br />// Connection Handle is allocated<br />rc = SQLAllocConnect(envHnd, &amp;conHnd);<br />rc = SQLConnect(conHnd, _T("stpc19"), SQL_NTS, _T("scott"), SQL_NTS, _T("tiger"),<br /> SQL_NTS);<br />.<br />.<br />.<br />if (conHnd)<br />SQLFreeConnect(conHnd);<br />if (envHnd)<br />  SQLFreeEnv(envHnd);<br /></pre>
<a id="BABEDAGC" name="BABEDAGC"></a><a id="ADFNS1224" name="ADFNS1224"></a>
<p class="subhead2">Example 2: Simple Retrieval</p>
<p>The following example retrieves the employee names and the job titles from the <code>EMP</code>
table. With the exception that you need to specify TCHAR compliant data
to every ODBC function, there is no difference to the ANSI case. If the
case is a Unicode application, you have to specify the length of the
buffer to the <code>BYTE</code> length when you call <code>SQLBindCol</code> (for example, <code>sizeof(ename)</code> ).</p>
<pre xml:space="preserve" class="oac_no_warn">/*<br />** Execute SQL, bind columns, and Fetch.<br />** Procedure:<br />**<br />** SQLExecDirect<br />** SQLBindCol<br />** SQLFetch<br />** <br />*/<br />static SQLTCHAR *sqlStmt = _T("SELECT ename, job FROM emp");<br />SQLTCHAR ename[50];<br />SQLTCHAR job[50];<br />SQLINTEGER enamelen, joblen;<br /> <br />_tprintf(_T("Retrieve ENAME and JOB using SQLBindCol 1.../n[%s]/n"), sqlStmt);<br /> <br />// Step 1: Prepare and Execute<br />rc = SQLExecDirect(stmtHnd, sqlStmt, SQL_NTS); // select<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 2: Bind Columns<br />rc = SQLBindCol(stmtHnd,<br />1,<br />SQL_C_TCHAR,<br />ename,<br />sizeof(ename),<br />&amp;enamelen);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />rc = SQLBindCol(stmtHnd,<br />2,<br />SQL_C_TCHAR,<br />job,<br />sizeof(job),<br />&amp;joblen);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />do <br />{<br />// Step 3: Fetch Data<br />rc = SQLFetch(stmtHnd);<br />if (rc == SQL_NO_DATA)<br />break;<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br />_tprintf(_T("ENAME = %s, JOB = %s/n"), ename, job);<br />} while (1);<br />_tprintf(_T("Finished Retrieval/n/n"));<br /></pre>
<a id="BABDCEJH" name="BABDCEJH"></a><a id="ADFNS1225" name="ADFNS1225"></a>
<p class="subhead2">Example 3: Retrieval Using SQLGetData (Binding After Fetch)</p>
<p>This example shows how to use <code>SQLGetData</code>. For those who are not familiar with ODBC programming, the fetch is allowed before binding the data using <code>SQLGetData</code>, unlike in an OCI program. There is no difference to the ANSI application in terms of Unicode-specific issues.</p>
<pre xml:space="preserve" class="oac_no_warn">/*<br />** Execute SQL, bind columns, and Fetch.<br />** Procedure:<br />**<br />** SQLExecDirect<br />** SQLFetch<br />** SQLGetData<br />*/<br />static SQLTCHAR *sqlStmt = _T("SELECT ename,job FROM emp"); // same as Case 1.<br />SQLTCHAR ename[50];<br />SQLTCHAR job[50];<br /> <br />_tprintf(_T("Retrieve ENAME and JOB using SQLGetData.../n[%s]/n"), sqlStmt);<br />if (rc != SQL_SUCCESS)<br />{<br />_tprintf(_T("Failed to allocate STMT/n"));<br />goto exit2;<br />}<br /> <br />// Step 1: Prepare and Execute<br />rc = SQLExecDirect(stmtHnd, sqlStmt, SQL_NTS); // select<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br /><br />do <br />{<br /><br />// Step 2: Fetch<br />rc = SQLFetch(stmtHnd);<br />if (rc == SQL_NO_DAT<br />break;<br /><br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /><br /> <br />// Step 3: GetData<br />rc = SQLGetData(st<br />mtHnd, <br />1,<br />SQL_C_TCHAR,<br />(SQLPOINTER)ename,<br />sizeof(ename), <br />NULL);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br />rc = SQLGetData(stmtHnd, <br />2,<br />SQL_C_TCHAR, <br />(SQLPOINTER)job,<br />sizeof(job), <br />NULL);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br />_tprintf(_T("ENAME = %s, JOB = %s/n"), ename, job);<br />} while (1);<br />_tprintf(_T("Finished Retrieval/n/n"));<br /></pre>
<a id="BABCJHID" name="BABCJHID"></a><a id="ADFNS1226" name="ADFNS1226"></a>
<p class="subhead2">Example 4: Simple Update</p>
<p>This example shows how to update data. Likewise, the length of data for <code>SQLBindParameter</code> has to be specified with the <code>BYTE</code> length, even in the case of a Unicode application.</p>
<pre xml:space="preserve" class="oac_no_warn">/<br />*<br />** Execute SQL, bind columns, and Fetch.<br />** Procedure:<br />**<br />** SQLPrepare<br />** SQLBindParameter<br />** SQLExecute<br />*/<br />static SQLTCHAR *sqlStmt = _T("INSERT INTO emp(empno,ename,job) VALUES(?,?,?)");<br />static SQLTCHAR *empno = _T("9876"); // Emp No<br />static SQLTCHAR *ename = _T("ORACLE"); // Name<br />static SQLTCHAR *job = _T("PRESIDENT"); // Job<br /> <br />_tprintf(_T("Insert User ORACLE using SQLBindParameter.../n[%s]/n"), sqlStmt);<br /> <br />// Step 1: Prepar<br />rc = SQLPrepare(stmtHnd, sqlStmt, SQL_NTS); // select<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 2: Bind Parameterrc = SQLBindParameter(stmtHnd, <br />1, <br />SQL_PARAM_INPUT,<br />SQL_C_TCHAR,<br />SQL_DECIMAL,<br />4, // 4 digit<br />0,<br />(SQLPOINTER)empno,<br />0,<br />NULL);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />rc = SQLBindParameter(stmtHnd, <br />2, <br />SQL_PARAM_INPUT,<br />SQL_C_TCHAR,<br />SQL_CHAR,<br />lstrlen(ename)*sizeof(TCHAR),<br />0,<br />(SQLPOINTER)ename,<br />lstrlen(ename)*sizeof(TCHAR),<br />NULL);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />rc = SQLBindParameter(stmtHnd, <br />3, <br />SQL_PARAM_INPUT,<br />SQL_C_TCHAR,<br />SQL_CHAR,<br />lstrlen(job)*sizeof(TCHAR),<br />0,<br />(SQLPOINTER)job,<br />lstrlen(job)*sizeof(TCHAR),<br />NULL);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 3: Execute<br />rc = SQLExecute(stmtHnd);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /></pre>
<a id="BABJAAJH" name="BABJAAJH"></a><a id="ADFNS1227" name="ADFNS1227"></a>
<p class="subhead2">Example 5: Update and Retrieval for Long Data (CLOB)</p>
<p>This example may be the most complicated case to update and retrieve data for long data, like <code>CLOB</code>, in Oracle. Since the length of data should be always the <code>BYTE</code> length, <code>lstrlen(TCHAR data)*sizeof(TCHAR)</code> is needed to derive the <code>BYTE</code> length.</p>
<pre xml:space="preserve" class="oac_no_warn">/*<br />** Execute SQL, bind columns, and Fetch.<br />** Procedure:<br />**<br />** SQLPrepare<br />** SQLBindParameter<br />** SQLExecute<br />** SQLParamData<br />** SQLPutData<br />**<br />** SQLExecDirect<br />** SQLFetch<br />** SQLGetData<br />*/<br />static SQLTCHAR *sqlStmt1 = _T("INSERT INTO clobtbl(clob1) VALUES(?)");<br />static SQLTCHAR *sqlStmt2 = _T("SELECT clob1 FROM clobtbl");<br />SQLTCHAR clobdata[1001];<br />SQLTCHAR resultdata[1001];<br />SQLINTEGER ind = SQL_DATA_AT_EXEC;<br />SQLTCHAR *bufp;<br />int clobdatalen, chunksize, dtsize, retchklen;<br /> <br />_tprintf(_T("Insert CLOB1 using SQLPutData.../n[%s]/n"), sqlStmt1);<br /> <br />// Set CLOB Data<br />{<br />int i;<br />SQLTCHAR ch;<br />for (i=0, ch=_T('A'); i&lt; sizeof(clobdata)/sizeof(SQLTCHAR); ++i, ++ch)<br />{<br />if (ch &gt; _T('Z'))<br />ch = _T('A');<br />clobdata[i] = ch;<br />}<br />clobdata[sizeof(clobdata)/sizeof(SQLTCHAR)-1] = _T('/0');<br />}<br />clobdatalen = lstrlen(clobdata); // length of characters<br />chunksize = clobdatalen / 7; // 7 times to put<br /> <br />// Step 1: Prepare<br />rc = SQLPrepare(stmtHnd, sqlStmt1, SQL_NTS);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 2: Bind Parameter with SQL_DATA_AT_EXEC<br />rc = SQLBindParameter(stmtHnd, <br />1, <br />SQL_PARAM_INPUT,<br />SQL_C_TCHAR,<br />SQL_LONGVARCHAR,<br />clobdatalen*sizeof(TCHAR),<br />0,<br />(SQLPOINTER)clobdata,<br />clobdatalen*sizeof(TCHAR),<br />&amp;ind);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 3: Execute<br />rc = SQLExecute(stmtHnd);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 4: ParamData (initiation)<br />rc = SQLParamData(stmtHnd, (SQLPOINTER*)&amp;bufp); // set value<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />for (dtsize=0, bufp = clobdata;<br />dtsize &lt; clobdatalen;<br />dtsize += chunksize, bufp += chunksize)<br />{<br />int len;<br />if (dtsize+chunksize&lt;clobdatalen)<br />len = chunksize;<br />else<br />len = clobdatalen-dtsize;<br /> <br />// Step 5: PutData<br />rc = SQLPutData(stmtHnd, (SQLPOINTER)bufp, len*sizeof(TCHAR));<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br />}<br /> <br />// Step 6: ParamData (temination)<br />rc = SQLParamData(stmtHnd, (SQLPOINTER*)&amp;bufp);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />rc = SQLFreeStmt(stmtHnd, SQL_CLOSE);<br />_tprintf(_T("Finished Update/n/n"));<br />rc = SQLAllocStmt(conHnd, &amp;stmtHnd);<br />if (rc != SQL_SUCCESS)<br />{<br />_tprintf(_T("Failed to allocate STMT/n"));<br />goto exit2;<br />}<br /> <br />// Clear Result Data<br />memset(resultdata, 0, sizeof(resultdata));<br />chunksize = clobdatalen / 15; // 15 times to put<br /> <br />// Step 1: Prepare<br />rc = SQLExecDirect(stmtHnd, sqlStmt2, SQL_NTS); // select<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />// Step 2: Fetch<br />rc = SQLFetch(stmtHnd);<br />checkSQLErr(envHnd, conHnd, stmtHnd, rc);<br /> <br />for(dtsize=0, bufp = resultdata;<br />dtsize &lt; sizeof(resultdata)/sizeof(TCHAR) &amp;&amp; rc != SQL_NO_DATA;<br />dtsize += chunksize-1, bufp += chunksize-1)<br />{<br />int len; // len should contain the space for NULL termination<br />if (dtsize+chunksize&lt;sizeof(resultdata)/sizeof(TCHAR))<br />len = chunksize;<br />else<br />len = sizeof(resultdata)/sizeof(TCHAR)-dtsize;<br /> <br />// Step 3: GetData<br />rc = SQLGetData(stmtHnd, <br />1,<br />SQL_C_TCHAR,<br />(SQLPOINTER)bufp,<br />len*sizeof(TCHAR), <br />&amp;retchklen);<br />}<br />if (!_tcscmp(resultdata, clobdata))<br />{<br />_tprintf(_T("Succeeded!!/n/n"));<br />}<br />else<br />{<br />_tprintf(_T("Failed!!/n/n"));<br />}<br /></pre></div>
<!-- class="sect3" --></div>
<!-- class="sect2" -->
<a id="BABIIAIH" name="BABIIAIH"></a><a id="ADFNS1228" name="ADFNS1228"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Performance and Tuning</h3>
<ul>
<li>
<p><a href="#BABBEJFD">General ODBC Programming Tips</a></p>
</li>
<li>
<p><a href="#BABFHDGC">Data Source Configuration Options</a></p>
</li>
<li>
<p><a href="#BABDHDBB">DATE and TIMESTAMP Data Types</a></p>
</li>
</ul>
<a id="BABBEJFD" name="BABBEJFD"></a><a id="ADFNS1229" name="ADFNS1229"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">General ODBC Programming Tips</h4>
<p>This section describes some general programming tips to improve the performance of an ODBC application.</p>
<ul>
<li>
<p>Enable connection pooling if the application will frequently connect
and disconnect from a data source. Reusing pooled connections is
extremely efficient compared to reestablishing a connection.</p>
</li>
<li>
<p>Minimize the number of times a statement needs to be prepared. Where
possible, use bind parameters to make a statement reusable for
different parameter values. Preparing a statement once and executing it
several times is much more efficient than preparing the statement for
every <code>SQLExecute</code>.</p>
</li>
<li>
<p>Do not include columns in a <code>SELECT</code> statement of which you know the application will not retrieve; especially <code>LONG</code> columns. Due to the nature of the database server protocols, the ODBC Driver must fetch the entire contents of a <code>LONG</code> column if it is included in the <code>SELECT</code> statement, regardless if the application binds the column or does a <code>SQLGetData</code>.</p>
</li>
<li>
<p>If you are performing transactions that do not update the data source, set the <code>SQL_ATTR_ACCESS_MODE</code> attribute of the ODBC <code>SQLSetConnectAttr</code> function to <code>SQL_MODE_READ_ONLY</code>.</p>
</li>
<li>
<p>If you are not using ODBC escape clauses, set the <code>SQL_ATTR_NOSCAN</code> attribute of the ODBC <code>SQLSetConnectAttr</code> function or the ODBC <code>SQLSetStmtAttr</code> function to true.</p>
</li>
<li>
<p>Use the ODBC <code>SQLFetchScroll</code> function instead of the ODBC <code>SQLFetch</code> function for retrieving data from tables that have a large number of rows.</p>
</li>
<li>
<p>Enable OCI statement caching when the same SQL statements are used multiple times (<code>StatementCache=T</code>).</p>
</li>
<li>
<p>Binding <code>NUMBER</code> columns as <code>FLOAT</code> speeds up query execution (<code>BindAsFLOAT=T</code>).</p>
</li>
<li>
<p>While fetching <code>LONG</code> or <code>LONG RAW</code> set <code>MaxLargeData=&lt;value&gt;</code> for optimum performance.</p>
</li>
<li>
<p>Setting <code>UseOCIDescribeAny=T</code> for applications making heavy calls to small packaged procedures that return <code>Ref Cursor</code> will see performance improvement.</p>
</li>
</ul>
</div>
<!-- class="sect3" -->
<a id="BABFHDGC" name="BABFHDGC"></a><a id="ADFNS1230" name="ADFNS1230"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">Data Source Configuration Options</h4>
<p>This topic discusses performance implications of the following ODBC data source configuration options:</p>
<ul>
<li>
<p><a href="#BABICDCG">Enable Result Sets</a></p>
</li>
<li>
<p><a href="#BABGGIBJ">Enable LOBs</a></p>
</li>
<li>
<p><a href="#BABJDGJB">Bind TIMESTAMP as DATE</a></p>
</li>
<li>
<p><a href="#BABBBGDG">Enable Closing Cursors</a></p>
</li>
<li>
<p><a href="#BABFGFCH">Enable Thread Safety</a></p>
</li>
<li>
<p><a href="#BABCHCIA">Fetch Buffer Size</a></p>
</li>
</ul>
<a id="BABICDCG" name="BABICDCG"></a><a id="ADFNS1231" name="ADFNS1231"></a>
<p class="subhead2">Enable Result Sets</p>
<p>This option enables the support of returning result sets (for example, <code>RefCursor</code>) from procedure calls. The default is enabling the returning of result sets.</p>
<p>The ODBC Driver must query the database server to determine the set
of parameters for a procedure and their data types in order to
determine if there are any <code>RefCursor</code> parameters. This query incurs an additional network round trip the first time any procedure is prepared and executed.</p>
<a id="BABGGIBJ" name="BABGGIBJ"></a><a id="ADFNS1232" name="ADFNS1232"></a>
<p class="subhead2">Enable LOBs</p>
<p>This option enables the support of inserting and updating LOBs. The default is enabled.</p>
<p>The ODBC Driver must query the database server to determine the data types of each parameter in an <code>INSERT</code> or <code>UPDATE</code>
statement in order to determine if there are any LOB parameters. This
query incurs an additional network round trip the first time any <code>INSERT</code> or <code>UPDATE</code> is prepared and executed.</p>
<a id="BABJDGJB" name="BABJDGJB"></a><a id="ADFNS1233" name="ADFNS1233"></a>
<p class="subhead2">Bind TIMESTAMP as DATE</p>
<p>Binds <code>SQL_TIMESTAMP</code> parameters as the appropriate Oracle data type. If this option is set to <code>TRUE</code>, <code>SQL_TIMESTAMP</code> binds as the Oracle <code>DATE</code> data type. If this option is set to <code>FALSE</code>, <code>SQL_TIMESTAMP</code> binds as the Oracle <code>TIMESTAMP</code> data type (which is the default).</p>
<a id="BABBBGDG" name="BABBBGDG"></a><a id="ADFNS1234" name="ADFNS1234"></a>
<p class="subhead2">Enable Closing Cursors</p>
<p>The <code>SQL_CLOSE</code> option of the ODBC function, <code>SQLFreeStmt</code>,
is supposed to close associated cursors with a statement and discard
all pending results. The application can reopen the cursor by executing
the statement again without doing a <code>SQLPrepare</code> again. A
typical scenario for this would be an application which expects to be
idle for a while but will reuse the same SQL statement again. While the
application is idle, it may want to free up any associated server
resources.</p>
<p>The <a href="#BABGCEAA">Oracle Call Interface</a> (OCI), on which the Oracle ODBC Driver is layered, does not support the functionality of closing cursors. So, by default, the <code>SQL_CLOSE</code> option has no effect in the Oracle ODBC Driver. The cursor and associated resources remain open on the database server.</p>
<p>Enabling this option will cause the associated cursor to be closed
on the database server. However, this results in the parse context of
the SQL statement being lost. The ODBC application can execute the
statement again without calling <code>SQLPrepare</code>. However,
internally the ODBC Driver must prepare and execute the statement all
over. Enabling this option will have a severe performance impact on
applications that prepare a statement once and execute it repeatedly.</p>
<p>This option should only be enabled if freeing the resources on the server is absolutely necessary.</p>
<a id="BABFGFCH" name="BABFGFCH"></a><a id="ADFNS1235" name="ADFNS1235"></a>
<p class="subhead2">Enable Thread Safety</p>
<p>If an application is single threaded, this option can be disabled.
By default, the ODBC Driver ensures that access to all internal
structures (environment, connection, statement) are thread safe. Single
threaded applications can eliminate some of the thread safety overhead
by disabling this option. Disabling this option should show a minor
performance improvement.</p>
<a id="BABCHCIA" name="BABCHCIA"></a><a id="ADFNS1236" name="ADFNS1236"></a>
<p class="subhead2">Fetch Buffer Size</p>
<p>Set the Fetch Buffer Size in the <a href="#BABFJIJI">Oracle Options</a> tab of the <a href="#BABEFEHG">Oracle ODBC Driver Configuration Dialog Box</a>
to a value specified in bytes. This value is the amount of memory
needed that will determine how many rows of data the ODBC Driver will
pre-fetch at a time from an Oracle database to the client's cache
regardless of the number of rows the application program requests in a
single query, thus improving performance.</p>
<p>Applications that typically fetch fewer than 20 rows of data at a
time will see an improvement in response time, particularly over slow
network connections or to heavily loaded servers. Setting this too high
can actually make response time worse or consume large amounts of
memory. The default is 64,000 bytes. Choose a value that works best for
your application.</p>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
When <code>LONG</code> and LOB data types
are present, the number of rows pre-fetched by the ODBC Driver is not
determined by the Fetch Buffer Size. The inclusion of the <code>LONG</code>
and LOB data types minimizes the performance improvement and could
result in excessive memory use. The ODBC Driver will disregard the
Fetch Buffer Size and only pre-fetch a set number of rows in the
presence of the <code>LONG</code> and LOB data types.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
</div>
<!-- class="sect3" -->
<a id="BABDHDBB" name="BABDHDBB"></a><a id="ADFNS1237" name="ADFNS1237"></a>
<div class="sect3"><!-- infolevel="all" infotype="General" -->
<h4 class="sect3">DATE and TIMESTAMP Data Types</h4>
<p>If a <code>DATE</code> column in the database is used in a <code>WHERE</code> clause and the column has an index, there can be an impact on performance. For example:</p>
<pre xml:space="preserve" class="oac_no_warn">SELECT * FROM EMP WHERE HIREDATE = ?<br /></pre>
<p>In this example, an index on the <code>HIREDATE</code> column could be used to make the query execute quickly. But, because <code>HIREDATE</code> is actually a <code>DATE</code> value and the ODBC Driver is supplying the parameter value as <code>TIMESTAMP</code>,
the Oracle server's query optimizer must apply a conversion function.
To prevent incorrect results (as might happen if the parameter value
had nonzero fractional seconds), the optimizer applies the conversion
to the <code>HIREDATE</code> column resulting in the following statement:</p>
<pre xml:space="preserve" class="oac_no_warn">SELECT * FROM EMP WHERE TO_TIMESTAMP(HIREDATE) = ?<br /></pre>
<p>Unfortunately, this has the effect of disabling the use of the index on the <code>HIREDATE</code>
column and instead the server performs a sequential scan of the table.
If the table has many rows, this can take a long time. As a workaround
for this situation, the ODBC Driver has the connection option to <code>Bind TIMESTAMP as DATE</code>. When this option is enabled, the ODBC Driver binds <code>SQL_TIMESTAMP</code> parameters as the Oracle <code>DATE</code> data type instead of the Oracle <code>TIMESTAMP</code> data type. This allows the query optimizer to use any index on the <code>DATE</code> columns.</p>
<div align="center">
<div class="inftblnote"><br />
<table class="Note oac_no_warn" summary="" border="1" cellpadding="3" cellspacing="0" frame="hsides" rules="groups" width="80%">
<tbody>
<tr>
<td align="left">
<p class="notep1">Note:</p>
This option is intended only for use with Microsoft Access or other similar programs that bind <code>DATE</code> columns as <code>TIMESTAMP</code> columns. It should not be used when there are actual <code>TIMESTAMP</code>
columns present or when data loss may occur. Microsoft Access executes
such queries using whatever columns are selected as the primary key.</td>
</tr>
</tbody>
</table>
<br /></div>
<!-- class="inftblnote" --></div>
<a id="ADFNS1238" name="ADFNS1238"></a>
<p class="subhead2">Related Topic</p>
<p><a href="#BABBAEEH">Implementation of Data Types (Advanced)</a></p>
</div>
<!-- class="sect3" --></div>
<!-- class="sect2" --></div>
<!-- class="sect1" -->
<a id="BABJHHHH" name="BABJHHHH"></a><a id="ADFNS1239" name="ADFNS1239"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">Glossary</h2>
<a id="BABECBJJ" name="BABECBJJ"></a><a id="ADFNS1240" name="ADFNS1240"></a>
<p class="subhead2">API</p>
<p>Application Program Interface. A set of program functions or calls
that allow an application to make use of, or communicate with, an
underlying program or system.</p>
<a id="ADFNS1241" name="ADFNS1241"></a>
<p class="subhead2">Client</p>
<p>A client is a software program that accesses data by selecting a
service provided by a server using an agreed upon interface. The server
responds by receiving and processing client requests, and sending the
results back to the client. ODBC client applications use the ODBC
Driver API to call ODBC functions to submit SQL statements and retrieve
results.</p>
<a id="BABJACEF" name="BABJACEF"></a><a id="ADFNS1242" name="ADFNS1242"></a>
<p class="subhead2">Conformance Levels</p>
<p>Some applications can only use drivers that support certain levels
of functionality or conformance levels. For example, an application
might want to set the cursor position in a rowset and allow an
application to refresh data in the rowset. This ability is part of the
Level 1 conformance level for the Application Programming Interface
(API).</p>
<p>ODBC drivers conform to Core API level and part of Level 1 and Level
2 and is broadly compatible with the SQL-99 Core specification. Drivers
may support some of the functionality in levels above their stated
level.</p>
<p>For detailed information about what is in the various conformance
levels, programmers should see the Microsoft ODBC 3.52 Software
Development Kit and Programmer's Reference.</p>
<a id="BABFICBD" name="BABFICBD"></a><a id="ADFNS1243" name="ADFNS1243"></a>
<p class="subhead2">Data Source</p>
<p>A data source includes information about the data a user wants to
access and information on how to access that data. For the Oracle ODBC
Driver, a data source is an alias for a specific instance of an Oracle
database and the Oracle Net Services components used for communication
to the Oracle database.</p>
<a id="BABIIHGJ" name="BABIIHGJ"></a><a id="ADFNS1244" name="ADFNS1244"></a>
<p class="subhead2">DLL</p>
<p>Dynamic Link Library. A set of routines that one or more
applications can use to perform common tasks. The ODBC drivers are DLLs
on the Windows platform and Shared Object (.so) files on the UNIX
platform.</p>
<a id="ADFNS1245" name="ADFNS1245"></a>
<p class="subhead2">Driver Manager</p>
<p>The Driver Manager, which is provided by Microsoft and unixODBC,
loads drivers on behalf of an application when the application calls
the ODBC <code>SQLConnect</code> or ODBC <code>SQLDriverConnect</code> functions.</p>
<a id="ADFNS1246" name="ADFNS1246"></a>
<p class="subhead2">Network Transports</p>
<p>A network is made up of communications software and hardware through
which an OCI client on a machine communicates with an Oracle server on
another computer system. Message requests from the OCI client and
response requests from the server travel over a Oracle Net Services
communications link that can support a variety of network transports.</p>
<a id="BABFCGBI" name="BABFCGBI"></a><a id="ADFNS1247" name="ADFNS1247"></a>
<p class="subhead2">ODBC</p>
<p>Open Database Connectivity. A Driver Manager and a set of ODBC
drivers that enable applications to access data using SQL as a standard
language.</p>
<a id="ADFNS1248" name="ADFNS1248"></a>
<p class="subhead2">ODBC Application</p>
<p>The ODBC application performs processing and calls ODBC functions to
submit SQL statements and retrieve results. The application can access
multiple data drivers, each of which accesses a different data source.</p>
<a id="ADFNS1249" name="ADFNS1249"></a>
<p class="subhead2">ODBC Driver</p>
<p>Microsoft's Open Database Connectivity (ODBC) provides a standard
interface that allows one application to access many different data
sources. The application's source code does not have to be recompiled
for each data source. An ODBC driver is a dynamic-link library (DLL) on
the Windows platform or a Shared Object (SO) on the UNIX platform that
applications can invoke on demand to gain access to a data source. An
ODBC driver links the application to a specific data source and
processes ODBC function calls, submits SQL requests to a specific data
source, and returns results to the application. If necessary, the ODBC
driver modifies an application's request so that the request conforms
to syntax supported by the associated data source. The Driver Manager
and the ODBC driver appear to an application as one unit that processes
ODBC function calls. The Oracle ODBC Driver allows read and write
access to Oracle databases only.</p>
<p>ODBC drivers are now available for UNIX platforms which works with
3rd party open source Driver Managers (DM). Oracle ODBC Driver has been
certified using DM provided by <code>unixODBC.org</code> on Unix platforms. (For platform details, refer to the <code>ODBC_Readme_Unix.html</code> file.</p>
<a id="ADFNS1250" name="ADFNS1250"></a>
<p class="subhead2">Oracle</p>
<p>Oracle is a high performance, high availability, scalable, general
purpose, multiuser, database management system based on the relational
model extended with objects that runs on a wide variety of computer
operating systems. Oracle Database 12g is available in a choice of
editions tailored to meet the business and IT needs of needs of all
sizes of organizations. It supports a full set of utilities and an
industry-standard SQL data definition and data manipulation language
that lets you create, query, and maintain your Oracle databases. Oracle
supports VLDB, high-end OLTP and data warehouse applications, and
contains enhanced management capabilities using the Oracle Enterprise
Manager GUI and enhanced security using Oracle Advanced Security.</p>
<p>Additionally, Oracle offers a range of Enterprise Edition Options to
meet your most demanding requirements for mission-critical transaction
processing, data warehousing, and content management applications.
These options include: Oracle Active Data Guard, Oracle Advanced
Compression, Oracle Advanced Security, Oracle Database Vault, Oracle
Data Mining, Oracle In-Memory Database Cache, Oracle Label Security,
Oracle OLAP, Oracle Partitioning, Oracle Real Application Clusters,
Real Application Testing, Oracle Spatial, Oracle Total Recall, Oracle
Warehouse Builder Enterprise ETL Option, Oracle Warehouse Builder Data
Quality Option, Oracle Warehouse Builder Connections, and Oracle
Content Database Suite.</p>
<p>In addition, Oracle provides an integrated management solution for
managing Oracle database with a unique top-down application management
approach. With new self-managing capabilities, Oracle eliminates
time-consuming, error-prone administrative tasks, so database
administrators can focus on strategic business objectives instead of
performance and availability fire drills. Oracle provides the following
database management packs: Oracle Change Management Pack, Oracle
Configuration Management Pack, Oracle Data Masking, Oracle Diagnostic
Pack, Pack, Oracle Provisioning and Patch Automation Pack, and Oracle
Tuning Pack.</p>
<p>Oracle also offers a variety of related products that include:
Oracle Audit Vault, Oracle Secure Backup, Oracle Programmer, Oracle
TimesTen In-Memory Database, Oracle Berkeley DB, and Oracle Database
Lite.</p>
<a id="BABGCEAA" name="BABGCEAA"></a><a id="ADFNS1251" name="ADFNS1251"></a>
<p class="subhead2">Oracle Call Interface</p>
<p>The Oracle Call Interface (OCI) is a set of standard software
routines (program call interface) used to access an Oracle server. OCI
allows users to embed Oracle calls directly into high-level languages.</p>
<a id="BABFDACF" name="BABFDACF"></a><a id="ADFNS1252" name="ADFNS1252"></a>
<p class="subhead2">Oracle Net Services</p>
<p>Oracle Corporation's family of networking products that
transparently integrates clients, servers, and gateways to deliver a
unified information resource using industry-standard or proprietary
network protocols.</p>
<a id="BABFGCHB" name="BABFGCHB"></a><a id="ADFNS1253" name="ADFNS1253"></a>
<p class="subhead2">Oracle Net Configuration Assistant (NETCA)</p>
<p>A utility to easily configure and maintain an Oracle Net Services network by updating entries in the <code>tnsnames.ora</code> file for each of your TNS Service Names.</p>
<a id="BABJABEH" name="BABJABEH"></a><a id="ADFNS1254" name="ADFNS1254"></a>
<p class="subhead2">SQL</p>
<p>Structured Query Language. The internationally accepted standard for
relational systems, covering not only query but also data definition,
manipulation, security, and some aspects of referential integrity.</p>
<a id="ADFNS1255" name="ADFNS1255"></a>
<p class="subhead2">TCP/IP</p>
<p>Transmission Control Protocol (TCP)/Internet Protocol (IP). An
interprocess communication mechanism used by applications to share data
across a network. TCP allows a process on one machine to send a stream
of data to a process on another machine. Software implementing TCP
usually resides in the operating system and uses the IP to transmit
information across the Internet.</p>
</div>
<!-- class="sect1" -->
<a id="BABCEBCD" name="BABCEBCD"></a><a id="ADFNS1256" name="ADFNS1256"></a>
<div class="sect1"><!-- infolevel="all" infotype="General" -->
<h2 class="sect1">Acknowledgements</h2>
<p>This section contains the following acknowledgements:</p>
<ul>
<li>
<p><a href="#BABIICBI">Copyright and Trademark Acknowledgements</a></p>
</li>
<li>
<p><a href="#BABIFHIA">Accessibility Statements</a></p>
</li>
</ul>
<a id="BABIICBI" name="BABIICBI"></a><a id="ADFNS1257" name="ADFNS1257"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Copyright and Trademark Acknowledgements</h3>
<p>Oracle ODBC Driver, Release ********.0</p>
<p>Copyright &#169; 1993, 2013 Oracle. All rights reserved.</p>
<p>This software and related documentation are provided under a license
agreement containing restrictions on use and disclosure and are
protected by intellectual property laws. Except as expressly permitted
in your license agreement or allowed by law, you may not use, copy,
reproduce, translate, broadcast, modify, license, transmit, distribute,
exhibit, perform, publish, or display any part, in any form, or by any
means. Reverse engineering, disassembly, or decompilation of this
software, unless required by law for interoperability, is prohibited.</p>
<p>The information contained herein is subject to change without notice
and is not warranted to be error-free. If you find any errors, please
report them to us in writing.</p>
<p>If this is software or related documentation that is delivered to
the U.S. Government or anyone licensing it on behalf of the U.S.
Government, the following notice is applicable:</p>
<p>U.S. GOVERNMENT END USERS: Oracle programs, including any operating
system, integrated software, any programs installed on the hardware,
and/or documentation, delivered to U.S. Government end users are
"commercial computer software" pursuant to the applicable Federal
Acquisition Regulation and agency-specific supplemental regulations. As
such, use, duplication, disclosure, modification, and adaptation of the
programs, including any operating system, integrated software, any
programs installed on the hardware, and/or documentation, shall be
subject to license terms and license restrictions applicable to the
programs. No other rights are granted to the U.S. Government.</p>
<p>This software or hardware is developed for general use in a variety
of information management applications. It is not developed or intended
for use in any inherently dangerous applications, including
applications that may create a risk of personal injury. If you use this
software or hardware in dangerous applications, then you shall be
responsible to take all appropriate fail-safe, backup, redundancy, and
other measures to ensure its safe use. Oracle Corporation and its
affiliates disclaim any liability for any damages caused by use of this
software or hardware in dangerous applications.</p>
<p>Oracle and Java are registered trademarks of Oracle and/or its
affiliates. Other names may be trademarks of their respective owners.</p>
<p>Intel and Intel Xeon are trademarks or registered trademarks of
Intel Corporation. All SPARC trademarks are used under license and are
trademarks or registered trademarks of SPARC International, Inc. AMD,
Opteron, the AMD logo, and the AMD Opteron logo are trademarks or
registered trademarks of Advanced Micro Devices. UNIX is a registered
trademark of The Open Group.</p>
<p>This software or hardware and documentation may provide access to or
information on content, products, and services from third parties.
Oracle Corporation and its affiliates are not responsible for and
expressly disclaim all warranties of any kind with respect to
third-party content, products, and services. Oracle Corporation and its
affiliates will not be responsible for any loss, costs, or damages
incurred due to your access to or use of third-party content, products,
or services.</p>
</div>
<!-- class="sect2" -->
<a id="BABIFHIA" name="BABIFHIA"></a><a id="ADFNS1258" name="ADFNS1258"></a>
<div class="sect2"><!-- infolevel="all" infotype="General" -->
<h3 class="sect2">Accessibility Statements</h3>
<p>For information about Oracle's commitment to accessibility, visit the Oracle Accessibility Program website at <code><a href="http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=docacc">http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=docacc</a></code>.</p>
<a id="ADFNS1259" name="ADFNS1259"></a><a id="sthref18" name="sthref18"></a>
<p class="subhead2">Access to Oracle Support</p>
<p>Oracle customers have access to electronic support through My Oracle Support. For information, visit <code><a href="http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=info">http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=info</a></code> or visit <code><a href="http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=trs">http://www.oracle.com/pls/topic/lookup?ctx=acc&amp;id=trs</a></code> if you are hearing impaired.</p>
</div>
<!-- class="sect2" --></div>
<!-- class="sect1" --></div>
<!-- class="ind" -->
<!-- Start Footer -->
<div class="footer">
<hr />
<table class="simple oac_no_warn" summary="" cellpadding="0" cellspacing="0" width="100%">
<col width="6%" />
<col width="6%" />
<col width="1*" />
<col width="6%" />
<tbody><tr>
<td align="center"><a href="toc.htm"><br />
<span class="icon">Previous</span></a>&nbsp;</td>
<td align="center"><span class="copyrightlogo">Copyright&nbsp;&#169;&nbsp;1993,&nbsp;2013,&nbsp;Oracle&nbsp;and/or&nbsp;its&nbsp;affiliates.&nbsp;All&nbsp;rights&nbsp;reserved.</span><br />
<a href="cpyr.htm"><span class="copyrightlogo">Legal Notices</span></a></td>
<td align="right" valign="top"><a href="toc.htm"><br />
<span class="icon">Contents</span></a></td>
</tr>
</tbody></table>
</div>
<!-- class="footer" -->
</body></html>
