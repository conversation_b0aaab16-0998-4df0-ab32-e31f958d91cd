// Any comment. You must start the file with a comment!

// =================================================================
// THESE ARE THE PROPERTIES THAT MUST BE ENABLED FOR JUGGLER TO WORK
// =================================================================

pref("datareporting.policy.dataSubmissionEnabled", false);
pref("datareporting.policy.dataSubmissionPolicyAccepted", false);
pref("datareporting.policy.dataSubmissionPolicyBypassNotification", true);

// Force pdfs into downloads.
pref("pdfjs.disabled", true);

// Disable cross-process iframes, but not cross-process navigations.
pref("fission.webContentIsolationStrategy", 0);

// Disable BFCache in parent process.
// We also separately disable B<PERSON><PERSON> in content via docSchell property.
pref("fission.bfcacheInParent", false);

// Disable first-party-based cookie partitioning.
// When it is enabled, we have to retain "thirdPartyCookie^" permissions
// in the storageState.
pref("network.cookie.cookieBehavior", 4);

// Increase max number of child web processes so that new pages
// get a new process by default and we have a process isolation
// between pages from different contexts. If this becomes a performance
// issue we can povide custom '@mozilla.org/ipc/processselector;1'
pref("dom.ipc.processCount", 60000);

// Never reuse processes as they may keep previously overridden values
// (locale, timezone etc.).
pref("dom.ipc.processPrelaunch.enabled", false);

// Isolate permissions by user context.
pref("permissions.isolateBy.userContext", true);

// Allow creating files in content process - required for
// |Page.setFileInputFiles| protocol method.
pref("dom.file.createInChild", true);

// Do not warn when closing all open tabs
pref("browser.tabs.warnOnClose", false);

// Do not warn when closing all other open tabs
pref("browser.tabs.warnOnCloseOtherTabs", false);

// Do not warn when multiple tabs will be opened
pref("browser.tabs.warnOnOpen", false);

// Do not warn on quitting Firefox
pref("browser.warnOnQuit", false);

// Disable popup-blocker
pref("dom.disable_open_during_load", false);

// Disable the ProcessHangMonitor
pref("dom.ipc.reportProcessHangs", false);
pref("hangmonitor.timeout", 0);

// Allow the application to have focus even it runs in the background
pref("focusmanager.testmode", true);

// No ICC color correction. We need this for reproducible screenshots.
// See https://developer.mozilla.org/en/docs/Mozilla/Firefox/Releases/3.5/ICC_color_correction_in_Firefox.
pref("gfx.color_management.mode", 0);
pref("gfx.color_management.rendering_intent", 3);

// Always use network provider for geolocation tests so we bypass the
// macOS dialog raised by the corelocation provider
pref("geo.provider.testing", true);




// =================================================================
// THESE ARE NICHE PROPERTIES THAT ARE NICE TO HAVE
// =================================================================

// Enable software-backed webgl. See https://phabricator.services.mozilla.com/D164016
pref("webgl.forbid-software", false);

// Disable auto-fill for credit cards and addresses.
// See https://github.com/microsoft/playwright/issues/21393
pref("extensions.formautofill.creditCards.supported", "off");
pref("extensions.formautofill.addresses.supported", "off");

// Allow access to system-added self-signed certificates. This aligns
// firefox behavior with other browser defaults.
pref("security.enterprise_roots.enabled", true);

// Avoid stalling on shutdown, after "xpcom-will-shutdown" phase.
// This at least happens when shutting down soon after launching.
// See AppShutdown.cpp for more details on shutdown phases.
pref("toolkit.shutdown.fastShutdownStage", 3);

// @see https://github.com/microsoft/playwright/issues/8178
pref("dom.postMessage.sharedArrayBuffer.bypassCOOP_COEP.insecure.enabled", true);

// Use light theme by default.
pref("ui.systemUsesDarkTheme", 0);

// Only allow the old modal dialogs. This should be removed when there is
// support for the new modal UI (see Bug 1686743).
pref("prompts.contentPromptSubDialog", false);

// Do not use system colors - they are affected by themes.
pref("ui.use_standins_for_native_colors", true);

pref("dom.push.serverURL", "");
// This setting breaks settings loading.
pref("services.settings.server", "");
pref("browser.safebrowsing.provider.mozilla.updateURL", "");
pref("browser.library.activity-stream.enabled", false);
pref("browser.search.geoSpecificDefaults", false);
pref("browser.search.geoSpecificDefaults.url", "");
pref("captivedetect.canonicalURL", "");
pref("network.captive-portal-service.enabled", false);
pref("network.connectivity-service.enabled", false);
pref("browser.newtabpage.activity-stream.asrouter.providers.snippets", "");

// Make sure Shield doesn't hit the network.
pref("app.normandy.api_url", "");
pref("app.normandy.enabled", false);

// Disable updater
pref("app.update.enabled", false);
// Disable Firefox old build background check
pref("app.update.checkInstallTime", false);
// Disable automatically upgrading Firefox
pref("app.update.disabledForTesting", true);

// make absolutely sure it is really off
pref("app.update.auto", false);
pref("app.update.mode", 0);
pref("app.update.service.enabled", false);
// Dislabe newtabpage
pref("browser.startup.homepage", "about:blank");
pref("browser.startup.page", 0);
pref("browser.newtabpage.enabled", false);
// Do not redirect user when a milstone upgrade of Firefox is detected
pref("browser.startup.homepage_override.mstone", "ignore");

// Disable topstories
pref("browser.newtabpage.activity-stream.feeds.section.topstories", false);
// DevTools JSONViewer sometimes fails to load dependencies with its require.js.
// This spams console with a lot of unpleasant errors.
// (bug 1424372)
pref("devtools.jsonview.enabled", false);

// Increase the APZ content response timeout in tests to 1 minute.
// This is to accommodate the fact that test environments tends to be
// slower than production environments (with the b2g emulator being
// the slowest of them all), resulting in the production timeout value
// sometimes being exceeded and causing false-positive test failures.
//
// (bug 1176798, bug 1177018, bug 1210465)
pref("apz.content_response_timeout", 60000);

// Indicate that the download panel has been shown once so that
// whichever download test runs first doesn't show the popup
// inconsistently.
pref("browser.download.panel.shown", true);
// Background thumbnails in particular cause grief, and disabling
// thumbnails in general cannot hurt
pref("browser.pagethumbnails.capturing_disabled", true);
// Disable safebrowsing components.
pref("browser.safebrowsing.blockedURIs.enabled", false);
pref("browser.safebrowsing.downloads.enabled", false);
pref("browser.safebrowsing.passwords.enabled", false);
pref("browser.safebrowsing.malware.enabled", false);
pref("browser.safebrowsing.phishing.enabled", false);
// Disable updates to search engines.
pref("browser.search.update", false);
// Do not restore the last open set of tabs if the browser has crashed
pref("browser.sessionstore.resume_from_crash", false);
// Don't check for the default web browser during startup.
pref("browser.shell.checkDefaultBrowser", false);

// Disable browser animations (tabs, fullscreen, sliding alerts)
pref("toolkit.cosmeticAnimations.enabled", false);

// Close the window when the last tab gets closed
pref("browser.tabs.closeWindowWithLastTab", true);

// Do not allow background tabs to be zombified on Android, otherwise for
// tests that open additional tabs, the test harness tab itself might get
// unloaded
pref("browser.tabs.disableBackgroundZombification", false);

// Disable first run splash page on Windows 10
pref("browser.usedOnWindows10.introURL", "");

// Disable the UI tour.
//
// Should be set in profile.
pref("browser.uitour.enabled", false);

// Turn off search suggestions in the location bar so as not to trigger
// network connections.
pref("browser.urlbar.suggest.searches", false);

// Do not show datareporting policy notifications which can
// interfere with tests
pref("datareporting.healthreport.documentServerURI", "");
pref("datareporting.healthreport.about.reportUrl", "");
pref("datareporting.healthreport.logging.consoleEnabled", false);
pref("datareporting.healthreport.service.enabled", false);
pref("datareporting.healthreport.service.firstRun", false);
pref("datareporting.healthreport.uploadEnabled", false);

// Automatically unload beforeunload alerts
pref("dom.disable_beforeunload", false);

// Disable slow script dialogues
pref("dom.max_chrome_script_run_time", 0);
pref("dom.max_script_run_time", 0);

// Only load extensions from the application and user profile
// AddonManager.SCOPE_PROFILE + AddonManager.SCOPE_APPLICATION
pref("extensions.autoDisableScopes", 0);
pref("extensions.enabledScopes", 5);

// Disable metadata caching for installed add-ons by default
pref("extensions.getAddons.cache.enabled", false);

// Disable installing any distribution extensions or add-ons.
pref("extensions.installDistroAddons", false);

// Turn off extension updates so they do not bother tests
pref("extensions.update.enabled", false);
pref("extensions.update.notifyUser", false);

// Make sure opening about:addons will not hit the network
pref("extensions.webservice.discoverURL", "");

pref("extensions.screenshots.disabled", true);
pref("extensions.screenshots.upload-disabled", true);

// Disable useragent updates
pref("general.useragent.updates.enabled", false);

// Do not scan Wifi
pref("geo.wifi.scan", false);

// Show chrome errors and warnings in the error console
pref("javascript.options.showInConsole", true);

// Disable download and usage of OpenH264: and Widevine plugins
pref("media.gmp-manager.updateEnabled", false);

// Do not prompt with long usernames or passwords in URLs
pref("network.http.phishy-userpass-length", 255);

// Do not prompt for temporary redirects
pref("network.http.prompt-temp-redirect", false);

// Disable speculative connections so they are not reported as leaking
// when they are hanging around
pref("network.http.speculative-parallel-limit", 0);

// Do not automatically switch between offline and online
pref("network.manage-offline-status", false);

// Make sure SNTP requests do not hit the network
pref("network.sntp.pools", "");

// Disable Flash
pref("plugin.state.flash", 0);

pref("privacy.trackingprotection.enabled", false);

pref("security.certerrors.mitm.priming.enabled", false);

// Local documents have access to all other local documents,
// including directory listings
pref("security.fileuri.strict_origin_policy", false);

// Tests do not wait for the notification button security delay
pref("security.notification_enable_delay", 0);

// Do not automatically fill sign-in forms with known usernames and
// passwords
pref("signon.autofillForms", false);

// Disable password capture, so that tests that include forms are not
// influenced by the presence of the persistent doorhanger notification
pref("signon.rememberSignons", false);

// Disable first-run welcome page
pref("startup.homepage_welcome_url", "about:blank");
pref("startup.homepage_welcome_url.additional", "");

// Prevent starting into safe mode after application crashes
pref("toolkit.startup.max_resumed_crashes", -1);
lockPref("toolkit.crashreporter.enabled", false);

pref("toolkit.telemetry.enabled", false);
pref("toolkit.telemetry.server", "");

// Disable downloading the list of blocked extensions.
pref("extensions.blocklist.enabled", false);

// Force Firefox Devtools to open in a separate window.
pref("devtools.toolbox.host", "window");

