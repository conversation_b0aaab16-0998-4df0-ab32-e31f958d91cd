import io
import os
import re
import socket
import sys
import tkinter as tk
import traceback
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
import pandas

import requests,json, time

import datetime
from datetime import  timedelta

import threading

from concurrent.futures import ThreadPoolExecutor

from tool import Tool

lock = threading.Lock()
# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称

def getmailtrace(mailno):

    #print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid2 + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_no': mailno,
        'numType': 15

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session2.post(url, headers=headers, params=data, verify=False)

    #print(response.text)
    data = json.loads(response.text)
    # 处理JSON数据
    results = []
    yjh=''
    sffx=''
    hgzl=''
    zldm=''
    zlsj=''
    nbgkffsj=''
    nbgkffmc=''
    nbgkkcsj=''
    nbgkkcmc=''
    fnbgkffsj=''
    jdjg=''
    ylmc=''
    fcsj=''

    for record in data:
        if '总包条码:' in record['internalDesc']  and record['opOrgCode'] == '51040034':
            # 提取总包号码
            pattern_num = r"总包条码:(\d+)"
            match_num = re.search(pattern_num, record['internalDesc'])

            if match_num:
                total_num = match_num.group(1)
                prefix = "5104003400000"
                if total_num.startswith(prefix):
                    # 判断处理方式
                    pattern_way = r"处理方式:(\S+)"
                    match_way = re.search(pattern_way, record['internalDesc'])
                    if match_way and '开拆' in match_way.group(1) :#== "批量开拆":
                        nbgkkcsj=record['opTime']
                        # 匹配原寄局（不包含后面的总包寄达局）
                        pattern_origin = r"总包原寄局:(.+?),"
                        match_origin = re.search(pattern_origin, record['internalDesc'])
                        if match_origin:
                            nbgkkcmc=match_origin.group(1)
                    else:
                        nbgkffsj=record['opTime']
                        # 匹配原寄局（不包含后面的总包寄达局）
                        pattern_origin = r"总包寄达局:(.+?),"
                        match_origin = re.search(pattern_origin, record['internalDesc'])
                        if match_origin:
                            nbgkffmc = match_origin.group(1)
                else:
                    fnbgkffsj=record['opTime']
                    # 匹配原寄局（不包含后面的总包寄达局）
                    pattern_origin = r"总包寄达局:(.+?),"
                    match_origin = re.search(pattern_origin, record['internalDesc'])
                    if match_origin:
                        jdjg = match_origin.group(1)

        if '处理中心封车' in record["opName"] and record['opOrgCode'] == '51040034':
            ylmc=re.findall(r'交接对象:(.*?)(?=\s\d+)', str(record["internalDesc"]))
            if ylmc:
                ylmc = ylmc[0]
                fcsj=record['opTime']
    lock.acquire()  # 锁住资源
    hz=getcustReceiptInfo(mailno)
    hgzl = hz[1]
    zldm = hz[0]
    zlsj = hz[2]
    if '海关放行' in hgzl:
        sffx='是'
    else:
        sffx='否'

    results.append({
        '邮件号': mailno,
        '是否放行': sffx,
        '海关指令': hgzl,
        '指令代码': zldm,
        '指令时间': zlsj,
        '内部格口封发时间': nbgkffsj,
        '内部格口封发名称': nbgkffmc,
        '内部格口开拆时间': nbgkkcsj,
        '内部格口开拆名称': nbgkkcmc,
        '非内部格口封发时间': fnbgkffsj,
        '寄达机构': jdjg,
        '邮路名称': ylmc,
        '封车时间': fcsj
    })
    # 释放线程锁
    lock.release()
    # 循环结束后释放session资源
    session.close()
    #print(results)
    return results

def getcustReceiptInfo(mailno):
    requests.packages.urllib3.disable_warnings()
    custReceiptCode = ''
    custReceiptInfo = ''
    custReceiptTimeStr = ''
    url = 'https://**********/intprep-web/a/intprep/queryInfo/querySortInfor'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/intprep-web/a/intprep/queryInfo/main',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'itemId': mailno

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    jsonObj = json.loads(r)
    if jsonObj:
        for line in jsonObj['detail']:
            custReceiptCode=line.get('custReceiptCode', '')
            custReceiptInfo = line.get('custReceiptInfo', '')
            custReceiptTimeStr = line.get('custReceiptTimeStr', '')


    return (custReceiptCode,custReceiptInfo,custReceiptTimeStr)


def run(title):
    try:
        global username, password, session, jdptid, L,session2, jdptid2

        # 构造Session
        session = requests.Session()
        session2 = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            session2.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)

        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        tool.process_input('开始登录新一代获取回执')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/intprep-web/a/intprep/queryInfo/main'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break

        i = 2

        tool.process_input('开始登录新一代获取轨迹')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        result = tool.getck(username, password, session2, url)
        jdptid2 = result[0]
        userName = result[1]

        while (jdptid2 == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session2, url)
            jdptid2 = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)

        # 启动线程池并发执行任务
        num_threads = int(threads_combobox.get())
        tool.process_input('当前线程:' + threads_combobox.get())

        # 获取API数据
        results = []

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(getmailtrace, mailno) for mailno in datalist]

            for future in futures:
                results.extend(future.result())


        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        if not results:
            print("没有返回任何记录")
            tool.process_input("没有找到任何记录")

        final_df = pandas.DataFrame(results)
        # 定义当前时间
        currentTime = datetime.datetime.now()
        final_df.to_excel("进口信息统计-" +currentTime.strftime("%Y%m%d%H%M%S")  + "-bylhx.xlsx", index=False)

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')

        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)


def handle_input(title):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run(title))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")




def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session,session2, jdptid, jdptid2,L, proxy_enabled, input_textbox, output_textbox,\
    submit_button,button_clear,  account_entry, password_entry,threads_combobox,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()
    session2 = requests.Session()
    # 创建主窗口
    #root = tk.Tk()

    today = datetime.datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)



    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=0, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=0, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title))
    submit_button.grid(row=1, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()