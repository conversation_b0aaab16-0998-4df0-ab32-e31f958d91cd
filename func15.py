import io
import os
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from urllib.parse import quote

from tool import Tool
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from bs4 import BeautifulSoup
import requests, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading
import socket
from os import path



# 修改全局变量 dataOutput 为线程安全的队列

lock = threading.Lock()
# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称

def getmail(parent,mailno):
    global L,dataOutput
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intprep-web/a/intprep/exchangeReplace/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded',
        'Host': '**********',
        'Referer': 'https://**********/intprep-web/a/intprep/exchangeReplace/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'itemId': mailno,
        'errorInfo': ''
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    html = BeautifulSoup(r, 'lxml')

    signway = html.find('tbody', {'id': 'signwaybillreturned_tbody'})
    if 'cusDeclareInfoOrigin' in str(signway):
        sbxxly = html.find('select', {'name': 'cusDeclareInfoOrigin'})  # 申报信息来源
        sbxxly = re.findall(r'selected="" value="(.+?)"', str(sbxxly))
        if sbxxly:
            sbxxly = sbxxly[0]
        else:
            sbxxly = ''
        sbhg = html.find('input', attrs={'name': 'custCode'})['value']  # 申报海关
        yzqymc = html.find('input', attrs={'name': 'postEntName'})['value']  # 邮政企业名称
        yzqydm = html.find('input', attrs={'name': 'postEntCode'})['value']  # 邮政企业代码
        js = html.find('input', attrs={'name': 'itemNum'})['value']  # 件数
        hgyjtsbz = html.find('select', attrs={'name': 'custSpecItemFlag'})  # 海关邮件特殊标志
        hgyjtsbz = re.findall(r'selected="" value="(.+?)"', str(hgyjtsbz))
        if hgyjtsbz:
            hgyjtsbz = hgyjtsbz[0]
        else:
            hgyjtsbz = ''
        jck = html.find('select', attrs={'name': 'impExp'})  # 进/出口
        jck = re.findall(r'selected="" value="(.+?)"', str(jck))
        if jck:
            jck = jck[0]
        else:
            jck = ''
        hggjdm = html.find('select', attrs={'name': 'nationCodess'})  # 海关国家代码
        nationCode = html.find('input', attrs={'name': 'nationCode'})['value']  # 原寄局list表
        hggjdm = re.findall(r'selected="" value="(.+?)"', str(hggjdm))
        if hggjdm:
            hggjdm = hggjdm[0]
        else:
            hggjdm = ''
        jdj = html.find('input', attrs={'name': 'destOrgName'})['value']  # 寄达局
        sblb = html.find('select', attrs={'name': 'custDeclareClassCode'})  # 申报类型
        sblb = re.findall(r'selected="" value="(.+?)"', str(sblb))
        if sblb:
            sblb = sblb[0]
        else:
            sblb = ''
        yjzl = html.find('select', attrs={'name': 'bizSect'})  # 邮件种类
        yjzl = re.findall(r'selected="" value="(.+?)"', str(yjzl))
        if yjzl:
            yjzl = yjzl[0]
        else:
            yjzl = ''
        yjj = html.find('input', attrs={'name': 'originOrgName'})['value']  # 原寄局
        crjsj = html.find('input', attrs={'name': 'crossBoundTime'})['value']  # 出入境时间
        zjlx = html.find('select', attrs={'name': 'senderIdClassCode'})  # 证件类型
        zjlx = re.findall(r'selected="" value="(.+?)"', str(zjlx))
        if zjlx:
            zjlx = zjlx[0]
        else:
            zjlx = ''
        jjrzjhm = html.find('input', attrs={'name': 'snederIdNo'})['value']  # 寄件人证件号码
        jjrdh = html.find('input', attrs={'name': 'senderPhone'})['value']  # 寄件人电话
        jjrqymc = html.find('input', attrs={'name': 'senderCompany'})['value']  # 寄件人企业名称
        jjrxm = html.find('input', attrs={'name': 'senderName'})['value'].replace('NULL', 'NONAME')  # 寄件人姓名
        jjrdz = html.find('input', attrs={'name': 'senderAddr'})['value']  # 寄件人地址
        sjrzjlx = html.find('select', attrs={'name': 'recipientIdClassCode'})  # 收件人证件类型
        sjrzjlx = re.findall(r'selected="" value="(.+?)"', str(sjrzjlx))
        if sjrzjlx:
            sjrzjlx = sjrzjlx[0]
        else:
            sjrzjlx = ''
        sjrzjhm = html.find('input', attrs={'name': 'recipientIdNo'})['value']  # 收件人证件号码
        sjrdh = html.find('input', attrs={'name': 'recipientAddress'})['value']  # 收件人电话
        sjrqymc = html.find('input', attrs={'name': 'recipientCompany'})['value']  # 收件人企业名称
        sjrxm = html.find('input', attrs={'name': 'recName'})['value'].replace('"', '').replace("'", '')  # 收件人姓名
        sjrdz = html.find('input', attrs={'name': 'recAddress'})['value'].replace('"', '').replace("'", '')  # 收件人地址
        zl = html.find('input', attrs={'name': 'totalWeight'})['value']  # 重量
        zl = float(zl) * 0.001
        zl = str(zl)
        sbbz = html.find('select', attrs={'name': 'declareCurrType'})  # 申报币种
        # sbbz = re.findall(r'selected="" value="(.+?)"', str(sbbz))
        #
        # if sbbz:
        #     sbbz = sbbz[0]
        # else:
        #     sbbz = ''
        # 找到所有的option标签
        #print(sbbz)
        #options = sbbz.find_all('option')
        # 找到所有带有 selected 属性的 option 标签
        options = sbbz.select('option[selected]')
        #print(options)
        if options:
            sbbz = options[0].text.strip()
        # 遍历所有的option标签，找到带有selected属性的文本
        # for option in options:
        #     if option.has_attr('selected'):
        #         second_dash_index = option.find('-', option.find('-') + 1)  # 找到第二个减号的索引
        #         if second_dash_index != -1:
        #             desired_text = option[second_dash_index + 1:].strip()  # 提取第二个减号后的字符
        #             sbbz=desired_text
        #             print(desired_text)
        #         break

        yzbz = html.find('input', attrs={'name': 'postageCurrType'})['value']  # 邮资币种
        yzaybzdjz = html.find('input', attrs={'name': 'postageFee'})['value']  # 邮资按邮币制的价值
        zywpmc = html.find('input', attrs={'name': 'contentDescription'})['value']  # 主要物品名称
        bz = html.find('input', attrs={'name': 'remTxt'})['value']  # 备注

        tbody = html.find('tbody', attrs={'id': 'btbody'})

        bizSerno = tbody.find_all('input', {'name': 'bizSerno'})
        bizName = tbody.find_all('input', {'name': 'bizName'})
        custDeclareSortCode = tbody.find_all('input', {'name': 'custDeclareSortCode'})
        goodsNum = tbody.find_all('input', {'name': 'goodsNum'})
        meterUnit = tbody.find_all('select', {'name': 'meterUnit'})
        meterUnit = re.findall(r'selected="" value="(.+?)"', str(meterUnit))
        declareTotalValue = tbody.find_all('input', {'name': 'declareTotalValue'})
        # specModel=tbody.find_all('input',{'name':'specModel'})
        sumtotalvalue = 0.00
        for k in range(len(meterUnit)):
            Serno = re.findall(r'value="(.+?)"', str(bizSerno[k]))[0]
            Name = re.findall(r'value="(.+?)"', str(bizName[k]))[0].replace('NULL', 'NONAME')
            DeclareSortCode = re.findall(r'value="(.+?)"', str(custDeclareSortCode[k]))
            if DeclareSortCode:
                DeclareSortCode = DeclareSortCode[0]
            else:
                DeclareSortCode = '00000000'
            Num = re.findall(r'value="(.+?)"', str(goodsNum[k]))[0]
            Unit = meterUnit[k]
            TotalValue = re.findall(r'value="(.+?)"', str(declareTotalValue[k]))[0]
            sumtotalvalue = sumtotalvalue + float(TotalValue)


            # print ("商品序号:"+Serno+",商品名称:"+Name+",行邮物品归纳编码:"+DeclareSortCode+",数量:"+Num+",价值:"+TotalValue)
        sumtotalvalue = str(sumtotalvalue)

        dataOutput['邮件号'].append(mailno)
        dataOutput['关区代码'].append(sbhg)
        dataOutput['原寄局'].append(yjj)
        dataOutput['寄达地'].append(jdj)
        dataOutput['价值'].append(sumtotalvalue)
        dataOutput['重量'].append(zl)
        dataOutput['币种'].append(sbbz)
        dataOutput['收件人电话'].append(sjrdh)
        dataOutput['收件人姓名'].append(sjrxm)
        dataOutput['收件人地址'].append(sjrdz)
        dataOutput['内件'].append(zywpmc)
        dataOutput['备注'].append(bz)

        L += 1
        if L % 1000 == 0:
            # 添加适当的同步机制，例如使用 threading.Lock

            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()


def run(title,parent):
    try:
        global username, password, session, jdptid, L,dataOutput
        dataOutput = {
            "邮件号": [],
            "关区代码": [],
            "原寄局": [],
            "寄达地": [],
            "价值": [],
            "重量": [],
            "币种": [],
            "收件人电话": [],
            "收件人姓名": [],
            "收件人地址": [],
            "内件": [],
            "备注": []

        }

        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************'==ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()
        password = password_entry.get()

        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')

        url='https://**********/intprep-web/a/intprep/exchangeReplace/list'
        result=tool.getck(username,password,session,url)
        jdptid = result[0]
        userName=result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName,title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #pool.map(lambda mailno: getmail(mailno, parent), datalist)
        # 创建一个偏函数，其中root参数预先设定
        getmailtrace_bound = partial(getmail, parent)
        pool.map(getmailtrace_bound, datalist)
        tool.process_input('处理完毕')
        if bool(dataOutput):
            tool.process_input('正在写入Excel')

            # 定义当前时间
            currentTime = datetime.datetime.now()
            dataForm = pandas.DataFrame(dataOutput)
            row = 1048570
            length = len(dataForm)
            number = length // row
            for i in range(number + 1):
                dataForm[i * row:(i + 1) * row].to_excel("邮件补录信息-" +
                                                         currentTime.strftime("%Y%m%d%H%M%S")  + "-bylhx.xlsx",
                                                         index=False)

            tool.process_input("写入完成共" + str(number + 1) + "个文件")
        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        #del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()




def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox, \
        submit_button, button_clear, start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, \
        business_label, business_combobox, business_options, selected, jlyy_textbox,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建主窗口
    #root = tk.Tk()

    #today = datetime.datetime.today()
    #yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)


    # 创建代理功能勾选框的变量
    #proxy_enabled = tk.BooleanVar()

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["国际"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项


    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=0, column=1, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=0, column=2, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行",command=lambda: handle_input(title,func_window))
    submit_button.grid(row=1, column=1, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=2, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)
    # 创建工具类实例
    tool = Tool(output_textbox, account_entry,password_entry,func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单", command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
