import os
import re
import json
import sys
import asyncio
from datetime import datetime
from playwright.async_api import async_playwright

# 配置
OUTPUT_DIR = "js_analysis"
CAPTURED_JS_DIR = "captured_js"
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(CAPTURED_JS_DIR, exist_ok=True)

# 目标关键词和模式
TARGET_PATTERNS = [
    "_\\$\$O",            # 目标函数1
    "_\\$hK",              # 目标函数2
    "_\\$jX",              # 目标函数3
    "MG9Olv7Z",            # 目标参数
    "enable_3bg4b5fckQas", # 目标Cookie1
    "3bg4b5fckQasP",       # 目标Cookie2
]

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

async def download_js_files(page):
    """下载所有JavaScript文件"""
    log_message("开始下载JavaScript文件...")
    
    # 添加请求拦截器记录JS请求
    js_resources = []
    async def on_response(response):
        if response.url.endswith('.js'):
            js_resources.append(response)
    
    page.on('response', on_response)
    
    # 访问目标页面
    try:
        await page.goto("https://**********/cas/login", timeout=30000)
        log_message("页面加载完成，等待网络请求完成...")
        await page.wait_for_load_state('networkidle')
    except Exception as e:
        log_message(f"页面加载失败: {e}")
        return []
    
    # 下载所有JS文件
    downloaded_files = []
    for response in js_resources:
        url = response.url
        filename = url.split('/')[-1]
        file_path = os.path.join(CAPTURED_JS_DIR, filename)
        
        try:
            content = await response.text()
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            log_message(f"下载文件: {filename}")
            downloaded_files.append(file_path)
        except Exception as e:
            log_message(f"下载文件失败 {url}: {e}")
    
    # 特别尝试下载目标文件
    try:
        log_message("尝试专门下载目标JS文件...")
        target_file = await page.evaluate("""
            async function getFile() {
                try {
                    // 尝试多个可能的路径
                    const paths = [
                        '/cas/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js',
                        '/querypush-web/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js'
                    ];
                    
                    for (const path of paths) {
                        try {
                            const response = await fetch(path);
                            if (response.ok) {
                                return {
                                    path: path,
                                    content: await response.text()
                                };
                            }
                        } catch (e) {
                            console.error("Fetch failed for " + path, e);
                        }
                    }
                    return null;
                } catch (e) {
                    console.error("Error in getFile", e);
                    return null;
                }
            }
            getFile();
        """)
        
        if target_file and target_file.get("content"):
            filename = target_file["path"].split('/')[-1]
            file_path = os.path.join(CAPTURED_JS_DIR, filename)
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(target_file["content"])
            log_message(f"成功下载关键文件: {filename}")
            downloaded_files.append(file_path)
    except Exception as e:
        log_message(f"下载关键文件失败: {e}")
    
    # 尝试提取eval中的代码
    await extract_eval_content(page)
    
    log_message(f"共下载 {len(downloaded_files)} 个JavaScript文件")
    return downloaded_files

async def extract_eval_content(page):
    """提取eval执行的JavaScript代码"""
    log_message("尝试提取eval执行的代码...")
    
    try:
        eval_code = await page.evaluate("""
        async function captureEval() {
            // 保存原始eval
            const originalEval = window.eval;
            
            // 收集的eval代码
            const capturedCode = [];
            
            // 替换eval以捕获代码
            window.eval = function(code) {
                capturedCode.push(code);
                return originalEval.call(this, code);
            };
            
            // 等待一段时间让页面执行脚本
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 恢复原始eval
            window.eval = originalEval;
            
            return capturedCode;
        }
        captureEval();
        """)
        
        if eval_code and len(eval_code) > 0:
            log_message(f"成功捕获 {len(eval_code)} 段eval执行代码")
            
            # 保存每段eval代码到文件
            for i, code in enumerate(eval_code):
                file_path = os.path.join(CAPTURED_JS_DIR, f"eval_code_{i+1}.js")
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(code)
                log_message(f"保存eval代码片段到: {file_path}")
        else:
            log_message("未捕获到eval执行代码")
    
    except Exception as e:
        log_message(f"提取eval代码失败: {e}")

def analyze_js_files(file_paths):
    """分析下载的JavaScript文件"""
    log_message("开始分析JavaScript文件...")
    
    results = {
        "timestamp": datetime.now().isoformat(),
        "file_count": len(file_paths),
        "matches": {},
        "function_definitions": {},
        "eval_calls": {},
        "cookie_operations": {},
        "param_generation": {},
    }
    
    # 分析每个文件
    for file_path in file_paths:
        file_name = os.path.basename(file_path)
        log_message(f"分析文件: {file_name}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_results = {
                "size": len(content),
                "matches": find_pattern_matches(content, TARGET_PATTERNS),
                "functions": extract_target_functions(content),
                "eval_calls": find_eval_calls(content),
                "cookie_operations": find_cookie_operations(content),
                "param_generation": find_param_generation(content),
            }
            
            # 只保存有意义的结果
            has_relevant_data = (
                len(file_results["matches"]) > 0 or
                len(file_results["functions"]) > 0 or
                len(file_results["eval_calls"]) > 0 or
                len(file_results["cookie_operations"]) > 0 or
                len(file_results["param_generation"]) > 0
            )
            
            if has_relevant_data:
                results["matches"][file_name] = file_results["matches"]
                results["function_definitions"][file_name] = file_results["functions"]
                results["eval_calls"][file_name] = file_results["eval_calls"]
                results["cookie_operations"][file_name] = file_results["cookie_operations"]
                results["param_generation"][file_name] = file_results["param_generation"]
                
                # 保存单个文件的分析结果
                save_file_analysis(file_name, file_results)
        
        except Exception as e:
            log_message(f"分析文件 {file_name} 时出错: {e}")
    
    # 保存整体分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(OUTPUT_DIR, f"analysis_summary_{timestamp}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    log_message(f"分析完成，结果保存到: {output_file}")
    return results

def find_pattern_matches(content, patterns):
    """查找内容中的目标模式"""
    results = {}
    
    for pattern in patterns:
        matches = list(re.finditer(pattern, content))
        if matches:
            contexts = []
            for match in matches:
                start = max(0, match.start() - 100)
                end = min(len(content), match.end() + 100)
                context = content[start:end]
                contexts.append({
                    "position": match.start(),
                    "context": context,
                })
            results[pattern] = {
                "count": len(matches),
                "contexts": contexts[:5]  # 只保存前5个匹配，避免文件过大
            }
    
    return results

def extract_target_functions(content):
    """提取目标函数定义"""
    results = []
    
    # 查找函数定义
    function_patterns = [
        # 常规函数声明
        r'function\s+(_\$\$O|_\$hK|_\$jX)(\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
        # 函数表达式
        r'(?:var|let|const)\s+(_\$\$O|_\$hK|_\$jX)\s*=\s*function(\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
        # 对象方法
        r'(_\$\$O|_\$hK|_\$jX)\s*:\s*function(\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
    ]
    
    for pattern in function_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            function_name = match.group(1)
            function_body = match.group(0)
            
            # 处理嵌套括号问题
            brace_count = 0
            in_function = False
            function_end = 0
            
            for i, char in enumerate(function_body):
                if char == '{':
                    brace_count += 1
                    in_function = True
                elif char == '}':
                    brace_count -= 1
                    if in_function and brace_count == 0:
                        function_end = i + 1
                        break
            
            # 如果找到完整函数体
            if function_end > 0:
                complete_function = function_body[:function_end]
                results.append({
                    "name": function_name,
                    "code": complete_function,
                    "length": len(complete_function)
                })
    
    return results

def find_eval_calls(content):
    """查找eval调用"""
    results = []
    
    # 寻找eval调用
    eval_patterns = [
        r'eval\s*\(([^;]+?)(?:\)|;)',
        r'Function\s*\([^)]*\)\s*\(\)',
        r'new Function\s*\([^)]*\)'
    ]
    
    for pattern in eval_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            # 获取上下文
            start = max(0, match.start() - 100)
            end = min(len(content), match.end() + 100)
            context = content[start:end]
            
            results.append({
                "pattern": pattern,
                "position": match.start(),
                "context": context,
                "contains_target": any(target in context for target in ["_$$O", "_$hK", "MG9Olv7Z", "3bg4b5fckQasP"])
            })
    
    return results

def find_cookie_operations(content):
    """查找Cookie操作"""
    results = []
    
    # Cookie操作模式
    cookie_patterns = [
        r'document\.cookie\s*=\s*([^;]+)',
        r'(?:get|set)Cookie\s*\(\s*[\'"]([^\'"]+)[\'"]',
    ]
    
    target_cookies = ["enable_3bg4b5fckQas", "3bg4b5fckQasP"]
    
    for pattern in cookie_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            # 获取上下文
            start = max(0, match.start() - 100)
            end = min(len(content), match.end() + 100)
            context = content[start:end]
            
            # 检查是否包含目标Cookie
            is_target = any(cookie in context for cookie in target_cookies)
            
            if is_target:
                results.append({
                    "pattern": pattern,
                    "position": match.start(),
                    "context": context,
                    "target_cookie": True
                })
    
    return results

def find_param_generation(content):
    """查找参数生成代码"""
    results = []
    
    # 参数生成模式
    param_patterns = [
        r'MG9Olv7Z\s*=\s*([^;]+)',
        r'[\'"]+MG9Olv7Z[\'"]+\s*:\s*([^,}]+)',
        r'[?\&]MG9Olv7Z=([^&\'"]+)',
    ]
    
    for pattern in param_patterns:
        matches = re.finditer(pattern, content)
        for match in matches:
            # 获取上下文
            start = max(0, match.start() - 100)
            end = min(len(content), match.end() + 100)
            context = content[start:end]
            
            results.append({
                "pattern": pattern,
                "position": match.start(),
                "context": context,
                "param_value": match.group(1) if len(match.groups()) > 0 else None
            })
    
    return results

def save_file_analysis(file_name, analysis_results):
    """保存单个文件的分析结果"""
    output_file = os.path.join(OUTPUT_DIR, f"{file_name}_analysis.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2)
    
    log_message(f"保存{file_name}分析结果到: {output_file}")

def extract_encryption_logic():
    """从分析结果中提取加密逻辑"""
    log_message("开始提取加密逻辑...")
    
    # 查找所有分析文件
    analysis_files = []
    for root, _, files in os.walk(OUTPUT_DIR):
        for file in files:
            if file.endswith("_analysis.json"):
                analysis_files.append(os.path.join(root, file))
    
    if not analysis_files:
        log_message("未找到分析文件，请先运行分析")
        return
    
    # 加密逻辑提取结果
    encryption_logic = {
        "target_functions": [],
        "cookie_generators": [],
        "param_generators": [],
        "related_code": []
    }
    
    # 分析每个文件的结果
    for file_path in analysis_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 提取目标函数
            if "functions" in results:
                for func in results["functions"]:
                    if func["name"] in ["_$$O", "_$hK", "_$jX"]:
                        encryption_logic["target_functions"].append(func)
            
            # 提取Cookie生成逻辑
            if "cookie_operations" in results:
                for op in results["cookie_operations"]:
                    if op.get("target_cookie"):
                        encryption_logic["cookie_generators"].append(op)
            
            # 提取参数生成逻辑
            if "param_generation" in results:
                encryption_logic["param_generators"].extend(results["param_generation"])
            
            # 提取相关代码片段
            if "matches" in results:
                for pattern, match_data in results["matches"].items():
                    if pattern in ["_\\$\\$O", "_\\$hK", "MG9Olv7Z"]:
                        for context in match_data.get("contexts", []):
                            encryption_logic["related_code"].append({
                                "pattern": pattern,
                                "context": context.get("context", "")
                            })
        
        except Exception as e:
            log_message(f"处理分析文件 {file_path} 时出错: {e}")
    
    # 保存提取的加密逻辑
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(OUTPUT_DIR, f"encryption_logic_{timestamp}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(encryption_logic, f, indent=2)
    
    log_message(f"加密逻辑提取完成，结果保存到: {output_file}")
    
    # 创建加密逻辑可读报告
    create_readable_report(encryption_logic, timestamp)
    
    return encryption_logic

def create_readable_report(encryption_logic, timestamp):
    """创建可读的加密逻辑报告"""
    report_file = os.path.join(OUTPUT_DIR, f"encryption_report_{timestamp}.md")
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# MG9Olv7Z 参数生成逻辑分析报告\n\n")
        f.write(f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n")
        
        # 核心函数
        f.write("## 核心函数\n\n")
        if encryption_logic["target_functions"]:
            for i, func in enumerate(encryption_logic["target_functions"]):
                f.write(f"### {i+1}. 函数 `{func['name']}`\n\n")
                f.write("```javascript\n")
                f.write(func["code"])
                f.write("\n```\n\n")
        else:
            f.write("*未找到核心函数定义*\n\n")
        
        # Cookie 生成
        f.write("## Cookie 生成逻辑\n\n")
        if encryption_logic["cookie_generators"]:
            for i, generator in enumerate(encryption_logic["cookie_generators"]):
                f.write(f"### {i+1}. Cookie 操作\n\n")
                f.write("```javascript\n")
                f.write(generator["context"])
                f.write("\n```\n\n")
        else:
            f.write("*未找到 Cookie 生成逻辑*\n\n")
        
        # 参数生成
        f.write("## 参数生成逻辑\n\n")
        if encryption_logic["param_generators"]:
            for i, generator in enumerate(encryption_logic["param_generators"]):
                f.write(f"### {i+1}. 参数生成\n\n")
                if generator.get("param_value"):
                    f.write(f"参数值: `{generator['param_value']}`\n\n")
                f.write("```javascript\n")
                f.write(generator["context"])
                f.write("\n```\n\n")
        else:
            f.write("*未找到参数生成逻辑*\n\n")
        
        # 相关代码
        f.write("## 相关代码片段\n\n")
        if encryption_logic["related_code"]:
            shown_contexts = set()  # 避免重复片段
            for i, code in enumerate(encryption_logic["related_code"]):
                context = code["context"]
                if context in shown_contexts:
                    continue
                shown_contexts.add(context)
                f.write(f"### {i+1}. 包含 `{code['pattern']}` 的代码片段\n\n")
                f.write("```javascript\n")
                f.write(context)
                f.write("\n```\n\n")
        else:
            f.write("*未找到相关代码片段*\n\n")
        
        # 总结和建议
        f.write("## 分析总结\n\n")
        f.write("根据静态分析结果，MG9Olv7Z 参数生成的关键路径为：\n\n")
        f.write("1. 首先设置 `enable_3bg4b5fckQas=true` Cookie\n")
        f.write("2. 通过 `_$$O → _$hK → _$$O` 函数链生成 `3bg4b5fckQasP` Cookie\n")
        f.write("3. 最后通过相同的函数链生成 MG9Olv7Z 参数值\n\n")
        f.write("要模拟此过程，需要：\n\n")
        f.write("1. 提取关键函数的完整实现\n")
        f.write("2. 分析函数参数和返回值的关系\n")
        f.write("3. 重现Cookie设置和参数生成的完整流程\n")
    
    log_message(f"可读报告已生成: {report_file}")

async def main():
    """主函数"""
    log_message("开始JS静态分析，不依赖运行时调试...")
    
    # 命令行参数处理
    download_only = "--download-only" in sys.argv
    analyze_only = "--analyze-only" in sys.argv
    extract_only = "--extract-only" in sys.argv
    
    if download_only:
        log_message("仅执行下载步骤")
    elif analyze_only:
        log_message("仅执行分析步骤")
    elif extract_only:
        log_message("仅执行加密逻辑提取步骤")
    
    # 下载JavaScript文件
    if not analyze_only and not extract_only:
        async with async_playwright() as p:
            browser = await p.firefox.launch(
                headless=False,
                args=["--disable-blink-features=AutomationControlled"]
            )
            context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
            page = await context.new_page()
            
            downloaded_files = await download_js_files(page)
            await browser.close()
            
            if download_only:
                log_message("下载完成，退出程序")
                return
    else:
        # 查找已下载文件
        downloaded_files = []
        if os.path.exists(CAPTURED_JS_DIR):
            for file in os.listdir(CAPTURED_JS_DIR):
                if file.endswith('.js'):
                    downloaded_files.append(os.path.join(CAPTURED_JS_DIR, file))
        
        log_message(f"找到 {len(downloaded_files)} 个已下载的JavaScript文件")
    
    # 分析JavaScript文件
    if not download_only and not extract_only and downloaded_files:
        analyze_js_files(downloaded_files)
        
        if analyze_only:
            log_message("分析完成，退出程序")
            return
    
    # 提取加密逻辑
    if not download_only and not analyze_only:
        extract_encryption_logic()
    
    log_message("分析完成")

if __name__ == "__main__":
    asyncio.run(main()) 