import cx_Oracle
import pandas as pd
import re

# 连接数据库
def get_db_connection():
    dsn_tns = cx_Oracle.makedsn('10.194.69.26', '1521', service_name='gkxt')
    print("正在连接数据库...")
    return cx_Oracle.connect(user='c##zxjtotal', password='Gzyqzxj2019.', dsn=dsn_tns)

# 查询 bcmc 是否为中文
def is_chinese(text):
    return bool(re.search(r'[\u4e00-\u9fa5]', text))

# 查询 bcmc
def get_bcmc(cursor, ygxm, pbrq):
    query = f"SELECT bcmc FROM TB_SYGL_CBGK_PBXX WHERE ygxm='{ygxm}' AND pbrq='{pbrq}'"
    print(f"查询 bcmc: ygxm='{ygxm}', pbrq='{pbrq}'")
    cursor.execute(query)
    result = cursor.fetchone()
    if result:
        print(f"找到 bcmc: {result[0]}")
        return result[0]
    else:
        print("没有找到 bcmc")
        return None

# 执行更新操作
def update_bcmc(cursor, ygxm, pbrq):
    update_query = f"""
    UPDATE TB_SYGL_CBGK_PBXX 
    SET bcmc=NULL 
    WHERE ygxm='{ygxm}' AND pbrq='{pbrq}'
    """
    print(f"执行更新操作: ygxm='{ygxm}', pbrq='{pbrq}'")
    cursor.execute(update_query)

# 读取Excel文件
df = pd.read_excel('2.xlsx')  # 替换为你的文件路径

# 批量更新
def batch_update():
    print("开始批量更新...")
    conn = get_db_connection()
    cursor = conn.cursor()

    for index, row in df.iterrows():
        ygxm = row['姓名']
        pbrq = row['日期']

        print(f"处理第 {index + 1} 行数据: 姓名='{ygxm}', 日期='{pbrq}'")

        # 查询 bcmc
        bcmc = get_bcmc(cursor, ygxm, pbrq)

        # 如果 bcmc 是中文，则更新为空
        if bcmc and is_chinese(bcmc):
            update_bcmc(cursor, ygxm, pbrq)

    # 提交事务并关闭连接
    conn.commit()
    print("批量更新完成，已提交事务。")
    cursor.close()
    conn.close()

# 调用批量更新函数
batch_update()
