@echo off
set "new_path=D:\Tesseract-OCR"

REM 检查新路径是否已经存在于 PATH 中
for %%I in ("%new_path%") do set "full_path=%%~dpI"
echo 新路径: %new_path%
echo 完整路径: %full_path%

echo 当前 PATH: %PATH%
echo %PATH% | findstr /C:"%full_path%;" > nul
if %errorlevel% equ 0 (
    echo 路径 %new_path% 已经存在于环境变量 PATH 中。
) else (
    REM 将新路径添加到环境变量 PATH 中
    setx PATH "%PATH%;%new_path%" > nul
    if %errorlevel% equ 0 (
        echo 成功将路径 %new_path% 添加到环境变量 PATH 中。
    ) else (
        echo 添加路径 %new_path% 到环境变量 PATH 失败。
    )
)
