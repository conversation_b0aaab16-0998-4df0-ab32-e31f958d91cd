import os
import shutil
import subprocess
import site
import PyInstaller.__main__


def run_command(command):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    output, error = process.communicate()
    if process.returncode != 0:
        print(f"Error executing command: {command}")
        print(error.decode())
        raise RuntimeError(f"Command failed: {command}")
    return output.decode()


def generate_requirements():
    print("Generating requirements.txt...")
    try:
        run_command("pip install pip-chill")
        with open("requirements.txt", "w") as f:
            f.write(run_command("pip-chill --no-version"))
    except Exception as e:
        print(f"Error generating requirements.txt: {e}")
        print("Falling back to pip freeze...")
        with open("requirements.txt", "w") as f:
            f.write(run_command("pip freeze"))

if __name__ == "__main__":
    app_name = "OA办结神器"
    spec_file = f"{app_name}.spec"

    # 生成 requirements.txt
    generate_requirements()

    # 执行 PyInstaller 命令
    PyInstaller.__main__.run([
        'oabj.py',                    # 主脚本
        '--onefile',                  # 打包成单个文件
        '--icon=OA办公系统.ico',      # 图标
        '--noconsole',               # 隐藏命令行窗口
        '--distpath=dist',           # 输出目录
        f'--name={app_name}',        # 应用程序名称
        '--hidden-import=babel.numbers',  # 必要的隐藏导入
        '--hidden-import=tkinter',    # 添加tkinter作为隐藏导入
        '--hidden-import=_tkinter',   # 添加_tkinter作为隐藏导入
        '--hidden-import=pandas',     # 添加pandas作为隐藏导入
        '--hidden-import=numpy',      # pandas依赖numpy
        '--clean',                   # 清理临时文件
        '--noconfirm',              # 不询问确认
        # 添加必要的数据文件
        '--add-data=config.json;.',  # 配置文件
        '--add-data=chinapost.ico;.',  # 窗口图标文件
        # 排除不必要的模块
        '--exclude-module=matplotlib',
        '--exclude-module=PIL',
        '--exclude-module=scipy',
    ])

    print("Packaging complete!")
