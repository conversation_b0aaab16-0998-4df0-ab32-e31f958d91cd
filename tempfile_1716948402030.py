
import pandas as pd
import random

# 获取下个月的第一天
next_month_start = pd.Timestamp.now() + pd.DateOffset(months=1)
# 获取下个月的最后一天
next_month_end = (next_month_start + pd.DateOffset(months=1) - pd.DateOffset(days=1))

# 定义员工
employees = ['A', 'B', 'C']

# 创建一个空的DataFrame来存储排班信息
schedule = pd.DataFrame(index=pd.date_range(start=next_month_start, end=next_month_end), columns=['Employee', 'Shift', 'EmployeeShift'])

# 初始化日班和夜班的计数器
day_shifts, night_shifts = [0] * len(employees), [0] * len(employees)
total_hours = [0] * len(employees)

# 遍历日期
for date in schedule.index:
    # 检查前一天是否在排班表的索引范围内
    prev_date = date - pd.Timedelta(days=1)
    if prev_date not in schedule.index:
        prev_date = schedule.index[0]  # 如果不在索引内，设为排班表的第一个日期

    # 每天必须有一个夜班
    night_employee_index = night_shifts.index(min(night_shifts))
    if night_employee_index is None:
        night_employee_index = 0  # 如果所有员工都已经完成了夜班，从头开始
    night_employee = employees[night_employee_index]
    schedule.loc[date, 'Shift'] = 'Night'
    schedule.loc[date, 'Employee'] = night_employee
    schedule.loc[date, 'EmployeeShift'] = f'{night_employee} - Night'
    night_shifts[night_employee_index] += 1
    total_hours[night_employee_index] += 10  # 夜班10小时

    # 动态安排日班，确保不超过184小时
    for employee in employees:
        if day_shifts[employees.index(employee)] < 6:  # 每人至少6天日班
            if schedule.loc[date, 'Shift'] == '' and schedule.loc[prev_date, 'Shift'] != 'Night':
                schedule.loc[date, 'Shift'] = 'Day'
                schedule.loc[date, 'Employee'] = employee
                schedule.loc[date, 'EmployeeShift'] = f'{employee} - Day'
                day_shifts[employees.index(employee)] += 1
                total_hours[employees.index(employee)] += 8  # 日班8小时
                break

    # 调整日班以保持总工时相对平衡
    for i in range(len(employees)):
        if total_hours[i] < 168:
            min_hours_employee_index = total_hours.index(min(total_hours))
            if schedule.loc[date, 'Shift'] == '' and schedule.loc[prev_date, 'Shift'] != 'Night':
                schedule.loc[date, 'Shift'] = 'Day'
                schedule.loc[date, 'Employee'] = employees[min_hours_employee_index]
                schedule.loc[date, 'EmployeeShift'] = f'{employees[min_hours_employee_index]} - Day'
                day_shifts[min_hours_employee_index] += 1
                total_hours[min_hours_employee_index] += 8

# 将日期列转换为仅日期字符串
schedule['Date'] = schedule.index.strftime('%Y-%m-%d')

# 将结果导出到Excel，只保留日期、Shift和EmployeeShift列
schedule[['Date', 'Shift', 'EmployeeShift']].to_excel('schedule.xlsx', index=False)