import asyncio
import tkinter as tk
from tkinter import ttk, messagebox
from tkcalendar import DateEntry
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from datetime import datetime
import pandas as pd
import json
import os
import sys
from os import path
import concurrent.futures
from tkinter import font as tkfont
import threading
import platform
from typing import Optional

def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)

# ================== 常量 ==================
BASE_URL = "https://tp.postoa.com.cn"
API_URL = BASE_URL + "/indexController/getMoreDb.done"
SUBMIT_URL = BASE_URL + "/worknoticeInfoController/zjbj.done"
ARCHIVES_SUBMIT_URL = BASE_URL + "/archivesInController/killWorkflow.done"
CONFIG_FILE = 'config.json'

# 默认配置
DEFAULT_CONFIG = {
    "orgs": {
        "手动选择": "",  # 添加空选项
        "生产管控2": "020088003",
        "邮件作业中心": "020088004",

    },
    'thread_count': 5,
    'idea': '已阅',
    'password': '',
    'org_name': '',
    'org_code': '',
    'target_date': ''
}

LOGIN_DATA = {}
daiban_records = []

# ================== 配置读取与保存 ==================
def load_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                try:
                    config = json.load(f)
                    print("=== 加载的配置文件内容 ===")
                    print(f"配置文件路径: {os.path.abspath(CONFIG_FILE)}")
                    print(f"配置内容: {config}")

                    # 确保所有必需的键都存在
                    for key in DEFAULT_CONFIG:
                        if key not in config:
                            print(f"配置中缺少键: {key}，使用默认值: {DEFAULT_CONFIG[key]}")
                            config[key] = DEFAULT_CONFIG[key]

                    # 特殊处理orgs字典，确保包含所有默认选项
                    if 'orgs' in config:
                        for org_name, org_code in DEFAULT_CONFIG['orgs'].items():
                            if org_name not in config['orgs']:
                                config['orgs'][org_name] = org_code

                    return config
                except json.JSONDecodeError:
                    print("配置文件格式错误，使用默认配置")
                    return DEFAULT_CONFIG
        print(f"配置文件不存在: {CONFIG_FILE}")
        return DEFAULT_CONFIG
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return DEFAULT_CONFIG

def save_config(new_config):
    try:
        # 先加载现有配置
        current_config = load_config()

        # 更新配置，保持现有的orgs不变
        config = {
            "orgs": current_config["orgs"],  # 保持现有的机构列表
            "thread_count": int(new_config.get('thread_count', DEFAULT_CONFIG['thread_count'])),
            "idea": str(new_config.get('idea', DEFAULT_CONFIG['idea'])),
            "password": str(new_config.get('password', '')),
            "org_name": str(new_config.get('org_name', '')),
            "org_code": str(new_config.get('org_code', '')),
            "target_date": str(new_config.get('target_date', ''))
        }

        # 如果有新的机构需要添加（在登录过程中手动选择的）
        if 'new_org_name' in new_config and 'new_org_code' in new_config:
            config['orgs'][new_config['new_org_name']] = new_config['new_org_code']

        # 格式化写入，确保文件格式正确
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

        print(f"配置已保存到: {os.path.abspath(CONFIG_FILE)}")
        print(f"保存的配置内容: {config}")

    except Exception as e:
        print(f"保存配置文件失败: {e}")

# ================== 登录界面 ==================
class LoginDialog:
    def __init__(self, saved_config):
        # print("=== LoginDialog初始化 ===")
        # print(f"接收到的配置: {saved_config}")

        self.root = tk.Tk()
        self.root.title("OA办结神器")
        self.root.geometry("450x700")  # 增加窗口高度从650到700
        self.root.resizable(False, False)
        self.root.configure(bg='#f0f2f5')

        # 设置窗口左上角图标
        try:
            # 使用get_resource_path函数获取图标文件路径
            icon_path = get_resource_path("chinapost.ico")
            self.root.iconbitmap(icon_path)
            print(f"成功设置窗口图标: {icon_path}")
        except Exception as e:
            print(f"设置窗口图标失败: {e}")

        # 设置样式
        style = ttk.Style()
        style.configure('TLabel', font=('Microsoft YaHei UI', 11), background='#f0f2f5')
        style.configure('TButton', font=('Microsoft YaHei UI', 11))
        style.configure('TEntry', font=('Microsoft YaHei UI', 11))
        style.configure('TFrame', background='#f0f2f5')

        main_frame = ttk.Frame(self.root, padding="20", style='TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Logo和标题区域
        title_frame = ttk.Frame(main_frame, style='TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk.Label(title_frame,
                               text="OA办结神器",
                               font=('Microsoft YaHei UI', 20, 'bold'),
                               foreground='#1890ff',
                               background='#f0f2f5')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame,
                                 text="自动化办理OA待办事项",
                                 font=('Microsoft YaHei UI', 11),
                                 foreground='#666666',
                                 background='#f0f2f5')
        subtitle_label.pack(pady=(5, 0))

        # 创建表单框架
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 15))

        WIDGET_WIDTH = 18

        # 第一行：机构选择
        ttk.Label(form_frame, text="选择机构", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=0, column=0, sticky='w', pady=(0, 5))
        ttk.Label(form_frame, text="线程数", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=0, column=1, sticky='w', pady=(0, 5))

        # 机构下拉框
        self.org_var = tk.StringVar()
        if saved_config.get('org_name'):
            self.org_var.set(saved_config['org_name'])

        self.org_dropdown = ttk.Combobox(
            form_frame,
            textvariable=self.org_var,
            values=list(saved_config['orgs'].keys()),
            state='readonly',
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        self.org_dropdown.grid(row=1, column=0, padx=(0, 10), pady=(0, 10))

        # 线程数下拉框
        self.thread_var = tk.StringVar()
        if saved_config.get('thread_count'):
            self.thread_var.set(str(saved_config['thread_count']))

        thread_dropdown = ttk.Combobox(
            form_frame,
            textvariable=self.thread_var,
            values=["5", "10", "15", "20"],
            state='readonly',
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        thread_dropdown.grid(row=1, column=1, pady=(0, 10))

        # 第二行：密码和日期
        ttk.Label(form_frame, text="密码", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=2, column=0, sticky='w', pady=(0, 5))
        ttk.Label(form_frame, text="日期", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=2, column=1, sticky='w', pady=(0, 5))

        # 密码输入框
        self.password_entry = ttk.Entry(
            form_frame,
            show="*",
            width=WIDGET_WIDTH,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('password'):
            self.password_entry.insert(0, saved_config['password'])
        self.password_entry.grid(row=3, column=0, padx=(0, 10), pady=(0, 10))

        # 日期选择框
        self.date_entry = DateEntry(
            form_frame,
            width=WIDGET_WIDTH,
            background='#1890ff',
            foreground='white',
            borderwidth=2,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('target_date'):
            try:
                date_obj = datetime.strptime(saved_config['target_date'], '%Y-%m-%d')
                self.date_entry.set_date(date_obj)
            except ValueError:
                pass
        self.date_entry.grid(row=3, column=1, pady=(0, 10))

        # 第三行：办结意见
        ttk.Label(form_frame, text="办结意见", font=('Microsoft YaHei UI', 11, 'bold')).grid(row=4, column=0, sticky='w', pady=(0, 5))

        # 办结意见输入框
        self.idea_entry = ttk.Entry(
            form_frame,
            width=WIDGET_WIDTH * 2 + 1,
            font=('Microsoft YaHei UI', 11)
        )
        if saved_config.get('idea'):
            self.idea_entry.insert(0, saved_config['idea'])
        self.idea_entry.grid(row=5, column=0, columnspan=2, pady=(0, 10))

        # 开始处理按钮
        button_frame = tk.Frame(main_frame, bg='#f0f2f5')
        button_frame.pack(pady=(0, 15))

        submit_btn = tk.Button(
            button_frame,
            text="开始处理",
            command=self.submit,
            font=('Microsoft YaHei UI', 11, 'bold'),
            fg='white',
            bg='#1890ff',
            activebackground='#096dd9',
            activeforeground='white',
            relief='flat',
            padx=25,
            pady=8,
            cursor='hand2'
        )

        def on_enter(e):
            submit_btn['bg'] = '#40a9ff'
        def on_leave(e):
            submit_btn['bg'] = '#1890ff'

        submit_btn.bind("<Enter>", on_enter)
        submit_btn.bind("<Leave>", on_leave)
        submit_btn.pack()

        # 进度显示区域
        self.progress_frame = ttk.Frame(main_frame, style='TFrame')
        self.progress_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建水平框架来容纳文本框和滚动条
        text_container = ttk.Frame(self.progress_frame)
        text_container.pack(fill=tk.BOTH, expand=True)

        self.progress_text = tk.Text(
            text_container,
            height=12,
            font=('Microsoft YaHei UI', 10),
            wrap=tk.WORD,
            background='#ffffff',
            foreground='#333333'
        )
        self.progress_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 配置标签样式
        self.progress_text.tag_configure('success', foreground='#52c41a')
        self.progress_text.tag_configure('error', foreground='#ff4d4f')
        self.progress_text.tag_configure('info', foreground='#1890ff')

        # 添加滚动条
        scrollbar = ttk.Scrollbar(text_container, orient="vertical", command=self.progress_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.progress_text.configure(yscrollcommand=scrollbar.set)

        # 初始时禁用文本框编辑
        self.progress_text.configure(state='disabled')

        # 版权信息 - 重新调整位置
        copyright_frame = ttk.Frame(self.root, style='TFrame')  # 改为self.root而不是main_frame
        copyright_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(0, 10))

        copyright_label = ttk.Label(
            copyright_frame,
            text="© 2025 OA办结神器 by 李浩贤",
            font=('Microsoft YaHei UI', 9),
            foreground='#999999',
            background='#f0f2f5',
            anchor='center'
        )
        copyright_label.pack(fill=tk.X)

        self.loop = None
        self.running_task = None

    def update_progress(self, message, message_type='normal'):
        """更新进度信息，支持不同类型的消息样式"""
        self.progress_text.configure(state='normal')
        if '✅' in message:
            self.progress_text.insert(tk.END, message + '\n', 'success')
        elif '❌' in message:
            self.progress_text.insert(tk.END, message + '\n', 'error')
        else:
            self.progress_text.insert(tk.END, message + '\n', 'info')
        self.progress_text.see(tk.END)
        self.progress_text.configure(state='disabled')
        self.root.update()

    def submit(self):
        global LOGIN_DATA
        selected_org_name = self.org_var.get()
        config = load_config()

        # 获取日期并确保格式正确
        date_str = self.date_entry.get_date().strftime('%Y-%m-%d')

        LOGIN_DATA = {
            'password': self.password_entry.get(),
            'org_name': selected_org_name,
            'org_code': config['orgs'][selected_org_name],  # 获取机构代码
            'target_date': date_str,
            'thread_count': int(self.thread_var.get()),
            'idea': self.idea_entry.get()
        }

        # 保存配置时同时保存机构名称和代码
        save_config({
            'password': LOGIN_DATA['password'],
            'org_name': LOGIN_DATA['org_name'],
            'org_code': LOGIN_DATA['org_code'],  # 保存机构代码
            'target_date': LOGIN_DATA['target_date'],  # 保存字符串格式的日期
            'thread_count': LOGIN_DATA['thread_count'],
            'idea': LOGIN_DATA['idea']
        })

        self.update_progress("\n=== 开始处理 ===")

        # 创建新线程运行异步任务
        threading.Thread(target=self.run_async_task, daemon=True).start()

    def run_async_task(self):
        """在新线程中运行异步任务"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.running_task = self.loop.create_task(self.process_tasks())
        self.loop.run_until_complete(self.running_task)

    async def process_tasks(self):
        """处理所有任务的异步函数"""
        try:
            async with async_playwright() as p:
                print("\n=== 检查Chrome浏览器 ===")
                # 检查本地Chrome
                chrome_path = find_chrome_executable()
                if chrome_path:
                    print(f"✓ 使用找到的Chrome浏览器: {chrome_path}")
                    # 使用本地Chrome
                    browser = await p.chromium.launch(
                        executable_path=chrome_path,
                        headless=False
                    )
                else:
                    current_dir_chrome = os.path.join(os.getcwd(), "chrome", "chrome.exe")
                    # 使用Playwright内置浏览器
                    browser = await p.chromium.launch(executable_path=current_dir_chrome, headless=False)
                #browser = await p.chromium.launch(headless=False)
                context = await browser.new_context()

                self.update_progress("正在登录...")
                page, login_success = await get_cookies_after_login(context)

                if not login_success:
                    self.update_progress("❌ 登录失败：密码错误，请重新运行程序输入密码！")
                    return
                else:
                    self.update_progress("✅ 登录成功")
                self.update_progress("\n开始获取代办列表...")
                all_html = await get_all_daiban_list_html(page)

                daiban_list = parse_all_daiban(all_html, LOGIN_DATA['target_date'])

                if not daiban_list:
                    self.update_progress("\n没有需要处理的代办事项")
                else:
                    for idx, daiban in enumerate(daiban_list, 1):
                        self.update_progress(f"\n✅ 正在提交第 {idx}/{len(daiban_list)} 条：{daiban['标题']}")
                        result, success = await fetch_and_submit(page, daiban)
                        daiban['提交结果'] = "成功" if success else "失败"
                        status = '✅ 成功' if success else '❌ 失败'
                        self.update_progress(f"提交结果: {status}")
                        daiban_records.append(daiban)

                    if daiban_records:
                        export_to_excel(daiban_records)
                        self.update_progress("\n✅ Excel文件已导出")

                await browser.close()
                self.update_progress("\n=== 处理完成 ===")

        except Exception as e:
            self.update_progress(f"\n❌ 处理过程出错: {str(e)}")
            import traceback
            self.update_progress(f"\n{traceback.format_exc()}")

# ================== 获取 cookies ==================
async def get_cookies_after_login(context):
    print("\n=== 开始登录流程 ===")
    page = await context.new_page()
    await page.goto(BASE_URL + "/jsp/employeeHome.jsp#")
    print("1. 已访问登录页面")

    await page.wait_for_selector('#userPassword')
    print("2. 已找到密码输入框")

    await page.fill('#userPassword', LOGIN_DATA['password'])
    await page.click('.login_btn')
    print("3. 已输入密码并点击登录按钮")

    try:
        print("4. 等待登录结果...")
        # 等待一小段时间让页面加载
        await page.wait_for_timeout(2000)

        # 首先检查是否有机构选择框
        has_org_selector = await page.locator('#boxdept').count() > 0

        if has_org_selector:
            # 需要选择机构的情况
            print("5. 检测到需要选择机构")

            if LOGIN_DATA['org_code']:  # 如果有预设的机构代码
                # 检查是否存在指定的机构
                org_selector = f'div[data-value="{LOGIN_DATA["org_code"]}"]'
                try:
                    await page.wait_for_selector(org_selector, timeout=5000)
                    print("6. 已找到目标机构选项")

                    # 点击选择机构
                    await page.click(org_selector)
                    print("7. 已点击选择机构")
                except:
                    print("预设机构未找到，等待手动选择")

            else:  # 手动选择模式
                print("等待手动选择机构...")
                # 等待用户点击某个机构（等待点击事件）
                await page.wait_for_selector('div[data-value]:not([data-value=""]).active', timeout=60000)

                # 获取选中的机构信息
                selected_org = await page.evaluate("""
                    () => {
                        const activeDiv = document.querySelector('div[data-value]:not([data-value=""]).active');
                        if (activeDiv) {
                            const fullName = activeDiv.getAttribute('title');
                            const orgCode = activeDiv.getAttribute('data-value');
                            const shortName = fullName.split('/').pop().trim();
                            return { code: orgCode, name: shortName, fullName: fullName };
                        }
                        return null;
                    }
                """)

                if selected_org:
                    print(f"检测到手动选择的机构：{selected_org['name']} ({selected_org['code']})")
                    # 更新配置
                    save_config({
                        'new_org_name': selected_org['name'],
                        'new_org_code': selected_org['code'],
                        'password': LOGIN_DATA['password'],
                        'org_name': selected_org['name'],
                        'org_code': selected_org['code'],
                        'target_date': LOGIN_DATA['target_date'],
                        'thread_count': LOGIN_DATA['thread_count'],
                        'idea': LOGIN_DATA['idea']
                    })
                    print("新机构已添加到配置文件")

            # 等待确认按钮可点击
            await page.wait_for_selector('#boxdept .login_btn', state='visible')
            await page.wait_for_timeout(500)
            print("8. 已找到确认按钮")

            # 点击确认按钮
            await page.locator('#boxdept .login_btn').click()
            print("9. 已点击确认按钮")

            # 等待登录完成
            await page.wait_for_timeout(3000)
            print("10. 等待3秒完成")
        else:
            # 直接登录成功的情况
            print("5. 检测到直接登录模式")
            await page.wait_for_timeout(2000)
            print("6. 等待页面加载完成")

        # 验证登录状态
        content = await page.content()
        if "首页" in content:
            print("✓ 登录成功：已验证页面内容")
            return page, True
        else:
            print("✗ 登录失败：未找到首页标识")
            return page, False

    except Exception as e:
        print(f"登录过程出错: {str(e)}")
        # 最后检查一次是否实际已登录
        try:
            content = await page.content()
            if "首页" in content:
                print("✓ 检测到已成功登录")
                return page, True
            if "密码错误" in content or "请重新登录" in content:
                print("✗ 登录失败：密码错误或需要重新登录")
            else:
                print("✗ 登录失败：未知原因")
        except:
            pass
        print("=== 登录流程异常结束 ===\n")
        return page, False

# 在主登录函数中添加重试逻辑
async def simulate_login(username, password, max_retries=3):
    LOGIN_DATA['password'] = password

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()

        for attempt in range(max_retries):
            print(f"\n=== 开始第 {attempt + 1} 次登录尝试 ===")
            page, login_success = await get_cookies_after_login(context)

            if login_success:
                print("✓ 登录成功！")
                print("=== 登录流程完成 ===\n")
                return
            else:
                print(f"✗ 登录尝试 {attempt + 1} 失败，{'正在重试...' if attempt < max_retries - 1 else '已达到最大重试次数'}")
                if attempt < max_retries - 1:
                    await page.wait_for_timeout(2000)  # 等待2秒后重试
                    continue

        messagebox.showerror("登录失败", "密码错误或登录异常，请检查后重试！")

# ================== 获取所有代办列表 ==================
async def get_daiban_list_html(page, page_no):
    api_url = f"{API_URL}?pageNo={page_no}"
    return await page.evaluate("""
        async (url) => {
            const response = await fetch(url, { method: 'GET', credentials: 'include' });
            return await response.text();
        }
    """, api_url)

async def get_page_count(page):
    first_page_html = await get_daiban_list_html(page, 1)
    soup = BeautifulSoup(first_page_html, "html.parser")
    return int(soup.select_one(".message i:nth-child(2)").text.split('/')[1].split('页')[0])

async def get_page_data(page, page_no):
    print(f"📄 正在获取第 {page_no} 页")
    return await get_daiban_list_html(page, page_no)

async def get_all_daiban_list_html(page):
    total_pages = await get_page_count(page)

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=LOGIN_DATA['thread_count']) as executor:
        # 创建所有任务
        tasks = []
        for page_no in range(1, total_pages + 1):
            task = asyncio.create_task(get_page_data(page, page_no))
            tasks.append(task)

        # 等待所有任务完成
        all_pages_html = await asyncio.gather(*tasks)

    return all_pages_html

# ================== 解析代办 ==================
def parse_all_daiban(html_contents, target_date):
    # 如果传入的是datetime对象，转换为字符串
    if isinstance(target_date, datetime):
        target_date = target_date.strftime("%Y-%m-%d")

    # 确保日期格式正确（将可能的 / 替换为 -）
    target_date = target_date.replace('/', '-')

    try:
        target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError as e:
        print(f"日期格式错误: {target_date}")
        raise

    daiban_list = []

    for html_content in html_contents:
        soup = BeautifulSoup(html_content, "html.parser")
        rows = soup.select("table#listtable tr")
        for row in rows:
            if row.get("class") and "tablelist-title" in row.get("class"):
                continue
            url = row.get("url")
            tds = row.find_all("td")
            if len(tds) < 6 or not url:
                continue
            title, date_str = tds[2].get_text(strip=True), tds[5].get_text(strip=True)
            try:
                date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                if date_obj <= target_date_obj:
                    daiban_list.append({"标题": title, "日期": date_str, "链接": BASE_URL + url})
            except ValueError:
                continue
    return daiban_list

# ================== 批量办结流程 ==================
async def fetch_and_submit_archives(page, daiban):
    detail_html = await page.evaluate("""async (url) => {
        const response = await fetch(url, { method: 'GET', credentials: 'include' });
        return await response.text();
    }""", daiban['链接'])

    soup = BeautifulSoup(detail_html, "html.parser")
    form = soup.find("form", {"id": "frm"})
    if not form:
        return "表单未找到"

    # 收集所有hidden input的值
    post_data = {}
    for input_tag in form.find_all("input", type="hidden"):
        name = input_tag.get("name")
        value = input_tag.get("value", "")
        if name:
            post_data[name] = value

    # 提取特定字段的值
    special_fields = ['needGradeName', 'secretGradeName', 'saveLimitIdName']
    for field in special_fields:
        input_tag = form.find("input", {"name": field})
        if input_tag:
            post_data[field] = input_tag.get("value", "")

    # 添加必要的其他字段
    additional_fields = {
        'idea': LOGIN_DATA['idea']  # 使用用户输入的办结意见
    }
    post_data.update(additional_fields)

    # 提交办结
    result = await page.evaluate("""([url, data]) => {
        const formBody = new URLSearchParams(data);
        return fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: formBody,
            credentials: 'include'
        }).then(r => r.text());
    }""", [ARCHIVES_SUBMIT_URL, post_data])

    return result

# 修改原有的fetch_and_submit函数名称
async def fetch_and_submit_worknotice(page, daiban):
    # 原有的fetch_and_submit函数代码保持不变
    detail_html = await page.evaluate("""async (url) => {
        const response = await fetch(url, { method: 'GET', credentials: 'include' });
        return await response.text();
    }""", daiban['链接'])

    soup = BeautifulSoup(detail_html, "html.parser")
    post_data = {tag.get("name"): tag.get("value", "") for tag in soup.find_all("input", type="hidden") if tag.get("name")}
    textarea = soup.find("textarea", {"id": "text"})
    if textarea:
        post_data["text"] = textarea.text.strip()

    result = await page.evaluate("""([url, data]) => {
        const formBody = new URLSearchParams(data);
        return fetch(url, { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: formBody, credentials: 'include' }).then(r => r.text());
    }""", [SUBMIT_URL, post_data])
    return result

# 根据URL类型选择不同的提交方法
async def fetch_and_submit(page, daiban):
    url = daiban['链接']
    if 'worknoticeInfoController' in url:
        result = await fetch_and_submit_worknotice(page, daiban)
        success = "001" in result
    elif 'archives/swAction' in url:
        result = await fetch_and_submit_archives(page, daiban)
        success = "流程结束" in result
    else:
        result = "未知的URL类型"
        success = False

    return result, success

# ================== 导出 Excel ==================
def export_to_excel(records):
    df = pd.DataFrame(records)
    filename = f"代办记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    df.to_excel(filename, index=False)
    print(f"📄 已导出：{filename}")

# 新增打印URL的函数
def print_daiban_urls(html_contents):
    print("\n" + "="*50)
    print("代办列表URL:")
    print("="*50)

    for html_content in html_contents:
        soup = BeautifulSoup(html_content, "html.parser")
        rows = soup.select("table#listtable tr")
        for row in rows:
            if row.get("class") and "tablelist-title" in row.get("class"):
                continue
            url = row.get("url")
            tds = row.find_all("td")
            if len(tds) < 6 or not url:
                continue
            title = tds[2].get_text(strip=True)
            date_str = tds[5].get_text(strip=True)
            full_url = BASE_URL + url

            print("\n" + "-"*30)
            print(f"标题: {title}")
            print(f"日期: {date_str}")
            print(f"URL: {full_url}")

# ================== 主流程 ==================
def find_chrome_executable() -> Optional[str]:
    """
    查找本地安装的Chrome浏览器可执行文件
    返回: 找到则返回路径，未找到返回None
    """
    system = platform.system()
    print(f"当前操作系统: {system}")

    if system == "Windows":
        # Windows下的常见Chrome安装路径
        locations = [
            os.path.expandvars(r"%ProgramFiles%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe"),
            os.path.expandvars(r"%LocalAppData%\Google\Chrome\Application\chrome.exe"),
        ]
        print("Windows系统，检查以下Chrome安装路径:")
        for loc in locations:
            print(f"- {loc}")
    else:
        print(f"不支持的操作系统: {system}")
        locations = []

    # 检查所有可能的位置
    for path in locations:
        if os.path.exists(path) and os.path.isfile(path):
            print(f"找到本地Chrome浏览器: {path}")
            return path

    print("未找到本地Chrome浏览器，将使用Playwright内置浏览器")
    return None

async def main():
    saved_config = load_config()
    login_dialog = LoginDialog(saved_config)
    login_dialog.root.mainloop()

    async with async_playwright() as p:
        print("\n=== 检查Chrome浏览器 ===")
        # 检查本地Chrome
        chrome_path = find_chrome_executable()
        if chrome_path:
            print(f"✓ 使用找到的Chrome浏览器: {chrome_path}")
            # 使用本地Chrome
            browser = await p.chromium.launch(
                executable_path=chrome_path,
                headless=False
            )
        else:
            current_dir_chrome = os.path.join(os.getcwd(), "chrome", "chrome.exe")
            # 使用Playwright内置浏览器
            browser = await p.chromium.launch( executable_path=current_dir_chrome,headless=False)

        context = await browser.new_context()
        page, login_success = await get_cookies_after_login(context)

        if not login_success:
            messagebox.showerror("登录失败", "密码错误，请重新运行程序输入密码！")
            return

        save_config({
            'password': LOGIN_DATA['password'],
            'org_name': LOGIN_DATA['org_name'],
            'target_date': LOGIN_DATA['target_date']
        })

        # 获取代办列表
        print("\n开始获取代办列表...")
        all_html = await get_all_daiban_list_html(page)

        # 打印URL
        #print_daiban_urls(all_html)

        # 注释掉提交相关代码
        #"""
        daiban_list = parse_all_daiban(all_html, LOGIN_DATA['target_date'])

        if not daiban_list:
            print("\n没有需要处理的代办事项")
        else:
            for idx, daiban in enumerate(daiban_list, 1):
                print(f"\n✅ 正在提交第 {idx}/{len(daiban_list)} 条：{daiban['标题']}\n{daiban['链接']}")
                result, success = await fetch_and_submit(page, daiban)
                daiban['提交结果'] = "成功" if success else "失败"
                print(f"提交结果: {'✅ 成功' if success else '❌ 失败'}")
                daiban_records.append(daiban)

            # 只在有提交记录时导出Excel
            if daiban_records:
                export_to_excel(daiban_records)

        await browser.close()

# ================== 启动 ==================
if __name__ == "__main__":
    # 加载配置
    config = load_config()

    # 创建并显示登录对话框
    dialog = LoginDialog(config)
    #asyncio.run(main())
    dialog.root.mainloop()
