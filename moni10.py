import asyncio

import os
import re
import socket
import sys
import tkinter as tk

from functools import reduce
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *

from os import path
import requests,json, time

from datetime import datetime
from playwright.async_api import async_playwright

import pandas as pd

import threading
from tool import Tool

executable_path = "firefox/firefox.exe"    # 指定可执行文件路径，用相对路径


def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1
def normalize_data(data):
    # 找到最长字段的长度
    max_length = max(len(value) for value in data.values()) if data else 0
    
    # 对于每个字段，检查其长度，如果小于最大长度，则用合适的值填充
    for key, value in data.items():
        if len(value) < max_length:
            # 如果列表为空，使用空字符串填充，否则使用第一个元素填充
            fill_value = "" if not value else value[0]
            value.extend([fill_value] * (max_length - len(value)))
            
    return data

# def merge_dicts(dict1, dict2):
#     # 获取所有的键
#     all_keys = set(dict1.keys()).union(set(dict2.keys()))
#
#     for key in all_keys:
#         # 如果 dict1 中没有该字段，就用空列表填充
#         if key not in dict1:
#             dict1[key] = []
#         # 如果 dict2 中没有该字段，就用空列表填充
#         if key not in dict2:
#             dict2[key] = []
#
#         len1 = len(dict1[key])
#         len2 = len(dict2[key])
#
#         # 如果长度不一致，填充较短的字段
#         if len1 < len2:
#             dict1[key].extend([dict2[key][0]] * (len2 - len1))
#         elif len2 < len1:
#             dict2[key].extend([dict1[key][0]] * (len1 - len2))
#
#         # 合并两个字典的字段
#         dict1[key].extend(dict2[key])
#
#     return dict1
# 需要匹配的 URL 固定部分（正则或字符串）
TARGET_URL_PARTS = [
    'queryCurrentPacketByContainNos',  # 只匹配包含这部分的 URL
    'queryPacketsByContainNo',
    'queryNumberOfInternals'
]
# 请求信息类
class RequestInfo:
    def __init__(self, url, response_data=None):
        self.url = url
        self.response_data = response_data

# 用于批量处理邮件号查询并导出数据
async def batch_query_and_export(title,parent):
    global L
    # 程序已经在运行中，禁用按钮
    submit_button.configure(state="disabled")
    back_button.configure(state="disabled")
    start = time.perf_counter()
    # 获取本机主机名
    hostname = socket.gethostname()
    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}

        tool.process_input("代理功能已启用")
    L = 0

    # 保存账号和密码
    tool.save_data()
    username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
    password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数

    try:
        # 使用 Playwright 启动浏览器
        async with async_playwright() as p:
            browser = await p.firefox.launch(
                headless=True,
                executable_path=executable_path,
                args=["--no-sandbox", "--disable-setuid-sandbox", "--disable-dev-shm-usage", 
                      "--disable-gpu", "--disable-extensions"],
                # 添加以下配置加速页面加载
                ignore_default_args=["--enable-automation"]
            )

            # 创建浏览器上下文
            context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
            page = await context.new_page()
            # 获取网络请求
            # page.on("request", lambda request: print( ">> ", request.method, request.url))
            # # 获取网络响应
            # page.on("response", lambda response: print( "<< ", response.status, response.text))

            # 设置 User-Agent模拟正常浏览器
            await context.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            })

            # 禁用Playwright的自动化标识
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)
            # 获取文本框中的文本
            text = input_textbox.get("1.0", "end-1c")
            # 将文本按行分割并去除空行
            lines = text.splitlines()
            lines = [line for line in lines if line.strip()]
            mailnos = lines
            #print(mailnos)
            # 登录
            userName,orgName,orgcode=await tool.newlogin(page, username, password)
            tool.postlog(username, userName, title, ip_address)
            await page.close()  # 登录完成后关闭页面

            all_logistics_data = []

            # 限制并发的最大任务数
            semaphore = asyncio.Semaphore(int(threads_combobox.get()))  # 设置并发上限为 5（可调整）

            # 初始化页面池
            pool = PagePool(context, size=min(10, int(threads_combobox.get())))
            await pool.initialize()

            async def query_with_semaphore(mailno, all_logistics_data):
                async with semaphore:
                    # 每个任务创建一个新的页面
                    page = await context.new_page()
                    try:
                        logistics_data = await query_mailno(page, mailno, parent, all_logistics_data)
                    finally:
                        await page.close()  # 确保页面关闭以释放资源
                    return logistics_data

            async def querybagno_with_semaphore(mailno,all_logistics_data):
                async with semaphore:
                    # 每个任务创建一个新的页面
                    page = await context.new_page()
                    try:
                        # 传递response_manager给query_bagno函数
                        logistics_data = await query_bagno(page, mailno, parent, all_logistics_data)
                    finally:
                        await page.close()  # 确保页面关闭以释放资源
                    return logistics_data
            if '容器' in selected3.get():
                # 创建任务列表
                tasks = [querybagno_with_semaphore(mailno, all_logistics_data) for mailno in mailnos]
                # 并发执行任务
                results = await asyncio.gather(*tasks)
                mailnos=all_logistics_data
                #print(all_logistics_data)
                all_logistics_data = []
                #print(mailnos)
                # 创建任务列表
                tasks = [query_with_semaphore(mailno,all_logistics_data) for mailno in mailnos]

                # 并发执行任务
                results = await asyncio.gather(*tasks)
            else:
                # 创建任务列表
                tasks = [query_with_semaphore(mailno, all_logistics_data) for mailno in mailnos]
                # 并发执行任务
                results = await asyncio.gather(*tasks)
            # 收集所有结果
            # for logistics_data in results:
            #     if logistics_data:
            #         all_logistics_data.extend(logistics_data)
            if '全程' in selected.get():
                merged_data = {
                    "总包号": [],
                    '寄达局': [],
                    '寄达局代码': [],
                    "时间": [],
                    "处理机构": [],
                    "处理机构代码": [],
                    "处理动作": [],
                    "详细说明": [],
                    "操作员": [],
                    "操作员代码": [],
                    "来源": [],
                    "备注": []
                }
            elif '最后' in selected.get():
                merged_data = {
                    "总包号码": [],
                    "总包条码": [],
                    "原寄局": [],
                    "原寄局代码": [],
                    "总包种类": [],
                    "封发时间": [],
                    '寄达局': [],
                    '寄达局代码': [],
                    "重量": [],
                    "件数": [],
                    "时间": [],
                    "处理机构": [],
                    "处理机构代码": [],
                    "处理动作": [],
                    "详细说明": [],
                    "操作员": [],
                    "操作员代码": [],
                    "来源": [],
                    "备注": []
                }
            else:
                merged_data = {
                    "总包号": [],
                    '寄达局': [],
                    '寄达局代码': [],
                    "时间": [],
                    "处理机构": [],
                    "处理机构代码": [],
                    "处理动作": [],
                    "详细说明": [],
                    "操作员": [],
                    "操作员代码": [],
                    "来源": [],
                    "备注": []
                }
                # results = pool.map(getallmailtrace, datalist)
                # 提取键
                extracted_keys = extract_keys_by_delimiters(input3_textbox.get())
                # 添加新字段
                for key in extracted_keys:
                    if key not in merged_data:
                        merged_data[key] = []

            merged_data = reduce(merge_dicts, all_logistics_data, merged_data)


            # 导出到Excel
            if merged_data:

                df = pd.DataFrame(merged_data)
                # # 生成Excel文件的保存路径，当前目录 + 当前时间
                currentTime = datetime.now().strftime('%Y%m%d_%H%M%S')
                # export_path = os.path.join(os.getcwd(), f'邮件全轨迹_{current_time}.xlsx')
                row = 1048570
                length = len(df)
                number = length // row
                for i in range(number + 1):
                    export_path=os.path.join(os.getcwd(), f'总包{selected.get()}-{currentTime}{i}-bylhx.xlsx')
                    df[i * row:(i + 1) * row].to_excel(export_path,index=False)
                # 保存文件
                #df.to_excel(export_path, index=False)
                    tool.process_input(f"\n数据已成功导出！\n文件路径：{export_path}")
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                del all_logistics_data
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
                #messagebox.showinfo("成功", f"数据已成功导出！文件路径：{export_path}")
            else:
                #messagebox.showwarning("警告", "没有获取到有效数据！")
                tool.process_input("没有获取到有效数据")
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
    except Exception as e:
        tool.process_input(f"查询失败，错误信息：{e}")
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    finally:
        # 确保按钮始终会被重新启用
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        # 确保浏览器总是会被关闭
        try:
            if 'browser' in locals() and browser:
                await browser.close()
        except Exception as e:
            print(f"关闭浏览器时出错：{e}")



# 提取当前总包号路向数据
async def extract_current_packet_data(response_text, dataOutput):
    # 在这里实现提取 queryCurrentPacketByContainNos 数据的逻辑

    try:
        jsonObj = json.loads(response_text)
    except json.JSONDecodeError:
        # 处理错误情况，例如设置默认值或记录日志
        jsonObj = None



    if jsonObj:
        nextOrgName=jsonObj[0]['nextOrgName']
        nextOrgCode= jsonObj[0]['nextOrgCode']
        containerNo=jsonObj[0]['containerNo']
        lastOrgName=jsonObj[0]['lastOrgName']
        lastOrgCode=jsonObj[0]['lastOrgCode']
        deliveryUserName=jsonObj[0]['deliveryUserName']
        if 'weight' in jsonObj[0]:
            weight=jsonObj[0]['weight']
        else:
            weight=''
        #dispatchDate=jsonObj[0]['dispatchDate']
        if 'dispatchDate' in jsonObj[0]:
            # Convert the Unix timestamp to a datetime object
            datetime_object = datetime.fromtimestamp(int(jsonObj[0]['dispatchDate']) / 1000)

            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
            dispatchDate = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
        else:
            dispatchDate=''
    else:
        nextOrgName=''
        nextOrgCode=''
        containerNo=''
        lastOrgName=''
        lastOrgCode=''
        deliveryUserName=''
        weight=0
        dispatchDate=''
    if '最后' in selected.get():
        dataOutput["寄达局"].append(nextOrgName)
        dataOutput["寄达局代码"].append(nextOrgCode)
        dataOutput["总包号码"].append(containerNo)
        dataOutput["重量"].append(weight)
        dataOutput["总包种类"].append(deliveryUserName)
        dataOutput["封发时间"].append(dispatchDate)
        dataOutput["原寄局"].append(lastOrgName)
        dataOutput["原寄局代码"].append(lastOrgCode)
    else :

        dataOutput["寄达局"].append(nextOrgName)
        dataOutput["寄达局代码"].append(nextOrgCode)



#提取容器的总包号
async def extract_current_packet_data2(response_text, dataOutput):
    try:
        jsonObj = json.loads(response_text)
    except json.JSONDecodeError:
        # 处理错误情况，例如设置默认值或记录日志
        jsonObj = None
    if jsonObj:
        for line in jsonObj:
            dataOutput.append(line.get('traceNo', ''))
            print(line.get('traceNo', ''))

#提取总包号轨迹数据
async def extract_packets_by_contain_no_data(response_text, dataOutput):
    # 在这里实现提取 queryPacketsByContainNo 数据的逻辑
    if '全程' in selected.get():

        try:
            jsonObj = json.loads(response_text)
            if '否' in selected2.get():
                for line in jsonObj:
                    # 通过循环来获取JSON中的数据，并添加到字典中

                    op_time_value = line.get('opTime', '')

                    if op_time_value:
                        # Convert the timestamp to a datetime object
                        datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                        # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                        formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        # Append the formatted datetime to the '时间' key in dataOutput
                        dataOutput["时间"].append(formatted_datetime)
                    else:
                        # Handle the case when 'opTime' is empty
                        dataOutput["时间"].append("")


                    dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('desc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
            else:
                for line in jsonObj:
                    if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]:
                        # 通过循环来获取JSON中的数据，并添加到字典中

                        # dataOutput["时间"].append(line.get('opTime', ''))
                        op_time_value = line.get('opTime', '')

                        if op_time_value:
                            # Convert the timestamp to a datetime object
                            datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                            formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                            # Append the formatted datetime to the '时间' key in dataOutput
                            dataOutput["时间"].append(formatted_datetime)
                        else:
                            # Handle the case when 'opTime' is empty
                            dataOutput["时间"].append("")

                        dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                        dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                        dataOutput["处理动作"].append(line.get('opName', ''))
                        dataOutput["详细说明"].append(line.get('desc', ''))
                        dataOutput["操作员"].append(line.get('operatorName', ''))
                        dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                        dataOutput["来源"].append(line.get('source', ''))
                        dataOutput["备注"].append(line.get('notes', ''))
        except json.JSONDecodeError:

                dataOutput["时间"].append('')
                dataOutput["处理机构"].append('')
                dataOutput["处理机构代码"].append('')
                dataOutput["处理动作"].append('')
                dataOutput["详细说明"].append('')
                dataOutput["操作员"].append('')
                dataOutput["操作员代码"].append('')
                dataOutput["来源"].append('')
                dataOutput["备注"].append('')
    elif '最后' in selected.get():
        try:
            jsonObj = json.loads(response_text)
            if '否' in selected2.get():
                line = jsonObj[-1]

                # dataOutput["时间"].append(line.get('opTime', ''))
                op_time_value = line.get('opTime', '')
                # print(op_time_value)
                if op_time_value:
                    # Convert the timestamp to a datetime object
                    datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)
                    # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                    formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
                    # Append the formatted datetime to the '时间' key in dataOutput
                    dataOutput["时间"].append(formatted_datetime)
                else:
                    # Handle the case when 'opTime' is empty
                    dataOutput["时间"].append("")

                dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                dataOutput["处理动作"].append(line.get('opName', ''))
                dataOutput["详细说明"].append(line.get('desc', ''))
                dataOutput["操作员"].append(line.get('operatorName', ''))
                dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                dataOutput["来源"].append(line.get('source', ''))
                dataOutput["备注"].append(line.get('notes', ''))
            else:
                sj = ''
                cljg = ''
                cljgdm = ''
                cldz = ''
                xxsm = ''
                czy = ''
                czydm = ''
                ly = ''
                bz = ''
                for line in jsonObj:

                    if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line[
                        "opOrgCode"]:
                        # 通过循环来获取JSON中的数据，并添加到字典中
                        op_time_value = line.get('opTime', '')
                        if op_time_value:
                            # Convert the timestamp to a datetime object
                            datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                            sj = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        # sj = line["opTime"]
                        if "opOrgSimplename" in line:
                            cljg = line["opOrgSimplename"]

                        if "opOrgCode" in line:
                            cljgdm = line["opOrgCode"]  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间

                        if "opName" in line:
                            cldz = line["opName"]

                        if "desc" in line:
                            xxsm = line["desc"]

                        if "operatorName" in line:
                            czy = line["operatorName"]

                        if "operatorNo" in line:
                            czydm = line["operatorNo"]

                        ly = line["source"]
                        if "notes" in line:
                            bz = line["notes"]


                dataOutput["时间"].append(sj)
                dataOutput["处理机构"].append(cljg)
                dataOutput["处理机构代码"].append(cljgdm)
                dataOutput["处理动作"].append(cldz)
                dataOutput["详细说明"].append(xxsm)
                dataOutput["操作员"].append(czy)
                dataOutput["操作员代码"].append(czydm)
                dataOutput["来源"].append(ly)
                dataOutput["备注"].append(bz)
        except json.JSONDecodeError:

            dataOutput["总包号码"].append('')

            dataOutput["原寄局"].append('')
            dataOutput["原寄局代码"].append('')
            dataOutput["总包种类"].append('')
            dataOutput["封发时间"].append('')

            dataOutput["重量"].append('')
            dataOutput["件数"].append('')
            dataOutput["时间"].append('')
            dataOutput["处理机构"].append('')
            dataOutput["处理机构代码"].append('')
            dataOutput["处理动作"].append('')
            dataOutput["详细说明"].append('')
            dataOutput["操作员"].append('')
            dataOutput["操作员代码"].append('')
            dataOutput["来源"].append('')
            dataOutput["备注"].append('')
    else:
        try:
            jsonObj = json.loads(response_text)
            # 提取键
            extracted_keys = extract_keys_by_delimiters(input3_textbox.get())

            # 添加新字段
            for key in extracted_keys:
                if key not in dataOutput:
                    dataOutput[key] = []
            # 获取input2_textbox的值
            input2_value = input2_textbox.get()

            # 用#分割每个值
            values_list = input2_value.split('#')
            if '否' in selected2.get():
                for line in jsonObj:
                    # 通过循环来获取JSON中的数据，并添加到字典中
                    if line['opName'] in values_list:

                        # dataOutput["时间"].append(line.get('opTime', ''))
                        op_time_value = line.get('opTime', '')

                        if op_time_value:
                            # Convert the timestamp to a datetime object
                            datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                            formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                            # Append the formatted datetime to the '时间' key in dataOutput
                            dataOutput["时间"].append(formatted_datetime)
                        else:
                            # Handle the case when 'opTime' is empty
                            dataOutput["时间"].append("")


                        dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                        dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                        dataOutput["处理动作"].append(line.get('opName', ''))
                        dataOutput["详细说明"].append(line.get('desc', ''))
                        dataOutput["操作员"].append(line.get('operatorName', ''))
                        dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                        dataOutput["来源"].append(line.get('source', ''))
                        dataOutput["备注"].append(line.get('notes', ''))
                        groups = input3_textbox.get().split('#')
                        for group in groups:
                            if '@' in group:
                                start_char, end_char = group.split('@')
                                start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                                # print("开始字符"+start_char,"结束字符"+ end_char)
                                if end_char:  # 有结束字符
                                    pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                                else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                    pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                                matches = re.findall(pattern, line.get('internalDesc', ''))
                                if matches:
                                    dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                                else:
                                    dataOutput[start_char.rstrip(':')].append('')
            else:
                for line in jsonObj:
                    if line['opName'] in values_list and (
                            '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line[
                        "opOrgCode"]):
                        # 通过循环来获取JSON中的数据，并添加到字典中

                        # dataOutput["时间"].append(line.get('opTime', ''))
                        op_time_value = line.get('opTime', '')

                        if op_time_value:
                            # Convert the timestamp to a datetime object
                            datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                            formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                            # Append the formatted datetime to the '时间' key in dataOutput
                            dataOutput["时间"].append(formatted_datetime)
                        else:
                            # Handle the case when 'opTime' is empty
                            dataOutput["时间"].append("")

                        dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                        dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                        dataOutput["处理动作"].append(line.get('opName', ''))
                        dataOutput["详细说明"].append(line.get('desc', ''))
                        dataOutput["操作员"].append(line.get('operatorName', ''))
                        dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                        dataOutput["来源"].append(line.get('source', ''))
                        dataOutput["备注"].append(line.get('notes', ''))
                        groups = input3_textbox.get().split('#')
                        for group in groups:
                            if '@' in group:
                                start_char, end_char = group.split('@')
                                start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                                # print("开始字符"+start_char,"结束字符"+ end_char)
                                if end_char:  # 有结束字符
                                    pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                                else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                    pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                                matches = re.findall(pattern, line.get('internalDesc', ''))
                                if matches:
                                    dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                                else:
                                    dataOutput[start_char.rstrip(':')].append('')
        except json.JSONDecodeError:

            dataOutput["时间"].append("")
            dataOutput["处理机构"].append("")
            dataOutput["处理机构代码"].append("")
            dataOutput["处理动作"].append("")
            dataOutput["详细说明"].append("")
            dataOutput["操作员"].append("")
            dataOutput["操作员代码"].append("")
            dataOutput["来源"].append("")
            dataOutput["备注"].append("")

#提取总包号件数
async def extract_number_of_internals_data(response_text, dataOutput):
    # 在这里实现提取 queryNumberOfInternals 数据的逻辑
    if '最后' in selected.get():

        dataOutput["件数"].append(response_text if response_text else "")



# 按容器查询总包号
async def query_bagno(page, mailno, parent, logistics_data, max_retries=5, retry_delay=0.5):
    # 获取网络响应
    async def handle_response(response):
        if any(target_part in response.url for target_part in TARGET_URL_PARTS) and not response_manager.is_processed(response.url):
            response_manager.mark_processed(response.url)
            print(f"<< Status: {response.status} URL: {response.url}")
            try:
                # 获取响应文本
                r = await response.text()

                # print(r)

                if 'queryCurrentPacketByContainNos' in response.url:
                    await extract_current_packet_data2(r, logistics_data)

            except Exception as e:
                print(f"query_bagno Error reading response: {e}")

        else:
            # print(f"忽略的请求：{response.url}")
            pass
    # 监听指定的响应
    page.on("response", handle_response)

    # 设置最大重试次数和重试间隔
    # 任务逻辑
    await load_and_querybagno_page(page, mailno, parent, max_retries, retry_delay)



# 查询总包全程轨迹函数
async def query_mailno(page, mailno, parent, logistics_data, max_retries=5, retry_delay=1):
    global L
    if '最后' in selected.get():
        dataOutput = {
            "总包号码": [],
            "总包条码": [],
            "原寄局": [],
            "原寄局代码": [],
            "总包种类": [],
            "封发时间": [],
            '寄达局': [],
            '寄达局代码': [],
            "重量": [],
            "件数": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        # 将此邮件号查询结果添加到主物流数据列表中
        dataOutput["总包条码"].append(mailno)
    else:
        dataOutput = {
            "总包号": [],
            '寄达局': [],
            '寄达局代码': [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        # 将此邮件号查询结果添加到主物流数据列表中
        dataOutput["总包号"].append(mailno)

    # 获取网络响应
    async def handle_response(response):
        if any(target_part in response.url for target_part in TARGET_URL_PARTS) and not response_manager.is_processed(response.url):
            response_manager.mark_processed(response.url)
            print(f"<< Status: {response.status} URL: {response.url}")
            try:
                # 获取响应文本
                r = await response.text()



                if 'queryCurrentPacketByContainNos' in response.url:
                    #print(f'queryCurrentPacketByContainNos:{r}')
                    await extract_current_packet_data(r, dataOutput)
                elif 'queryPacketsByContainNo' in response.url:
                    #print(f'queryPacketsByContainNo:{r}')
                    await extract_packets_by_contain_no_data(r, dataOutput)
                elif 'queryNumberOfInternals' in response.url:
                    #print(f'queryNumberOfInternals:{r}')
                    await extract_number_of_internals_data(r, dataOutput)




                # 打印响应的前200个字符
                #print(f"Response Text: {r}...")  # 只打印部分响应，防止输出过多
            except Exception as e:
                print(f"query_mailno Error reading response: {e}")

        else:
            #print(f"忽略的请求：{response.url}")
            pass

    # 监听指定的响应
    page.on("response", handle_response)

    # 设置最大重试次数和重试间隔
    # 任务逻辑
    await load_and_query_page(page, mailno, parent, max_retries, retry_delay)
    try:
        data = normalize_data(dataOutput)
        logistics_data.append(data)
        
        L += 1
        parent.after(0, tool.update_L(str(L)))
    except Exception as e:
        print(f"处理邮件号 {mailno} 时出错: {e}")
        # 确保即使出错也添加一条记录，避免数据不一致
        if dataOutput and all(isinstance(v, list) for v in dataOutput.values()):
            try:
                logistics_data.append(dataOutput)
            except Exception:
                pass

# 封装页面加载、输入和点击的任务逻辑
async def load_and_query_page(page, mailno, parent,max_retries,retry_delay):
    # 任务逻辑
    for attempt in range(max_retries):
        try:
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpspacket/list', 
                           timeout=5000,  # 减少超时时间
                           wait_until='domcontentloaded')  # 不等待所有资源加载完成

            await page.wait_for_selector('#containNos', timeout=10000)
            # 输入邮件号
            await page.fill('#containNos', mailno)

            # 点击查询按钮
            await page.click('#btnSubmit_contain_no')

            #time.sleep(1)
            try:
                await page.wait_for_selector('.mailQuery_container', timeout=10000)
                return []  # 如果加载成功，返回一个空列表或者其他你希望的内容
            except Exception:
                # tool.process_input(f"查询邮件号 {mailno} 超时，返回空数据。")
                #parent.after(0, tool.process_input(f"邮件号 {mailno} 没有轨迹信息。"))
                # 封装查询的部分，支持重试
                return []

        except Exception as e:
            # tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}")
            # parent.after(0, tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}"))
            # 捕获页面加载时的异常，如果是超时或者其他错误
            print(f"{mailno}第 {attempt + 1} 次加载失败: {str(e)}")

            # 如果达到最大重试次数，则退出
            if attempt == max_retries - 1:
                parent.after(0, tool.process_input(f"\n页面加载失败，总包号: {mailno}"))
                return []

            # 否则等待重试
            print(f"等待 {retry_delay} 秒后重试...")
            await asyncio.sleep(retry_delay)

# 封装页面加载、输入和点击的任务逻辑
async def load_and_querybagno_page(page, mailno, parent,max_retries,retry_delay):
    # 任务逻辑
    for attempt in range(max_retries):
        try:
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpspacket/list')

            await page.wait_for_selector('#containNos', timeout=10000)

            # 使用选择器点击"按容器条码查询"单选框
            await page.click('#containType2')
            # 输入容器号
            await page.fill('#containNos', mailno)

            # 点击查询按钮
            await page.click('#btnSubmit_contain_no')

            try:
                await page.wait_for_selector('.mailQuery_container', timeout=10000)
                return []  # 如果加载成功，返回一个空列表或者其他你希望的内容
            except Exception:
                # tool.process_input(f"查询邮件号 {mailno} 超时，返回空数据。")
                #parent.after(0, tool.process_input(f"邮件号 {mailno} 没有轨迹信息。"))
                # 封装查询的部分，支持重试
                return []

        except Exception as e:
            # tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}")
            # parent.after(0, tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}"))
            # 捕获页面加载时的异常，如果是超时或者其他错误
            print(f"第 {attempt + 1} 次加载失败: {str(e)}")

            # 如果达到最大重试次数，则退出
            if attempt == max_retries - 1:
                parent.after(0, tool.process_input(f"\n页面加载失败，总包号: {mailno}"))
                return []

            # 否则等待重试
            print(f"等待 {retry_delay} 秒后重试...")
            await asyncio.sleep(retry_delay)


def extract_keys_by_delimiters(input_str):
    results = {}
    groups = input_str.split('#')
    for group in groups:
        if '@' in group:
            start_char, _ = group.split('@')# 使用_来丢弃分割后的第二个元素（如果有的话）
            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
            key = start_char.rstrip(':')  # 去掉冒号
            results[key] = []  # 初始化为空列表
            #print("新增键："+str(results))
    return results

def handle_input(title,parent):
    # # 创建一个新线程来执行耗时的处理操作
    # t = threading.Thread(target=batch_query_and_export, args=(title, parent))
    # t.start()
    #asyncio.run(batch_query_and_export(title, parent))  # 运行异步任务
    output_textbox.configure(state="normal")
    # 清空文本框内容
    output_textbox.delete("1.0", tk.END)  # 清空内容
    def run_async_task():
        # 创建事件循环
        asyncio.run(batch_query_and_export(title, parent))

    # 启动线程运行异步任务
    threading.Thread(target=run_async_task, daemon=True).start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected,selected2,selected3, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,\
        back_button,organization_combobox,input2_textbox,input3_textbox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='全程轨迹')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=0, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    radio_button1.grid(row=0, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    radio_button2.grid(row=0, column=2, padx=5, pady=10)

    radio_button3 = ttk.Radiobutton(input_label_container, text="指定轨迹", value="指定轨迹", variable=selected)
    radio_button3.grid(row=0, column=3)

    # 创建单选按钮
    selected2 = tk.StringVar(value='否')
    radio2_label = ttk.Label(input_label_container, text="是否只筛广航轨迹:")
    radio2_label.grid(row=1, column=0, padx=10, pady=10)
    radio2_button1 = ttk.Radiobutton(input_label_container, text="是", value='是', variable=selected2)
    radio2_button1.grid(row=1, column=1, padx=5, pady=10)

    radio2_button2 = ttk.Radiobutton(input_label_container, text="否", value='否', variable=selected2)
    radio2_button2.grid(row=1, column=2, padx=5, pady=10)

    selected3 = tk.StringVar(value='按总包')
    radio3_label = ttk.Label(input_label_container, text="查询方式:")
    radio3_label.grid(row=2, column=0, padx=10, pady=10)
    radio3_button1 = ttk.Radiobutton(input_label_container, text="按总包", value='按总包', variable=selected3)
    radio3_button1.grid(row=2, column=1, padx=5, pady=10)

    radio3_button2 = ttk.Radiobutton(input_label_container, text="按容器", value='按容器', variable=selected3)
    radio3_button2.grid(row=2, column=2, padx=5, pady=10)

    # 添加指定处理动作
    input2_label = ttk.Label(input_label_container, text="筛处理动作(多个处理动作用#分隔):")
    input2_label.grid(row=3, column=0, padx=10, pady=10)
    # input_label.pack()
    input2_textbox = tk.Entry(input_label_container, width=30)
    input2_textbox.grid(row=3, column=1, padx=10, pady=10, columnspan=1)

    input3_label = ttk.Label(input_label_container, text="提取详细明细(开始和结束字符，用@隔开，多组内容用#分隔):")
    input3_label.grid(row=4, column=0, padx=10, pady=10)
    # input_label.pack()
    input3_textbox = tk.Entry(input_label_container, width=30)
    input3_textbox.grid(row=4, column=1, padx=10, pady=10, columnspan=1)
    input3_label2 = ttk.Label(input_label_container, text="例如AAA:@,#BBB:@,#CCC:@,#DDD:@")
    input3_label2.grid(row=4, column=2, padx=10, pady=10)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="总包号:")
    input_label.grid(row=5, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=5, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=6, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=6, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()

# 添加到moni10.py
class PagePool:
    def __init__(self, context, size=10):
        self.context = context
        self.size = size
        self.available_pages = []
        self.in_use = set()

    async def initialize(self):
        for _ in range(self.size):
            page = await self.context.new_page()
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpspacket/list',
                          wait_until='domcontentloaded')
            self.available_pages.append(page)

    async def get_page(self):
        if not self.available_pages:
            page = await self.context.new_page()
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpspacket/list',
                          wait_until='domcontentloaded')
        else:
            page = self.available_pages.pop()
        self.in_use.add(page)
        return page

    async def release_page(self, page):
        if page in self.in_use:
            self.in_use.remove(page)
            self.available_pages.append(page)

class ResponseManager:
    def __init__(self):
        self.processed_urls = set()
        
    def is_processed(self, url):
        import hashlib
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        return url_hash in self.processed_urls
    
    def mark_processed(self, url):
        import hashlib
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        self.processed_urls.add(url_hash)