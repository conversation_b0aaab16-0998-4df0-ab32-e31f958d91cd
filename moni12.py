import asyncio

import os

import socket
import sys
import tkinter as tk

from functools import reduce
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *

from os import path
import requests,json, time

import pandas as pd
from datetime import datetime
import threading
from playwright.async_api import async_playwright

from bs4 import BeautifulSoup

from tool import Tool

executable_path = "firefox/firefox.exe"    # 指定可执行文件路径，用相对路径

# 请求信息类
class RequestInfo:
    def __init__(self, url, response_data=None):
        self.url = url
        self.response_data = response_data

def saveOrgShopTeamSeat(cookies):
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    url = 'https://**********/portal/a/basic/saveOrgShopTeamSeat'
    # 获取当前时间戳
    #timestamp = int(time.time() * 1000)

    # 构建 URL
    #url = f'https://**********/portal/a/basic/saveOrgShopTeamSeat?t={timestamp}'
    data = {
        'rs': '{"workShopCode":"SD51040034","workShopName":"默认车间","workShopGroupCode":"GJCK","workShopGroupName":"国际出口班","seatCode":"30001","seatName":"测试01"}'
    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        #'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    #tool.process_input(response.text)
    print(response.text)
    tool.process_input(response.text)


def joinShift(cookies):
    requests.packages.urllib3.disable_warnings()
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    #url = 'https://**********/intproc-web/a/intproc/shift/view'
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Host': '**********',
        'DNT': '1',
        'Referer': 'https://**********/portal/a',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
    }
    response = session.get(url, headers=headers, verify=False)
    r = response.text
    soup = BeautifulSoup(r, 'lxml')
    #print(r)
    bc = soup.find('input', attrs={'name': 'ids'})['value']  # 班次
    # print(bc)
    url = 'https://**********/intproc-web/a/intproc/shift/joinShift'
    data = {
        'cryptId': bc

    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    #tool.process_input(response.text)
    print(response.text)

# 用于批量处理邮件号查询并导出数据
async def batch_query_and_export(title,parent):
    global L
    # 程序已经在运行中，禁用按钮
    submit_button.configure(state="disabled")
    back_button.configure(state="disabled")
    start = time.perf_counter()
    # 获取本机主机名
    hostname = socket.gethostname()
    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}

        tool.process_input("代理功能已启用")
    L = 0

    # 保存账号和密码
    tool.save_data()
    username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
    password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
    try:
        # 使用 Playwright 启动浏览器
        async with async_playwright() as p:
            browser = await p.firefox.launch(headless=True,
                                             executable_path=executable_path,
                                             args=["--no-sandbox", "--disable-setuid-sandbox"])

            # 创建浏览器上下文
            context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
            page = await context.new_page()
            # 获取网络请求
            # page.on("request", lambda request: print( ">> ", request.method, request.url))
            # # 获取网络响应
            # page.on("response", lambda response: print( "<< ", response.status, response.text))

            # 设置 User-Agent模拟正常浏览器
            await context.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            })

            # 禁用Playwright的自动化标识
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)
            # 获取文本框中的文本
            text = input_textbox.get("1.0", "end-1c")
            # 将文本按行分割并去除空行
            lines = text.splitlines()
            lines = [line for line in lines if line.strip()]
            mailnos = lines
            #print(mailnos)
            # 登录
            userName,orgName,orgcode=await tool.newlogin(page, username, password)
            tool.postlog(username, userName, title, ip_address)


            #进入班次查询页面
            #await select_latestbc(page)

            # 获得登录后 cookie
            cookies = await context.cookies()
            # 打印cookie
            # for cookie in cookies:
            #    print(cookie)
            # 将cookie添加到session中
            #session.cookies.update(cookies)

            # 获取登录后的 cookies
            #cookies = await page.context.cookies()
            # 将 Playwright 获取的 cookies 转换为 requests 需要的格式

            saveOrgShopTeamSeat(cookies)

            #await select_latestbc(page)
            await page.goto(url="https://**********/intproc-web/a/intproc/impcustomsquery/list")
            cookies = await page.context.cookies()

            joinShift(cookies)

            await page.close()  # 登录完成后关闭页面

            all_logistics_data = []

            # 限制并发的最大任务数
            semaphore = asyncio.Semaphore(int(threads_combobox.get()))  # 设置并发上限为 5（可调整）

            async def query_with_semaphore(mailno,cookies):
                async with semaphore:

                    try:
                        #tool.process_input(f"正在查询邮件号: {mailno}")
                        if '进口' in selected.get():
                            logistics_data = await getinmailtrace( parent,mailno,cookies)
                        else:
                            logistics_data = await getoutmailtrace( parent,mailno,cookies)

                    finally:
                        pass

                    return logistics_data


            # 创建任务列表
            tasks = [query_with_semaphore(mailno,cookies) for mailno in mailnos]
            # 并发执行任务
            results = await asyncio.gather(*tasks)
            #收集所有结果
            for logistics_data in results:
                if logistics_data:
                    #print(logistics_data)
                    # 使用 append 直接将字典添加到列表中
                    all_logistics_data.append(logistics_data)
            #print(all_logistics_data)
            if '进口' in selected.get():
                merged_data = {  "邮件号": [],
                                "开拆状态": [],
                                "是否参与金关": [],
                                "验关局代码": [],
                                "验关局名称": [],
                                "关区代码": [],
                                "海关回执信息": [],
                                "是否需征税": [],
                                "是否缴税": [],
                                "是否批译": []
                                 }
            else:
                merged_data = { "邮件号": [],
                                "开拆状态": [],
                                "是否参与金关": [],
                                "验关局代码": [],
                                "验关局名称": [],
                                "关区代码": [],
                                "海关回执信息": [],
                                "通关类型": []
                                }

            merged_data = reduce(merge_dicts, all_logistics_data, merged_data)

            print(merged_data)

            # 导出到Excel
            if merged_data:

                df = pd.DataFrame(merged_data)
                # # 生成Excel文件的保存路径，当前目录 + 当前时间
                currentTime = datetime.now().strftime('%Y%m%d_%H%M%S')
                # export_path = os.path.join(os.getcwd(), f'邮件全轨迹_{current_time}.xlsx')
                row = 1048570
                length = len(df)
                number = length // row
                for i in range(number + 1):
                    export_path=os.path.join(os.getcwd(), f'{selected.get()}邮件最后回执-{currentTime}{i}-bylhx.xlsx')
                    df[i * row:(i + 1) * row].to_excel(export_path,index=False)
                # 保存文件
                #df.to_excel(export_path, index=False)
                    tool.process_input(f"\n数据已成功导出！\n文件路径：{export_path}")
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                del all_logistics_data
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
                #messagebox.showinfo("成功", f"数据已成功导出！文件路径：{export_path}")
            else:
                #messagebox.showwarning("警告", "没有获取到有效数据！")
                tool.process_input("没有获取到有效数据")
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
    except Exception as e:
        tool.process_input(f"查询失败，错误信息：{e}")
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    await browser.close()



async def select_latestbc(page):

    await page.goto('https://**********/intproc-web/a/intproc/impcustomsquery/list')
    # 等待表格加载完成
    await page.wait_for_selector("#contentTable",timeout=10000)

    # 获取所有表格行
    rows = await page.query_selector_all("#contentTable tbody tr")
    print(rows)
    latest_row = None
    latest_date = None

    for row in rows:
        # 获取每一行的日期列 (假设日期列在最后一列)
        date_text = await row.query_selector("td:nth-child(7)").inner_text()

        # 转换为日期对象以便比较
        try:
            row_date = datetime.strptime(date_text, "%Y-%m-%d %H:%M:%S")
            if latest_date is None or row_date > latest_date:
                latest_date = row_date
                latest_row = row
                print(f"最新班次: {date_text}")
        except ValueError:
            print(f"无法解析日期: {date_text}")

    if latest_row:
        # 获取对应的radio按钮并点击
        radio_button = await latest_row.query_selector("input[type='radio']")
        await radio_button.click()
        print("已选择最新的班次")
        # 等待“进入已选班次”按钮可点击
        await page.wait_for_selector("#nowshift")
        button = await page.query_selector("#nowshift")
        await button.click()  # 点击按钮进入班次

        # 等待第一个 AJAX 请求响应，假设是一个提示框
        await page.wait_for_selector(".jbox")  # 等待弹窗出现
        # 获取弹窗的文本内容，确认是否弹出了正确的提示
        dialog_text = await page.inner_text(".jbox")

        # 如果弹窗内容中有确认文字，模拟点击“确定”
        if "确定" in dialog_text:
            confirm_button = await page.query_selector(".jbox button[data-id='ok']")
            if confirm_button:
                await confirm_button.click()  # 点击“确定”

    else:
        print("没有找到班次")




async def getinmailtrace(parent,mailno,cookies):
    global L
    # print ('正在处理:'+mailno)
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/getCustomState'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Length': '20',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'DNT': '1',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        # 'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'itemId': mailno

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)
    dataOutput= {
        "邮件号": [],
        "开拆状态": [],
        "是否参与金关": [],
        "验关局代码": [],
        "验关局名称": [],
        "关区代码": [],
        "海关回执信息": [],
        "是否需征税": [],
        "是否缴税": [],
        "是否批译": []
                 }
    soup = BeautifulSoup(r, 'lxml')
    # 找到第一个 tbody 标签
    tbody = soup.find('tbody')
    # print(tbody)
    if tbody:
        # 找到 tbody 内的所有 td 标签，并取出第七个 td 的文本内容

        tds = tbody.find_all('td')
        if len(tds) >= 7:
            #seventh_td_content = tds[6].get_text()

            # print(seventh_td_content)
            dataOutput["邮件号"].append(tds[0].get_text().strip())  # 获取JSON中的id，并存入字典中的id内
            dataOutput["开拆状态"].append(tds[1].get_text().strip())
            dataOutput["是否参与金关"].append(tds[2].get_text().strip())
            dataOutput["验关局代码"].append(tds[3].get_text().strip())
            dataOutput["验关局名称"].append(tds[4].get_text().strip())
            dataOutput["关区代码"].append(tds[5].get_text().strip())
            dataOutput["海关回执信息"].append(tds[6].get_text().strip())
            dataOutput["是否需征税"].append(tds[7].get_text().strip())
            dataOutput["是否缴税"].append(tds[8].get_text().strip())
            dataOutput["是否批译"].append(tds[9].get_text().strip())
    else:
        dataOutput["邮件号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
        dataOutput["开拆状态"].append('')
        dataOutput["是否参与金关"].append('')
        dataOutput["验关局代码"].append('')
        dataOutput["验关局名称"].append('')
        dataOutput["关区代码"].append('')
        dataOutput["海关回执信息"].append('')
        dataOutput["是否需征税"].append('')
        dataOutput["是否缴税"].append('')
        dataOutput["是否批译"].append('')

    L += 1
    parent.after(0, tool.update_L(str(L)))
        # if L % 1000 == 0:
        #     # 添加适当的同步机制，例如使用 threading.Lock
        #
        #     # 模拟一些耗时操作
        #     #time.sleep(1)
        #     # 在主线程中调度更新UI
        #     parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput


async def getoutmailtrace(parent,mailno,cookies):
    global L
    #print ('正在处理:'+mailno)
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/expcustomsquery/querydomunpexpcustoms'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
    }
    data = {

        'itemId': mailno,
        'cusReleaseFlag': ''

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text

    jsonObj = json.loads(r)
    #print(r)

    dataOutput = {
        "邮件号": [],
        "开拆状态": [],
        "是否参与金关": [],
        "验关局代码": [],
        "验关局名称": [],
        "关区代码": [],
        "海关回执信息": [],
        "通关类型": []
    }

    dataOutput["邮件号"].append(jsonObj["comment"]["itemId"])  # 获取JSON中的id，并存入字典中的id内
    if jsonObj["comment"]["unpState"]=='1':
        dataOutput["开拆状态"].append('未开拆')
    else:
        dataOutput["开拆状态"].append('开拆')
    if jsonObj["comment"]["cusFlag"]=='1':
        dataOutput["是否参与金关"].append('是')
    else:
        dataOutput["是否参与金关"].append('否')
    dataOutput["验关局代码"].append(jsonObj["comment"]["custOrgCode"])
    dataOutput["验关局名称"].append(jsonObj["comment"]["custOrgName"])
    dataOutput["关区代码"].append(jsonObj["comment"]["custCode"])
    dataOutput["海关回执信息"].append(jsonObj["comment"]["custReceiptInfo"])
    dataOutput["通关类型"].append(jsonObj["comment"]["ifC9610"])


    L += 1
    parent.after(0, tool.update_L(str(L)))
    # if L % 1000 == 0:
    #     # 添加适当的同步机制，例如使用 threading.Lock
    #
    #     # 模拟一些耗时操作
    #     time.sleep(1)
    #     # 在主线程中调度更新UI
    #     parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput

# 合并字典的函数
def merge_dicts(dict1, dict2):
    for key in dict1:
        # 检查两个字典中键对应的值是否是列表，如果不是，初始化为空列表
        if key in dict2:
            dict1[key].extend(dict2[key])
        else:
            dict1[key] = []  # 如果 dict2 中没有该 key，初始化为空列表
    return dict1




def handle_input(title,parent):
    output_textbox.configure(state="normal")
    # 清空文本框内容
    output_textbox.delete("1.0", tk.END)  # 清空内容

    def run_async_task():
        # 创建事件循环
        asyncio.run(batch_query_and_export(title, parent))

    # 启动线程运行异步任务
    threading.Thread(target=run_async_task, daemon=True).start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")




def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='进口')
    radio_label = ttk.Label(input_label_container, text="邮件类型:")
    radio_label.grid(row=0, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="进口", value="进口", variable=selected)
    radio_button1.grid(row=0, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="出口", value="出口", variable=selected)
    radio_button2.grid(row=0, column=2, padx=5, pady=10)



    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=1, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=1, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()