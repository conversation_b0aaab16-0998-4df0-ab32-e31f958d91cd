# function_menu_app.py
import os
import sys
import urllib
from functools import partial
from multiprocessing import current_process
from tkinter import ttk, messagebox
import subprocess
from urllib.parse import quote

import requests
from packaging import version
import psutil

import func1
import func11bak
import func13
import func14
import func15
import func16
import func17
import func2
import func3
import func4
import func5
import func6
import func7
import func8
import func9
import func10
import func11
import func12bak



class FunctionMenuApp:
    def __init__(self, root, current_version):
        # 网页链接
        web_icon_url = 'http://************:42300/' + quote('chinapost.ico')
        # 保存图标的本地路径
        local_icon_path = "chinapost.ico"
        if self.is_process_running("新一代神器.exe"):
            messagebox.showwarning("警告", "已有一个实例在运行，无法再次启动。")
            sys.exit()
        self.root = root
        self.current_version = current_version  # 存储 current_version
        self.setup_window(current_version,web_icon_url,local_icon_path)
        self.create_buttons()

        # 在初始化时调用检测更新函数
        self.check_update()
        # 添加窗口关闭事件绑定
        self.root.protocol("WM_DELETE_WINDOW", self.quit)

    def is_process_running(self,process_name):
        current_pid = psutil.Process().pid
        running_instances = sum(1 for process in psutil.process_iter(['pid','name']) if
                                process.info['name'] == process_name and process.info['pid'] != current_pid)
        return running_instances > 2

    # 从网页获取.ico文件并保存到本地
    def download_icon(self,url, save_path):
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            return False

    # 设置窗口图标
    def set_window_icon(self,icon_path):
        if os.path.exists(icon_path):

            self.root.iconbitmap(icon_path)
            self.root.mainloop()
        else:
            print("文件路径不存在")


    def setup_window(self,current_version,web_icon_url,local_icon_path):

        self.root.title("新一代神器 版本:"+current_version+"     Power by LHX")
        self.root.overrideredirect(False)  # 允许用户移动窗口
        # # 如果本地图标文件不存在则下载并设置窗口图标
        # if not os.path.exists(local_icon_path):
        #     if self.download_icon(web_icon_url, local_icon_path):
        #         self.set_window_icon(local_icon_path)
        #     else:
        #         print("无法下载图标")
        # else:
        #     self.set_window_icon(local_icon_path)
        # 设置图标
        icon_path = 'chinapost.ico'
        self.root.iconbitmap(icon_path)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        window_width = int(screen_width / 2)
        window_height = int(screen_height / 2)
        window_geometry = f"{window_width}x{window_height}"
        self.root.geometry(window_geometry)

    def create_buttons(self):
        button_data = [
            ("批量补发短信", func1.open_func_window, "批量补发短信"),  # 如果不需要额外参数，传递 None
            ("查国内残缺邮件", func2.open_func_window, "查残缺邮件"),
            ("交航扫描统计", func3.open_func_window, "交航扫描统计"),
            ("批量处理截留邮件", func4.open_func_window, "批量处理截留邮件"),
            ("邮件全程信息统计", func5.open_func_window, "邮件全程信息统计"),
            ("路单查询取数", func6.open_func_window, "路单查询取数"),
            ("进口信息统计", func7.open_func_window, "进口信息统计"),
            ("导异常验单图片", func8.open_func_window, "导异常验单图片"),
            ("导邮件轨迹", func9.open_func_window, "导邮件轨迹"),
            ("导总包轨迹", func10.open_func_window, "导总包轨迹"),
            ("导邮件收寄计费信息", func11.open_func_window, "导邮件收寄计费信息"),  # 传递 title 参数
            ("导邮件最后回执", func12.open_func_window, "导邮件最后回执"),
            ("导邮件扣仓数据", func13.open_func_window, "导邮件扣仓数据"),
            ("导邮件全状态回执", func14.open_func_window, "导邮件全状态回执"),
            ("导邮件补录信息", func15.open_func_window, "导邮件补录信息"),
            ("导邮袋回执", func16.open_func_window, "导邮袋回执"),
            ("导补录复核信息", func17.open_func_window, "导补录复核信息"),
           ("导邮件收寄计费信息", func11bak.open_func_window, "导邮件收寄计费信息"),
        ]

        # 指定每行几列
        num_cols = 5

        for i, (text, command,title) in enumerate(button_data, 1):  # 设置 i 的初始值为 1)
            # 添加序号到按钮文本
            numbered_text = f"{i}. {text}"
            # 使用 functools.partial 创建带参数的函数
            button_command = partial(command, self.root, title)
            button = ttk.Button(self.root, text=numbered_text, command=button_command)
            button.grid(row=(i - 1) // num_cols, column=(i - 1) % num_cols, padx=10, pady=10, sticky='nsew')

        # 让行和列的权重不相等，调整按钮大小
        for i in range(len(button_data)):
            self.root.grid_rowconfigure(i // num_cols, weight=1)
            self.root.grid_columnconfigure(i % num_cols, weight=1)


    # def open_function_window(self, command):
    #     self.root.withdraw()
    #     command(self.root)

    def check_update(self):
        # 获取最新版本号和更新内容
        response = urllib.request.urlopen('http://************:42300/version.txt')
        version_info = response.readline().decode('utf-8').lstrip('\ufeff').split()

        latest_version = version_info[0]
        update_content = ' '.join(version_info[1:]) if len(version_info) > 1 else '修复部分问题'

        # 获取当前版本号
        current_version = self.current_version  # 使用实例变量
        print(current_version,latest_version)
        # 检查是否需要更新
        if version.parse(latest_version) > version.parse(current_version):
            # 构建更新提示文本
            update_message = f"有新版本可用 ({latest_version})！！！\n\n更新内容:\n{update_content}"

            # 弹出对话框询问用户是否更新（设置标题为中文）
            answer = messagebox.askquestion("更新提示", update_message)

            if answer == 'yes':

                subprocess.Popen(['updater.exe'])
            else:
                self.quit()

    def quit(self):
        # 获取当前 Python 进程 ID
        this_pid = os.getpid()

        # 关闭当前进程
        os.kill(this_pid, 9)
        # 销毁窗口以结束程序
        self.root.destroy()