import io
import json
import os
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from urllib.parse import quote
from os import path
from tool import Tool
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from bs4 import BeautifulSoup
import requests, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading
import socket




# 修改全局变量 dataOutput 为线程安全的队列

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称

def getallmail(parent,mailno):
    global L
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/bagcustomrecpquery/query'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intcubr-web/a/intcubr/mailfullstatequery/main',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {
        'bagBarcode': mailno
    }

    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    #print(url)
    #response = session.get(url,  verify=False)
    #response = session.post(url, headers=headers,data=data,verify=False)
    with session.post(url, headers=headers,data=data, verify=False) as response:
        r = response.text

        jsonObj = json.loads(r)

        dataOutput = {
            "邮袋条码": [],
            "关区代码": [],

            "回执代码": [],
            "回执信息": [],
            "回执时间": []
        }

        #print(target_tbodies)
        if jsonObj["detail"]:
            for line in jsonObj["detail"]:
                dataOutput['邮袋条码'].append(line.get('mailBagNo', ''))
                dataOutput['关区代码'].append(line.get('custCode', ''))

                dataOutput['回执代码'].append(line.get('returnStatus', ''))
                dataOutput['回执信息'].append(line.get('returnInfo', ''))
                dataOutput['回执时间'].append(line.get('returnTime', ''))
        else:
            dataOutput['邮袋条码'].append(mailno)
            dataOutput['关区代码'].append('')

            dataOutput['回执代码'].append('')
            dataOutput['回执信息'].append('')
            dataOutput['回执时间'].append('')
        L += 1
        if L % 1000 == 0:
            # 添加适当的同步机制，例如使用 threading.Lock

            # 模拟一些耗时操作
            #time.sleep(1)
            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # # 循环结束后释放session资源
    # session.close()
    return dataOutput

def getlastmail(parent,mailno):
    global L
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/bagcustomrecpquery/query'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '336',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intcubr-web/a/intcubr/mailfullstatequery/main',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }
    data = {
        'bagBarcode': mailno
    }

    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    # print(url)
    # response = session.get(url,  verify=False)
    # response = session.post(url, headers=headers,data=data,verify=False)
    with session.post(url, headers=headers, data=data, verify=False) as response:
        r = response.text

        jsonObj = json.loads(r)

        dataOutput = {
            "邮袋条码": [],
            "关区代码": [],
            "是否转关邮袋": [],
            "回执代码": [],
            "回执信息": [],
            "回执时间": [],
        }

        # print(target_tbodies)
        if jsonObj["comment"]:
            dataOutput['邮袋条码'].append(jsonObj["comment"]["mailBagNo"])
            dataOutput['关区代码'].append(jsonObj["comment"]["custCode"])
            dataOutput['是否转关邮袋'].append(jsonObj["comment"]["reserved5"])
            dataOutput['回执代码'].append(jsonObj["comment"]["returnStatus"])
            dataOutput['回执信息'].append(jsonObj["comment"]["returnInfo"])
            dataOutput['回执时间'].append(jsonObj["comment"]["returnTime"])
        else:
            dataOutput['邮袋条码'].append(mailno)
            dataOutput['关区代码'].append('')
            dataOutput['是否转关邮袋'].append('')
            dataOutput['回执代码'].append('')
            dataOutput['回执信息'].append('')
            dataOutput['回执时间'].append('')
        L += 1
        if L % 1000 == 0:
            # 添加适当的同步机制，例如使用 threading.Lock

            # 模拟一些耗时操作
            # time.sleep(1)
            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # # 循环结束后释放session资源
    # session.close()
    return dataOutput


def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data


        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************'==ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()
        password = password_entry.get()

        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')

        url='https://**********/intproc-web/a/intproc/bagcustomrecpquery/main'
        result=tool.getck(username,password,session,url)
        jdptid = result[0]
        userName=result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName,title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))

        if '全部' in selected.get():
            # 并发执行并获取结果
            merged_data = {
                "邮袋条码": [],
                "关区代码": [],

                "回执代码": [],
                "回执信息": [],
                "回执时间": [],
            }
            #results = pool.map(lambda mailno: getallmail(mailno, parent), datalist)
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getallmail, parent)
            
            
        else:
            merged_data = {
                "邮袋条码": [],
                "关区代码": [],
                "是否转关邮袋": [],
                "回执代码": [],
                "回执信息": [],
                "回执时间": [],
            }
            #results = pool.map(lambda mailno: getlastmail(mailno, parent), datalist)
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getlastmail, parent)
            
        results = pool.map(getmailtrace_bound, datalist)
            
        # 合并字典数据
        def merge_dicts(dict1, dict2):
            for key in dict1:
                dict1[key].extend(dict2[key])
            return dict1

        merged_data = reduce(merge_dicts, results, merged_data)

        tool.process_input('处理完毕')
        if bool(merged_data):
            tool.process_input('正在写入Excel')

            # 定义当前时间
            currentTime = datetime.datetime.now()
            dataForm = pandas.DataFrame(merged_data)
            row = 1048570
            length = len(dataForm)
            number = length // row
            for i in range(number + 1):
                dataForm[i * row:(i + 1) * row].to_excel("邮袋"+selected.get()+"-" +
                                                         currentTime.strftime("%Y%m%d%H%M%S")  + "-bylhx.xlsx",
                                                         index=False)

            tool.process_input("写入完成共" + str(number + 1) + "个文件")
        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        #del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()




def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox, \
        submit_button, button_clear, start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, \
        business_label, business_combobox, business_options, selected, jlyy_textbox,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建主窗口
    #root = tk.Tk()

    #today = datetime.datetime.today()
    #yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)


    # 创建代理功能勾选框的变量
    #proxy_enabled = tk.BooleanVar()

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["国际"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项


    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='最后回执')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=0, column=0, padx=5, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="全部回执", value="全部回执", variable=selected)
    radio_button1.grid(row=0, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="最后回执", value="最后回执", variable=selected)
    radio_button2.grid(row=0, column=2, padx=5, pady=10)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮袋条码:")
    input_label.grid(row=1, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=1, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行",command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)
    # 创建工具类实例
    tool = Tool(output_textbox, account_entry,password_entry,func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单", command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
