import datetime
import io
import os
import socket
import sys
import threading
import tkinter as tk
import traceback
import urllib
from datetime import timedelta
from functools import reduce
from multiprocessing.dummy import Pool
from tkinter import *
from tkinter import scrolledtext
from tkinter import ttk

import pandas
import re
import requests
import time
from os import path
from bs4 import BeautifulSoup
from tkcalendar import DateEntry

from tool import Tool


# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

# output_folder = "路单查询统计-" +datetime.datetime.now().strftime("%Y%m%d%H%M%S")  # 文件夹名称
#
# output_folder=os.path.join(current_dir, output_folder)

def getmail(parent):
    start_date_str = str(
        start_date_entry.get_date()) + ' ' + starthour_combobox.get() + ':' + startminute_combobox.get() + ':' + startsecond_combobox.get()
    end_date_str = str(
        end_date_entry.get_date()) + ' ' + endhour_combobox.get() + ':' + endminute_combobox.get() + ':' + endsecond_combobox.get()

    # 将字符串转换为datetime对象
    start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d %H:%M:%S')
    end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d %H:%M:%S')

    # 定义时间间隔
    time_interval = timedelta(days=1)

    # 初始化当前分组的开始时间
    current_start_date = start_date
    combined_results = {}
    while current_start_date < end_date:
        # 定义当前分组的结束时间
        current_end_date = min(current_start_date + time_interval, end_date)

        # 将当前分组的开始时间和结束时间转换为字符串
        current_start_date_str = current_start_date.strftime('%Y-%m-%d %H:%M:%S')
        current_end_date_str = current_end_date.strftime('%Y-%m-%d %H:%M:%S')

        # 在当前时间分组内执行代码
        print(f'运行代码，时间分组：{current_start_date_str} 到 {current_end_date_str}')

        # 执行查询
        result = getzb(current_start_date_str, current_end_date_str, parent)

        # 合并结果
        for key, value in result.items():
            if key not in combined_results:
                combined_results[key] = value
            else:
                combined_results[key].extend(value)
        # 更新当前分组的开始时间
        current_start_date = current_end_date

    return combined_results
def getzb(start_date,end_date,parent):
    #print(jdptid)


    tool.process_input(f'时间分组：{start_date} 到 {end_date}')

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/monitor-web/a/monitor/orgwaybilldiff/listresult'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/monitor-web/a/monitor/orgwaybilldiff/tolist',
        'Sec-Ch-Ua':'"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }


    data = {
        'exportType': 0,
        'orgName': '广州航空中心',
        'orgCode': 51000061,
        'baseProductCode': '11210,11312,11111,11510,11610',
        'lineLevel': '1,2,3',
        'teanFlag': 0,
        'sanhuFlag': 0,
        'keynoteFlag': 0,
        'custProjectNoFlag': 0,
        'beginTime': start_date,
        'endTime': end_date
    }

    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)


    if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员')
        return
    else:
    #html=BeautifulSoup(r,'html.parser')
        soup = BeautifulSoup(r, 'html.parser')

        # 查找所有 a 标签
        links = soup.find_all('a')

        # 用正则表达式提取包含 'unseal72' 的链接前面的三个参数
        pattern = re.compile(r"openDetailView\('([^']*)','([^']*)','([^']*)','unseal72'\)")
        combined_output={}


        for link in links:
            onclick_content = link.get('onclick', '')
            match = pattern.search(onclick_content)
            if match:
                param1, param2, param3 = match.groups()
                print(f'param1: {param1}, param2: {param2}, param3: {param3}')
                dataOutput=process_page(param1, param2, param3,start_date,end_date,parent)
                # 合并 process_page 返回的结果到 combined_output 中
                for key, value in dataOutput.items():
                    if key not in combined_output:
                        combined_output[key] = value
                    else:
                        combined_output[key].extend(value)
    return combined_output


def process_page(param1,param2,param3,start_date,end_date,parent):
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/monitor-web/a/monitor/orgwaybilldiff/listresultdetail'+'?exportType=0&beginTime='+start_date\
          +'&endTime='+end_date+'&baseProductCode='+param3+'&lineLevel=1,2,3&orgCode=51000061&lineType=&routeCode='+param1\
          +'&billNo='+param2+'&detailType=unseal72&teanFlag=0&sanhuFlag=0&keynoteFlag=0&custProjectNoFlag=0'
    url='https://**********/monitor-web/a/monitor/orgwaybilldiff/listresultdetail'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-Length': '217',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Dnt': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/monitor-web/a/monitor/orgwaybilldiff/listresult',
        'Sec-Ch-Ua': '"Chromium";v = "15", "Not.A/Brand";v = "8"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
    }

    data = {
        'exportType': 0,
        'beginTime': start_date,
        'endTime': end_date,
        'baseProductCode': param3,
        'lineLevel': '1,2,3',
        'orgCode': 51000061,
        'lineType': '',
        'routeCode': param1,
        'billNo': param2,
        'detailType': 'unseal72',
        'teanFlag': 0,
        'sanhuFlag': 0,
        'keynoteFlag': 0,
        'custProjectNoFlag': 0
    }

    response = session.post(url, headers=headers,data=data,verify=False)


    r = response.text
    #print(r)
    if '系统后台问题，请重试，若仍有问题，请联系中心运维人员' in r:
        parent.after(0,tool.process_input('系统后台问题，请重试，若仍有问题，请联系中心运维人员'))

    # 初始化 dataOutput 字典
    dataOutput = {}
    html = BeautifulSoup(r, 'lxml')
    zjs = html.find('tbody')
    if zjs:
        trs = zjs.find_all('tr')
        for tr in trs:
            tds = tr.find_all('td')
            #print(tds)
            if tds:
                fields = [
                    "省份", "地市", "区县", "机构名称", "机构代码", "机构属性", "处理中心等级", "邮件条码", "邮件种类",
                    "总包条码", "是否特安邮件", "是否散户邮件", "是否重点邮件", "是否重点项目邮件", "来源邮路",
                    "邮路级别", "路单流水号", "解车时间", "到达时间", "最新扫描动作", "最新扫描动作操作人",
                    "最新扫描动作操作时间", "计划出口时间", "操作设备", "操作设备编号"
                ]

                # 遍历所有字段
                for i, field in enumerate(fields):
                    # 使用字段名称作为字典的键
                    dataOutput.setdefault(field, []).append(tds[i].get_text().strip())
    return dataOutput

def date_range(start_date, end_date):
    start = datetime.datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.datetime.strptime(end_date, '%Y-%m-%d')
    delta = datetime.timedelta(days=1)
    current_date = start
    while current_date <= end:
        yield current_date.strftime('%Y-%m-%d')
        current_date += delta

 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def run(title,parent):
    try:
        global username, password, session, jdptid,session2, jdptid2, L
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")

        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")

        # 保存账号和密码
        tool.save_data()

        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()


        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')

        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/monitor-web/a/monitor/orgwaybilldiff/tolist'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break

        tool.postlog(username, userName, title, ip_address)
        #routeLevel()
        tool.process_input('当前线程:' + threads_combobox.get())
        #pool = Pool(int(threads_combobox.get()))



        merged_data=getmail(parent)



        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()

        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("机构内邮件差异报告-" +
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)

        tool.process_input("写入完成共" + str(number + 1) + "个文件")


        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")



    except Exception as e:
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")

# # 回调函数修改状态下拉框的选项列表
# def on_interFlag_change():
#     global statusJ_options
#     selected_interFlag = interFlag_combobox.get()
#     if selected_interFlag == "出口":
#         statusJ_options = {
#             "未封车": "0",
#             "已封车": "1",
#             "预封车": "2"
#         }
#         statusJ_combobox.configure(values=list(statusJ_options.keys()))
#         statusJ_combobox.set("未封车")
#     else:
#         statusJ_options = {
#             "未进局": "3",
#             "进局未解车": "4",
#             "已解车": "5"
#         }
#         statusJ_combobox.configure(values=list(statusJ_options.keys()))
#         statusJ_combobox.set("未进局")
#
# def on_organization_change():
#     global workshop_options
#     selected_organization = organization_combobox.get()
#     print(selected_organization)
#     if selected_organization == "国际":
#         workshop_options = {
#             "默认车间": "SD51040034",
#             "函件车间": "SD51043407",
#             "国际进出": "SD51043408",
#             "专封车间": "SD51043404",
#             "海运车间": "SD51043406",
#             "佛山前置": "SD5104003402",
#             "清运车间": "SD51043405",
#             "香港渠道": "SD5104003403",
#             "石井前置": "SD5104003401",
#             "重货车间": "SD51045100",
#             "YD客户车间": "SD51043410",
#             "ZH客户车间": "SD51043409",
#
#         }
#         workshop_combobox.configure(values=list(workshop_options.keys()))
#         workshop_combobox.set("默认车间")
#     else:
#         workshop_options = {
#             "国际作业": "SD51000190",
#             "华南集散": "SD51001111",
#             "默认车间": "SD51000061",
#             "进口作业": "SD51000400",
#             "航空转运": "SD51000003",
#             "槎头": "SD51000192",
#             "空侧转运中心": "SD51000815",
#             "录取通知书专用车间": "SD51000166",
#             "苹果专用车间": "SD51000920",
#
#         }
#         workshop_combobox.configure(values=list(workshop_options.keys()))
#         workshop_combobox.set("航空转运")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session,session2, jdptid,jdptid2, L,  input_textbox, output_textbox, root, submit_button, button_clear, \
        start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,interFlag_options,interFlag_combobox,\
    routeLevel_options, routeLevel_combobox,statusJ_options,statusJ_combobox,starthour_var,startminute_var,endhour_var,endminute_var,\
    starthour_combobox,startminute_combobox,endhour_combobox,endminute_combobox,tool,back_button,organization_combobox,startsecond_var,startsecond_combobox,endsecond_var,endsecond_combobox, \
    workshop_combobox, workshop_options,selected



    # 构造Session
    session = requests.Session()
    session2 = requests.Session()
    # 创建主窗口
    #root = tk.Tk()

    today = datetime.datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["国际", "国内"])

    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)


    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=1, column=0)

    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=0, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(date_container, maxdate=today.date())
    start_date_entry.grid(row=0, column=1, padx=10, pady=10)
    # start_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=0, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    starthours = [str(h).zfill(2) for h in range(24)]
    startminutes = [str(m).zfill(2) for m in range(60)]
    startseconds = [str(s).zfill(2) for s in range(60)]

    starthour_var = StringVar()
    startminute_var = StringVar()
    startsecond_var = StringVar()

    starthour_combobox = ttk.Combobox(date_container, textvariable=starthour_var, values=starthours, width=5)
    starthour_combobox.set(starthours[0])
    starthour_combobox.grid(row=0, column=3, padx=5, pady=5)

    startminute_combobox = ttk.Combobox(date_container, textvariable=startminute_var, values=startminutes, width=5)
    startminute_combobox.set(startminutes[0])
    startminute_combobox.grid(row=0, column=4, padx=5, pady=5)
    # startminute_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())

    startsecond_combobox = ttk.Combobox(date_container, textvariable=startsecond_var, values=startseconds, width=5)
    startsecond_combobox.set(startseconds[0])
    startsecond_combobox.grid(row=0, column=5, padx=5, pady=5)

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=1, column=0, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(date_container, maxdate=today.date())
    end_date_entry.grid(row=1, column=1, padx=10, pady=10)
    # end_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=1, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    endhours = [str(h).zfill(2) for h in range(24)]
    endminutes = [str(m).zfill(2) for m in range(60)]
    endseconds = [str(s).zfill(2) for s in range(60)]

    endhour_var = StringVar()
    endminute_var = StringVar()
    endsecond_var = StringVar()

    endhour_combobox = ttk.Combobox(date_container, textvariable=endhour_var, values=endhours, width=5)
    endhour_combobox.set(endhours[23])
    endhour_combobox.grid(row=1, column=3, padx=5, pady=5)

    endminute_combobox = ttk.Combobox(date_container, textvariable=endminute_var, values=endminutes, width=5)
    endminute_combobox.set(endminutes[0])
    endminute_combobox.grid(row=1, column=4, padx=5, pady=5)

    endsecond_combobox = ttk.Combobox(date_container, textvariable=endsecond_var, values=endseconds, width=5)
    endsecond_combobox.set(endseconds[0])
    endsecond_combobox.grid(row=1, column=5, padx=5, pady=5)


    # # 添加查询类型下拉框
    # interFlag_label = ttk.Label(date_container, text="查询类型:")
    # interFlag_label.grid(row=2, column=0, padx=5, pady=10)
    #
    # interFlag_options = {
    #     "出口": "1",
    #     "进口": "0"
    # }
    # interFlag_combobox = ttk.Combobox(date_container, values=list(interFlag_options.keys()))
    # interFlag_combobox.grid(row=2, column=1, padx=5, pady=10)
    # interFlag_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())
    # interFlag_combobox.set("出口")
    #
    # # 添加邮路级别下拉框
    # routeLevel_label = ttk.Label(date_container, text="邮路级别:")
    # routeLevel_label.grid(row=2, column=2, padx=5, pady=10)
    #
    # routeLevel_options = {
    #     "全部": "",
    #     "一干": "1",
    #     "二干": "2",
    #     "邮区": "3"
    #
    # }
    # routeLevel_combobox = ttk.Combobox(date_container, values=list(routeLevel_options.keys()))
    # routeLevel_combobox.grid(row=2, column=3, padx=5, pady=10)
    # routeLevel_combobox.set("全部")
    #
    # # 添加状态下拉框
    # statusJ_label = ttk.Label(date_container, text="状态:")
    # statusJ_label.grid(row=3, column=0, padx=5, pady=10)
    #
    # statusJ_options = {
    #     "未封车": "0",
    #     "已封车": "1",
    #     "预封车": "2"
    # }
    # statusJ_combobox = ttk.Combobox(date_container, values=list(statusJ_options.keys()))
    # statusJ_combobox.grid(row=3, column=1, padx=5, pady=10)
    # statusJ_combobox.set("未封车")
    #
    # # 添加车间下拉框
    # workshop_label = ttk.Label(date_container, text="车间:")
    # workshop_label.grid(row=3, column=2, padx=5, pady=10)
    # workshop_options = {
    #     "默认车间": "SD51040034",
    #     "函件车间": "SD51043407",
    #     "国际进出": "SD51043408",
    #     "专封车间": "SD51043404",
    #     "海运车间": "SD51043406",
    #     "佛山前置": "SD5104003402",
    #     "清运车间": "SD51043405",
    #     "香港渠道": "SD5104003403",
    #     "石井前置": "SD5104003401",
    #     "重货车间": "SD51045100",
    #     "YD客户车间": "SD51043410",
    #     "ZH客户车间": "SD51043409",
    #
    # }
    # workshop_combobox = ttk.Combobox(date_container, values=list(workshop_options.keys()))
    # workshop_combobox.grid(row=3, column=3, padx=5, pady=10)
    # workshop_combobox.set("默认车间")
    #
    # # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=2, column=0)
    #
    # # 创建单选按钮
    # selected = tk.StringVar(value='默认')
    # radio_label = ttk.Label(input_label_container, text="功能选择:")
    # radio_label.grid(row=1, column=0, padx=5, pady=5)
    # radio_button1 = ttk.Radiobutton(input_label_container, text="默认", value="默认", variable=selected)
    # radio_button1.grid(row=1, column=1)
    #
    # radio_button2 = ttk.Radiobutton(input_label_container, text="下钻邮件", value="下钻邮件", variable=selected)
    # radio_button2.grid(row=1, column=2)
    #
    # radio_button3 = ttk.Radiobutton(input_label_container, text="解车超24h", value="解车超24h", variable=selected)
    # radio_button3.grid(row=1, column=3)
    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()
    # organization_combobox.bind("<<ComboboxSelected>>", lambda _: on_organization_change(),lambda _:tool.read_data())
    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()