import io
import os
import socket
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
import requests,json, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading

from bs4 import BeautifulSoup

from tool import Tool

# 修改全局变量 dataOutput 为线程安全的队列

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称


def getinmailtrace(parent,mailno):
    global L
    # print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/getCustomState'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Length': '20',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'DNT': '1',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'itemId': mailno

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)
    dataOutput= {
        "邮件号": [],
        "开拆状态": [],
        "是否参与金关": [],
        "验关局代码": [],
        "验关局名称": [],
        "关区代码": [],
        "回执时间": [],
        "海关回执信息": [],
        "是否需征税": [],
        "是否缴税": [],
        "是否批译": []
                 }
    soup = BeautifulSoup(r, 'lxml')
    # 找到第一个 tbody 标签
    tbodies = soup.find_all('tbody')


    tbody = soup.find('tbody')

    # print(tbody)
    if tbody:
        # 找到 tbody 内的所有 td 标签，并取出第七个 td 的文本内容

        tds = tbody.find_all('td')
        if len(tds) >= 7:
            #seventh_td_content = tds[6].get_text()

            # print(seventh_td_content)
            dataOutput["邮件号"].append(tds[0].get_text())  # 获取JSON中的id，并存入字典中的id内
            dataOutput["开拆状态"].append(tds[1].get_text())
            dataOutput["是否参与金关"].append(tds[2].get_text())
            dataOutput["验关局代码"].append(tds[3].get_text())
            dataOutput["验关局名称"].append(tds[4].get_text())
            dataOutput["关区代码"].append(tds[5].get_text())
            dataOutput["海关回执信息"].append(tds[6].get_text())
            dataOutput["是否需征税"].append(tds[7].get_text())
            dataOutput["是否缴税"].append(tds[8].get_text())
            dataOutput["是否批译"].append(tds[9].get_text().strip())

            if len(tbodies) >= 2:  # 确保至少有两个 tbody
                tbody = tbodies[1]  # 获取第二个 tbody

                tds = tbody.find_all('td')
                if len(tds) >= 5:
                    dataOutput["回执时间"].append(tds[1].get_text())
                else:
                    dataOutput["回执时间"].append('')

    else:
        dataOutput["邮件号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
        dataOutput["开拆状态"].append('')
        dataOutput["是否参与金关"].append('')
        dataOutput["验关局代码"].append('')
        dataOutput["验关局名称"].append('')
        dataOutput["关区代码"].append('')
        dataOutput["回执时间"].append('')
        dataOutput["海关回执信息"].append('')
        dataOutput["是否需征税"].append('')
        dataOutput["是否缴税"].append('')
        dataOutput["是否批译"].append('')


        L += 1
        if L % 1000 == 0:
            # 添加适当的同步机制，例如使用 threading.Lock

            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))

    return dataOutput


def getallinmailtrace(parent, mailno):
    global L
    # print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/getCustomState'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Length': '20',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'DNT': '1',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'itemId': mailno

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    # print(r)
    dataOutput = {
        "邮件号": [],
        "序号": [],
        "回执时间": [],
        "回执状态": [],
        "回执信息": [],
        "是否需征税": []
    }
    soup = BeautifulSoup(r, 'lxml')
    # 找到第一个 tbody 标签
    tbodies = soup.find_all('tbody')  # 查找所有 tbody 元素
    # print(tbody)
    if len(tbodies) >= 2:  # 确保至少有两个 tbody
        tbody = tbodies[1]  # 获取第二个 tbody
        # 找到 tbody 内的所有 td 标签，并取出第七个 td 的文本内容
        rows = tbody.find_all('tr')  # 查找 tbody 内的所有行
        for row in rows:

            tds = row.find_all('td')
            if len(tds) >= 5:
                dataOutput["序号"].append(tds[0].get_text())
                dataOutput["邮件号"].append(mailno)

                dataOutput["回执时间"].append(tds[1].get_text())
                dataOutput["回执状态"].append(tds[2].get_text())
                dataOutput["回执信息"].append(tds[3].get_text())
                dataOutput["是否需征税"].append(tds[4].get_text())

    else:
        dataOutput["邮件号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
        dataOutput["序号"].append('')
        dataOutput["回执时间"].append('')
        dataOutput["回执状态"].append('')
        dataOutput["回执信息"].append('')
        dataOutput["是否需征税"].append('')

        L += 1
        if L % 1000 == 0:
            # 添加适当的同步机制，例如使用 threading.Lock

            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))

    return dataOutput


def saveOrgShopTeamSeat():
    url = 'https://**********/portal/a/basic/saveOrgShopTeamSeat'
    # 获取当前时间戳
    timestamp = int(time.time() * 1000)

    # 构建 URL
    #url = f'https://**********/portal/a/basic/saveOrgShopTeamSeat?t={timestamp}'
    data = {
        'rs': '{"workShopCode":"SD51040034","workShopName":"默认车间","workShopGroupCode":"GJCK","workShopGroupName":"国际出口班","seatCode":"30001","seatName":"测试01"}'
    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    #tool.process_input(response.text)
    print("保存台席\n"+response.text+'\n')
def joinShift():
    requests.packages.urllib3.disable_warnings()

    #url = 'https://**********/intproc-web/a/intproc/shift/view'
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Host': '**********',
        'DNT': '1',
        'Referer': 'https://**********/portal/a',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    response = session.get(url, headers=headers, verify=False)
    r = response.text
    soup = BeautifulSoup(r, 'lxml')
    #print(r)
    bc = soup.find('input', attrs={'name': 'ids'})['value']  # 班次
    # print(bc)
    url = 'https://**********/intproc-web/a/intproc/shift/joinShift'
    data = {
        'cryptId': bc

    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    tool.process_input(response.text)
    #print(response.text)


def getoutmailtrace(parent,mailno):
    global L
    #print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/expcustomsquery/querydomunpexpcustoms'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'itemId': mailno,
        'cusReleaseFlag': ''

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    try:
        jsonObj = json.loads(r)
        #print(r)

        dataOutput = {
            "邮件号": [],
            "开拆状态": [],
            "是否参与金关": [],
            "验关局代码": [],
            "验关局名称": [],
            "关区代码": [],
            "回执时间": [],
            "海关回执信息": [],
            "通关类型": []
        }
    #     if 'detail' in jsonObj and jsonObj['detail']:
    #         dataOutput["回执时间"].append(jsonObj['detail'][0]['respTimeStr'])
    #     else:
    #         dataOutput["回执时间"].append('无')
    #
    #     if "comment" in jsonObj:
    #         #dataOutput["邮件号"].append(jsonObj["comment"]["itemId"])  # 获取JSON中的id，并存入字典中的id内
    #        # dataOutput["邮件号"].append(jsonObj["comment"].get("itemId", ""))
    #         dataOutput["邮件号"].append(mailno)
    #         if jsonObj["comment"]["unpState"]=='1':
    #             dataOutput["开拆状态"].append('未开拆')
    #         else:
    #             dataOutput["开拆状态"].append('开拆')
    #         if jsonObj["comment"]["cusFlag"]=='1':
    #             dataOutput["是否参与金关"].append('是')
    #         else:
    #             dataOutput["是否参与金关"].append('否')
    #         dataOutput["验关局代码"].append(jsonObj["comment"]["custOrgCode"])
    #         dataOutput["验关局名称"].append(jsonObj["comment"]["custOrgName"])
    #         dataOutput["关区代码"].append(jsonObj["comment"]["custCode"])
    #         dataOutput["海关回执信息"].append(jsonObj["comment"]["custReceiptInfo"])
    #         dataOutput["通关类型"].append(jsonObj["comment"]["ifC9610"])
    #     else:
    #         dataOutput["邮件号"].append(mailno)
    #         dataOutput["开拆状态"].append('无')
    #         dataOutput["是否参与金关"].append('无')
    #         dataOutput["验关局代码"].append('无')
    #         dataOutput["验关局名称"].append('无')
    #         dataOutput["关区代码"].append('无')
    #         dataOutput["回执时间"].append('无')
    #         dataOutput["海关回执信息"].append('无')
    #         dataOutput["通关类型"].append('无')
    # except Exception as e:
    #     print(mailno)
    #     print(e)
    #     dataOutput["邮件号"].append(mailno)
    #     dataOutput["开拆状态"].append('无')
    #     dataOutput["是否参与金关"].append('无')
    #     dataOutput["验关局代码"].append('无')
    #     dataOutput["验关局名称"].append('无')
    #     dataOutput["关区代码"].append('无')
    #     dataOutput["回执时间"].append('无')
    #     dataOutput["海关回执信息"].append('无')
    #     dataOutput["通关类型"].append('无')
        # 处理回执时间
        if 'detail' in jsonObj and jsonObj['detail']:
            dataOutput["回执时间"].append(jsonObj['detail'][0].get('respTimeStr', '无'))
        else:
            dataOutput["回执时间"].append('无')

        # 处理comment部分
        comment = jsonObj.get("comment", {})
        if comment:
            # 确保每个字段都有默认值
            dataOutput["邮件号"].append(mailno)

            unp_state = comment.get("unpState", "")
            dataOutput["开拆状态"].append('未开拆' if unp_state == '1' else '开拆')

            cus_flag = comment.get("cusFlag", "")
            dataOutput["是否参与金关"].append('是' if cus_flag == '1' else '否')

            dataOutput["验关局代码"].append(comment.get("custOrgCode", "无"))
            dataOutput["验关局名称"].append(comment.get("custOrgName", "无"))
            dataOutput["关区代码"].append(comment.get("custCode", "无"))
            dataOutput["海关回执信息"].append(comment.get("custReceiptInfo", "无"))  # 注意原代码可能有拼写错误
            dataOutput["通关类型"].append(comment.get("ifC9610", "无"))
        else:
            # comment不存在时填充所有字段为'无'
            dataOutput["邮件号"].append(mailno)
            dataOutput["开拆状态"].append('无')
            dataOutput["是否参与金关"].append('无')
            dataOutput["验关局代码"].append('无')
            dataOutput["验关局名称"].append('无')
            dataOutput["关区代码"].append('无')
            dataOutput["海关回执信息"].append('无')
            dataOutput["通关类型"].append('无')

    except Exception as e:
        print(f"处理{mailno}时出错：{e}")
        # 异常时确保所有字段都被填充
        dataOutput["邮件号"].append(mailno)
        dataOutput["开拆状态"].append('无')
        dataOutput["是否参与金关"].append('无')
        dataOutput["验关局代码"].append('无')
        dataOutput["验关局名称"].append('无')
        dataOutput["关区代码"].append('无')
        dataOutput["回执时间"].append('无')
        dataOutput["海关回执信息"].append('无')
        dataOutput["通关类型"].append('无')
        return dataOutput
    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

        # 模拟一些耗时操作
        #time.sleep(1)
        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput


def getalloutmailtrace(parent,mailno):
    global L
    #print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/expcustomsquery/querydomunpexpcustoms'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'itemId': mailno,
        'cusReleaseFlag': ''

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    try:
        jsonObj = json.loads(r)
        #print(r)

        dataOutput = {
            "邮件号": [],
            "序号": [],
            "回执时间": [],
            "回执状态": [],
            "回执信息": [],
            "是否需征税": []
        }
        if 'detail' in jsonObj and jsonObj['detail']:
            for i in range(len(jsonObj['detail'])):
                dataOutput["邮件号"].append(mailno)
                dataOutput["序号"].append(i+1)
                dataOutput["回执时间"].append(jsonObj['detail'][i]['respTimeStr'])
                dataOutput["回执状态"].append(jsonObj['detail'][i]['custReceiptCode'])
                dataOutput["回执信息"].append(jsonObj['detail'][i]['custReceiptInfo'])
                dataOutput["是否需征税"].append(jsonObj['detail'][i]['taxFlag'])

        else:
            dataOutput["邮件号"].append(mailno)
            dataOutput["序号"].append('无')
            dataOutput["回执时间"].append('无')
            dataOutput["回执状态"].append('无')
            dataOutput["回执信息"].append('无')
            dataOutput["是否需征税"].append('无')
    except Exception as e:
        print(e)
        dataOutput["邮件号"].append(mailno)
        dataOutput["序号"].append('无')
        dataOutput["回执时间"].append('无')
        dataOutput["回执状态"].append('无')
        dataOutput["回执信息"].append('无')
        dataOutput["是否需征税"].append('无')
        return dataOutput
    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

        # 模拟一些耗时操作
        #time.sleep(1)
        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput


 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data


        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数

        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        #tool.postlog(username,title,ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')
        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/intproc-web/a/intproc/impcustomsquery/list'
        #url = 'https://**********/intproc-web/a/intproc/shift/view'
        result = tool.getck(username, password, session, url,'互换局')
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url,'互换局')
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #saveOrgShopTeamSeat()
        joinShift()
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果

        # 并发执行并获取结果
        if '进口' in selected.get():
            if '全部回执' in selected2.get():
                merged_data = {
                    "邮件号": [],
                    "序号": [],
                    "回执时间": [],
                    "回执状态": [],
                    "回执信息": [],
                    "是否需征税": []
                }
                getmailtrace_bound = partial(getallinmailtrace, parent)
            else:
                merged_data = {
                    "邮件号": [],
                    "开拆状态": [],
                    "是否参与金关": [],
                    "验关局代码": [],
                    "验关局名称": [],
                    "关区代码": [],
                    "回执时间": [],
                    "海关回执信息": [],
                    "是否需征税": [],
                    "是否缴税": [],
                    "是否批译": []
                }
                # 创建一个偏函数，其中root参数预先设定
                getmailtrace_bound = partial(getinmailtrace, parent)
            #results = pool.map(lambda mailno: getinmailtrace(mailno, parent), datalist)
        elif '出口' in selected.get():
            if '全部回执' in selected2.get():
                merged_data = {
                    "邮件号": [],
                    "序号": [],
                    "回执时间": [],
                    "回执状态": [],
                    "回执信息": [],
                    "是否需征税": []
                }
                getmailtrace_bound = partial(getalloutmailtrace, parent)
            else:
                merged_data = {
                    "邮件号": [],
                    "开拆状态": [],
                    "是否参与金关": [],
                    "验关局代码": [],
                    "验关局名称": [],
                    "关区代码": [],
                    "回执时间": [],
                    "海关回执信息": [],
                    "通关类型": []
                }
                # 创建一个偏函数，其中root参数预先设定
                getmailtrace_bound = partial(getoutmailtrace, parent)
            #results = pool.map(lambda mailno: getoutmailtrace(mailno, parent), datalist)
        results = pool.map(getmailtrace_bound, datalist)
        merged_data = reduce(merge_dicts, results, merged_data)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()
        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel(selected.get()+"邮件"+selected2.get() +"-"+
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")




def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected,selected2, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='进口')
    radio_label = ttk.Label(input_label_container, text="邮件类型:")
    radio_label.grid(row=0, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="进口", value="进口", variable=selected)
    radio_button1.grid(row=0, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="出口", value="出口", variable=selected)
    radio_button2.grid(row=0, column=2, padx=5, pady=10)

    selected2 = tk.StringVar(value='最后回执')
    radio_label2 = ttk.Label(input_label_container, text="功能:")
    radio_label2.grid(row=1, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="最后回执", value="最后回执", variable=selected2)
    radio_button1.grid(row=1, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="全部回执", value="全部回执", variable=selected2)
    radio_button2.grid(row=1, column=2, padx=5, pady=10)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=2, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=2, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=3, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=3, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()