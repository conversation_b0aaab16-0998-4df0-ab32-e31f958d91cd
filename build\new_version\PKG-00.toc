('E:\\test\\build\\new_version\\new_version.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz', 'E:\\test\\build\\new_version\\PYZ-00.pyz', 'PYZ'),
  ('struct', 'E:\\test\\build\\new_version\\localpycs\\struct.pyc', 'PYMODULE'),
  ('pyimod01_archive',
   'E:\\test\\build\\new_version\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\test\\build\\new_version\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\test\\build\\new_version\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\test\\build\\new_version\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'E:\\test\\main.py', 'PYSOURCE'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('python37.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\python37.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\WINDOWS\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_x25519.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\PublicKey\\_x25519.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\select.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\md.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\etree.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\_elementpath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\sax.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\objectify.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\html\\diff.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\clean.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\html\\clean.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\builder.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\writers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\testing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\sparse.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\reshape.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\reduction.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\properties.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\parsers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\ops.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\missing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\json.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\join.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\interval.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\internals.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\indexing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\index.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\hashing.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\groupby.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\arrays.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\algos.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\tslib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\lib.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp37-win_amd64.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\hashtable.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\WINDOWS\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('sqlite3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\python3.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\DLLs\\tk86t.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\test\\build\\new_version\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('babel\\locale-data\\nmg_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nmg_CM.dat',
   'DATA'),
  ('babel\\locale-data\\dyo_SN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dyo_SN.dat',
   'DATA'),
  ('babel\\locale-data\\agq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\agq.dat',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('babel\\locale-data\\ccp_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ccp_IN.dat',
   'DATA'),
  ('babel\\locale-data\\sl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sl.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('babel\\locale-data\\kn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kn.dat',
   'DATA'),
  ('babel\\locale-data\\bm_Nkoo_ML.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bm_Nkoo_ML.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('babel\\locale-data\\mgh_MZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mgh_MZ.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('babel\\locale-data\\yue_Hant.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yue_Hant.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('babel\\locale-data\\sat.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sat.dat',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('babel\\locale-data\\en_MO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MO.dat',
   'DATA'),
  ('babel\\locale-data\\tzm_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tzm_MA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('babel\\locale-data\\io_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\io_001.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('babel\\locale-data\\dyo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dyo.dat',
   'DATA'),
  ('babel\\locale-data\\en_SX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SX.dat',
   'DATA'),
  ('babel\\locale-data\\kam.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kam.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('babel\\locale-data\\ks_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ks_Arab.dat',
   'DATA'),
  ('babel\\locale-data\\bo_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bo_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('babel\\locale-data\\bn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bn.dat',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('babel\\locale-data\\fo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fo.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('babel\\locale-data\\pa_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pa_Arab.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('babel\\locale-data\\ha_Arab_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_Arab_NG.dat',
   'DATA'),
  ('babel\\locale-data\\en_AE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AE.dat',
   'DATA'),
  ('babel\\locale-data\\ps_AF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ps_AF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('babel\\locale-data\\yo_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yo_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('babel\\locale-data\\cho_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cho_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('babel\\locale-data\\luy_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\luy_KE.dat',
   'DATA'),
  ('babel\\locale-data\\sv_AX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sv_AX.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('babel\\locale-data\\sw_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sw_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\ss_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ss_ZA.dat',
   'DATA'),
  ('babel\\locale-data\\en_Dsrt_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_Dsrt_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('babel\\locale-data\\en_MS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MS.dat',
   'DATA'),
  ('babel\\locale-data\\iu_CA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\iu_CA.dat',
   'DATA'),
  ('babel\\locale-data\\naq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\naq.dat',
   'DATA'),
  ('babel\\locale-data\\ja_JP.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ja_JP.dat',
   'DATA'),
  ('babel\\locale-data\\om_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\om_ET.dat',
   'DATA'),
  ('babel\\locale-data\\en_MY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MY.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('babel\\locale-data\\wae.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wae.dat',
   'DATA'),
  ('babel\\locale-data\\kpe.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kpe.dat',
   'DATA'),
  ('babel\\locale-data\\ar_KW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_KW.dat',
   'DATA'),
  ('babel\\locale-data\\ar_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_MA.dat',
   'DATA'),
  ('babel\\locale-data\\uz.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz.dat',
   'DATA'),
  ('babel\\locale-data\\ks_Deva.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ks_Deva.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('babel\\locale-data\\ken_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ken_CM.dat',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_GH.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('babel\\locale-data\\ar_AE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_AE.dat',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('babel\\locale-data\\ebu_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ebu_KE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('babel\\locale-data\\ne_NP.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ne_NP.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('babel\\locale-data\\mfe_MU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mfe_MU.dat',
   'DATA'),
  ('babel\\locale-data\\ms_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_Arab.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('babel\\locale-data\\en_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_FI.dat',
   'DATA'),
  ('babel\\locale-data\\sbp.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sbp.dat',
   'DATA'),
  ('babel\\locale-data\\kgp.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kgp.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('babel\\locale-data\\ab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ab.dat',
   'DATA'),
  ('babel\\locale-data\\mr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mr.dat',
   'DATA'),
  ('babel\\locale-data\\scn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\scn.dat',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('babel\\locale-data\\nd_ZW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nd_ZW.dat',
   'DATA'),
  ('babel\\locale-data\\en_TC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TC.dat',
   'DATA'),
  ('babel\\locale-data\\chr_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\chr_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('babel\\locale-data\\en_ZW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_ZW.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('babel\\locale-data\\ar_BH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_BH.dat',
   'DATA'),
  ('babel\\locale-data\\frr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\frr.dat',
   'DATA'),
  ('babel\\locale-data\\ce_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ce_RU.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('babel\\locale-data\\vec_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vec_IT.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('babel\\locale-data\\el.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\el.dat',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('babel\\locale-data\\apc_SY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\apc_SY.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('babel\\locale-data\\kcg_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kcg_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('babel\\locale-data\\nso.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nso.dat',
   'DATA'),
  ('babel\\locale-data\\fr_LU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_LU.dat',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('babel\\locale-data\\ce.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ce.dat',
   'DATA'),
  ('babel\\locale-data\\en_VC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_VC.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('babel\\global.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\global.dat',
   'DATA'),
  ('babel\\locale-data\\pa_Guru.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pa_Guru.dat',
   'DATA'),
  ('babel\\locale-data\\ha_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_GH.dat',
   'DATA'),
  ('babel\\locale-data\\en_VG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_VG.dat',
   'DATA'),
  ('babel\\locale-data\\fr_BE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_BE.dat',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('babel\\locale-data\\fr_GN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_GN.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('babel\\locale-data\\ga.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ga.dat',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('babel\\locale-data\\my.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\my.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('babel\\locale-data\\en_Dsrt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_Dsrt.dat',
   'DATA'),
  ('babel\\locale-data\\ny.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ny.dat',
   'DATA'),
  ('babel\\locale-data\\pap_CW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pap_CW.dat',
   'DATA'),
  ('babel\\locale-data\\pt_CV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_CV.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('babel\\locale-data\\jbo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jbo.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('babel\\locale-data\\ar_IL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_IL.dat',
   'DATA'),
  ('babel\\locale-data\\aa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\aa.dat',
   'DATA'),
  ('babel\\locale-data\\zgh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zgh.dat',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('babel\\locale-data\\fr_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_NE.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('babel\\locale-data\\sg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sg.dat',
   'DATA'),
  ('babel\\locale-data\\rm_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rm_CH.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('babel\\locale-data\\dua_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dua_CM.dat',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('babel\\locale-data\\se.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\se.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('babel\\locale-data\\zh_Hans_HK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hans_HK.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('babel\\locale-data\\ha_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_Arab.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('babel\\locale-data\\fr_RW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_RW.dat',
   'DATA'),
  ('babel\\locale-data\\lv_LV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lv_LV.dat',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('babel\\locale-data\\en_CC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CC.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('babel\\locale-data\\sc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sc.dat',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('babel\\locale-data\\hi_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hi_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\en_SB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SB.dat',
   'DATA'),
  ('babel\\locale-data\\en_AT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AT.dat',
   'DATA'),
  ('babel\\locale-data\\yi_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yi_001.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('babel\\locale-data\\ff.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('babel\\locale-data\\smn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\smn.dat',
   'DATA'),
  ('babel\\locale-data\\myv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\myv.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('babel\\locale-data\\tig_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tig_ER.dat',
   'DATA'),
  ('babel\\locale-data\\el_CY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\el_CY.dat',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_SN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_SN.dat',
   'DATA'),
  ('babel\\locale-data\\ca_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca_FR.dat',
   'DATA'),
  ('babel\\locale-data\\bg_BG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bg_BG.dat',
   'DATA'),
  ('babel\\locale-data\\luo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\luo.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('babel\\locale-data\\dav.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dav.dat',
   'DATA'),
  ('babel\\locale-data\\fr_DJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_DJ.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('babel\\locale-data\\fa_AF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fa_AF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('babel\\locale-data\\fy_NL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fy_NL.dat',
   'DATA'),
  ('babel\\locale-data\\fr_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_SL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_SL.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('babel\\locale-data\\cgg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cgg.dat',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('babel\\locale-data\\bgn_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn_IR.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('babel\\locale-data\\my_MM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\my_MM.dat',
   'DATA'),
  ('babel\\locale-data\\syr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\syr.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('babel\\locale-data\\ti_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ti_ET.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('babel\\locale-data\\gl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gl.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('babel\\locale-data\\tg_TJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tg_TJ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('babel\\locale-data\\saq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\saq.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('babel\\locale-data\\an.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\an.dat',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('babel\\locale-data\\myv_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\myv_RU.dat',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('babel\\locale-data\\wal.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wal.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('babel\\locale-data\\jmc_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jmc_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\mn_MN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mn_MN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('babel\\locale-data\\prg_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\prg_001.dat',
   'DATA'),
  ('babel\\locale-data\\fr_CI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CI.dat',
   'DATA'),
  ('babel\\locale-data\\ar.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar.dat',
   'DATA'),
  ('babel\\locale-data\\he_IL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\he_IL.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_LR.dat',
   'DATA'),
  ('babel\\locale-data\\fa_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fa_IR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('babel\\locale-data\\szl_PL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\szl_PL.dat',
   'DATA'),
  ('babel\\locale-data\\fr_KM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_KM.dat',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('babel\\locale-data\\to.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\to.dat',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('babel\\locale-data\\hr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hr.dat',
   'DATA'),
  ('babel\\locale-data\\ki_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ki_KE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('babel\\locale-data\\fa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fa.dat',
   'DATA'),
  ('babel\\locale-data\\fr_YT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_YT.dat',
   'DATA'),
  ('babel\\locale-data\\trw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\trw.dat',
   'DATA'),
  ('babel\\locale-data\\lo_LA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lo_LA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('babel\\locale-data\\nqo_GN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nqo_GN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('babel\\locale-data\\ckb_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ckb_IQ.dat',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('babel\\locale-data\\en_FM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_FM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tm.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('babel\\locale-data\\fr_ML.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_ML.dat',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('babel\\locale-data\\gd.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gd.dat',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('babel\\locale-data\\nl_BQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_BQ.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_GN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_GN.dat',
   'DATA'),
  ('babel\\locale-data\\su.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\su.dat',
   'DATA'),
  ('babel\\locale-data\\ssy_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ssy_ER.dat',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('babel\\locale-data\\ur_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ur_IN.dat',
   'DATA'),
  ('babel\\locale-data\\to_TO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\to_TO.dat',
   'DATA'),
  ('babel\\locale-data\\sr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('babel\\locale-data\\en_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_ZA.dat',
   'DATA'),
  ('babel\\locale-data\\ta_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ta_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('babel\\locale-data\\ar_PS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_PS.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('babel\\locale-data\\nr_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nr_ZA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('babel\\locale-data\\sw_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sw_KE.dat',
   'DATA'),
  ('babel\\locale-data\\nds_NL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nds_NL.dat',
   'DATA'),
  ('babel\\locale-data\\te.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\te.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_GM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_GM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('babel\\locale-data\\fr_GA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_GA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('babel\\locale-data\\kw_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kw_GB.dat',
   'DATA'),
  ('babel\\locale-data\\kkj_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kkj_CM.dat',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('babel\\locale-data\\kkj.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kkj.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('babel\\locale-data\\sc_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sc_IT.dat',
   'DATA'),
  ('babel\\locale-data\\wa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wa.dat',
   'DATA'),
  ('babel\\locale-data\\en_TO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_MR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_MR.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('babel\\locale-data\\af_NA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\af_NA.dat',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_NE.dat',
   'DATA'),
  ('babel\\locale-data\\en_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_IN.dat',
   'DATA'),
  ('babel\\locale-data\\nmg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nmg.dat',
   'DATA'),
  ('babel\\locale-data\\mzn_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mzn_IR.dat',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('babel\\locale-data\\nv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nv.dat',
   'DATA'),
  ('babel\\locale-data\\kok.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kok.dat',
   'DATA'),
  ('babel\\locale-data\\bg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bg.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('babel\\locale-data\\ksf_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksf_CM.dat',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('babel\\locale-data\\os_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\os_RU.dat',
   'DATA'),
  ('babel\\locale-data\\en_TK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TK.dat',
   'DATA'),
  ('babel\\locale-data\\kab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kab.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('babel\\locale-data\\fr_MG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('babel\\locale-data\\mni_Mtei.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mni_Mtei.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('babel\\locale-data\\rwk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rwk.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('babel\\locale-data\\ti_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ti_ER.dat',
   'DATA'),
  ('babel\\locale-data\\ewo_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ewo_CM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('babel\\locale-data\\ar_LB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_LB.dat',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('babel\\locale-data\\tr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tr.dat',
   'DATA'),
  ('babel\\locale-data\\vo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vo.dat',
   'DATA'),
  ('babel\\locale-data\\en_AG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AG.dat',
   'DATA'),
  ('babel\\locale-data\\st.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\st.dat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('babel\\locale-data\\doi_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\doi_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('babel\\locale-data\\sma_NO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sma_NO.dat',
   'DATA'),
  ('babel\\locale-data\\ne.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ne.dat',
   'DATA'),
  ('babel\\locale-data\\fr_GP.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_GP.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('babel\\locale-data\\it_VA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\it_VA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('babel\\locale-data\\shi_Tfng_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shi_Tfng_MA.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('babel\\locale-data\\tt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tt.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('babel\\locale-data\\dsb_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dsb_DE.dat',
   'DATA'),
  ('babel\\locale-data\\ro_MD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ro_MD.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('babel\\locale-data\\en_GI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('babel\\locale-data\\ro.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ro.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('babel\\locale-data\\brx.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\brx.dat',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('babel\\locale-data\\ks.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ks.dat',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('babel\\locale-data\\sk_SK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sk_SK.dat',
   'DATA'),
  ('babel\\locale-data\\az_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Arab.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('babel\\locale-data\\sr_Latn_RS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Latn_RS.dat',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('babel\\locale-data\\kcg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kcg.dat',
   'DATA'),
  ('babel\\locale-data\\kln.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kln.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('babel\\locale-data\\en.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('babel\\locale-data\\bal_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bal_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\mni.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mni.dat',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('babel\\locale-data\\es_MX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_MX.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('babel\\locale-data\\mas_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mas_KE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('babel\\locale-data\\ms_ID.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_ID.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('babel\\locale-data\\en_SC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SC.dat',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('babel\\locale-data\\ann.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ann.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('babel\\locale-data\\agq_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\agq_CM.dat',
   'DATA'),
  ('babel\\locale-data\\mgo_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mgo_CM.dat',
   'DATA'),
  ('babel\\locale-data\\ses_ML.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ses_ML.dat',
   'DATA'),
  ('babel\\locale-data\\ar_SO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_SO.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hant_TW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hant_TW.dat',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('babel\\locale-data\\shi_Tfng.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shi_Tfng.dat',
   'DATA'),
  ('babel\\locale-data\\bgc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgc.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('babel\\locale-data\\ar_DJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_DJ.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('babel\\locale-data\\cho.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cho.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('babel\\locale-data\\en_IL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_IL.dat',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('babel\\locale-data\\es_BR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_BR.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('babel\\locale-data\\jgo_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jgo_CM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('babel\\locale-data\\mai.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mai.dat',
   'DATA'),
  ('babel\\locale-data\\fr_MQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MQ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('babel\\locale-data\\en_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_KE.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('babel\\locale-data\\guz_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\guz_KE.dat',
   'DATA'),
  ('babel\\locale-data\\fr_VU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_VU.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('babel\\locale-data\\en_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\chr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\chr.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('babel\\locale-data\\iu_Latn_CA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\iu_Latn_CA.dat',
   'DATA'),
  ('babel\\locale-data\\gv_IM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gv_IM.dat',
   'DATA'),
  ('babel\\locale-data\\ml_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ml_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('babel\\locale-data\\kab_DZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kab_DZ.dat',
   'DATA'),
  ('babel\\locale-data\\aa_DJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\aa_DJ.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('babel\\locale-data\\bal_Arab_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bal_Arab_PK.dat',
   'DATA'),
  ('babel\\locale-data\\it_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\it_CH.dat',
   'DATA'),
  ('babel\\locale-data\\nl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('babel\\locale-data\\tr_TR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tr_TR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('babel\\locale-data\\ks_Deva_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ks_Deva_IN.dat',
   'DATA'),
  ('babel\\locale-data\\yav.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yav.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('babel\\locale-data\\sd_Arab_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sd_Arab_PK.dat',
   'DATA'),
  ('babel\\locale-data\\bn_BD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bn_BD.dat',
   'DATA'),
  ('babel\\locale-data\\en_GU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GU.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('babel\\locale-data\\sq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sq.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('babel\\locale-data\\ar_SD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_SD.dat',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('babel\\locale-data\\uz_Cyrl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Cyrl.dat',
   'DATA'),
  ('babel\\locale-data\\ve_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ve_ZA.dat',
   'DATA'),
  ('babel\\locale-data\\sd_Deva.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sd_Deva.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('babel\\locale-data\\fr_MF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MF.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('babel\\locale-data\\uz_Arab_AF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Arab_AF.dat',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('babel\\locale-data\\so_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\so_KE.dat',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('babel\\locale-data\\en_WS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_WS.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('babel\\locale-data\\lij_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lij_IT.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('babel\\locale-data\\hsb_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hsb_DE.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('babel\\locale-data\\lu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lu.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('babel\\locale-data\\hnj.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hnj.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('babel\\locale-data\\en_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_001.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('babel\\locale-data\\aa_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\aa_ET.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('babel\\locale-data\\fr_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_FR.dat',
   'DATA'),
  ('babel\\locale-data\\ksh_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksh_DE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('babel\\locale-data\\an_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\an_ES.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('babel\\locale-data\\en_Shaw_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_Shaw_GB.dat',
   'DATA'),
  ('babel\\locale-data\\az_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\mn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mn.dat',
   'DATA'),
  ('babel\\locale-data\\ksf.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksf.dat',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('babel\\locale-data\\hr_BA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hr_BA.dat',
   'DATA'),
  ('babel\\locale-data\\ar_TD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_TD.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('babel\\locale-data\\sr_Latn_ME.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Latn_ME.dat',
   'DATA'),
  ('babel\\locale-data\\gaa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gaa.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('babel\\locale-data\\rhg_Rohg_BD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rhg_Rohg_BD.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('babel\\locale-data\\nyn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nyn.dat',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('babel\\locale-data\\he.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\he.dat',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('babel\\locale-data\\eu_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\eu_ES.dat',
   'DATA'),
  ('babel\\locale-data\\ja.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ja.dat',
   'DATA'),
  ('babel\\locale-data\\th_TH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\th_TH.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('babel\\locale-data\\smj.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\smj.dat',
   'DATA'),
  ('babel\\locale-data\\en_FK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_FK.dat',
   'DATA'),
  ('babel\\locale-data\\sdh_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sdh_IQ.dat',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('babel\\locale-data\\mfe.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mfe.dat',
   'DATA'),
  ('babel\\locale-data\\pl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pl.dat',
   'DATA'),
  ('babel\\locale-data\\cy.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cy.dat',
   'DATA'),
  ('babel\\locale-data\\ar_TN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_TN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('babel\\locale-data\\en_MU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MU.dat',
   'DATA'),
  ('babel\\locale-data\\fr_PM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_PM.dat',
   'DATA'),
  ('babel\\locale-data\\tzm.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tzm.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('babel\\locale-data\\cad_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cad_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('babel\\locale-data\\yue.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yue.dat',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('babel\\locale-data\\az_Arab_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Arab_IQ.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('babel\\locale-data\\ln_CD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ln_CD.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('babel\\locale-data\\en_CK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CK.dat',
   'DATA'),
  ('babel\\locale-data\\mni_Beng_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mni_Beng_IN.dat',
   'DATA'),
  ('babel\\locale-data\\af_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\af_ZA.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('babel\\locale-data\\pa_Guru_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pa_Guru_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('babel\\locale-data\\ken.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ken.dat',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('babel\\locale-data\\zh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh.dat',
   'DATA'),
  ('babel\\locale-data\\en_RW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_RW.dat',
   'DATA'),
  ('babel\\locale-data\\kde_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kde_TZ.dat',
   'DATA'),
  ('babel\\py.typed',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\py.typed',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('babel\\locale-data\\sg_CF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sg_CF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('babel\\locale-data\\shn_TH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shn_TH.dat',
   'DATA'),
  ('babel\\locale-data\\cs_CZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cs_CZ.dat',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('babel\\locale-data\\gn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('babel\\locale-data\\jv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jv.dat',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('babel\\locale-data\\cic_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cic_US.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_MR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_MR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('babel\\locale-data\\vi_VN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vi_VN.dat',
   'DATA'),
  ('babel\\locale-data\\en_LC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_LC.dat',
   'DATA'),
  ('babel\\locale-data\\vai_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vai_Latn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('babel\\locale-data\\ca_ES_VALENCIA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca_ES_VALENCIA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_GW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_GW.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('babel\\locale-data\\bm_ML.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bm_ML.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('babel\\locale-data\\cy_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cy_GB.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('babel\\locale-data\\io.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\io.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('babel\\locale-data\\fr_SN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_SN.dat',
   'DATA'),
  ('babel\\locale-data\\cu_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cu_RU.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hans.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hans.dat',
   'DATA'),
  ('babel\\locale-data\\en_VI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_VI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('babel\\locale-data\\rw_RW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rw_RW.dat',
   'DATA'),
  ('babel\\locale-data\\sma_SE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sma_SE.dat',
   'DATA'),
  ('babel\\locale-data\\pt_ST.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_ST.dat',
   'DATA'),
  ('babel\\locale-data\\mer_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mer_KE.dat',
   'DATA'),
  ('babel\\locale-data\\ak.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ak.dat',
   'DATA'),
  ('babel\\locale-data\\en_PN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PN.dat',
   'DATA'),
  ('babel\\locale-data\\sah_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sah_RU.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('babel\\locale-data\\en_CX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CX.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('babel\\locale-data\\ku_TR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ku_TR.dat',
   'DATA'),
  ('babel\\locale-data\\ca.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('babel\\locale-data\\bgn_OM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn_OM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('babel\\locale-data\\wae_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wae_CH.dat',
   'DATA'),
  ('babel\\locale-data\\arn_CL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\arn_CL.dat',
   'DATA'),
  ('babel\\locale-data\\fr_BJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_BJ.dat',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('babel\\locale-data\\el_GR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\el_GR.dat',
   'DATA'),
  ('babel\\locale-data\\it_SM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\it_SM.dat',
   'DATA'),
  ('babel\\locale-data\\trv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\trv.dat',
   'DATA'),
  ('babel\\locale-data\\ee_TG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ee_TG.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('babel\\locale-data\\nl_SX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_SX.dat',
   'DATA'),
  ('babel\\locale-data\\bs_Cyrl_BA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bs_Cyrl_BA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('babel\\locale-data\\or_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\or_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('babel\\locale-data\\en_SE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SE.dat',
   'DATA'),
  ('babel\\locale-data\\fr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('babel\\locale-data\\os_GE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\os_GE.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('babel\\locale-data\\kl_GL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kl_GL.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('babel\\locale-data\\ne_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ne_IN.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_LR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('babel\\locale-data\\pcm.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pcm.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('babel\\locale-data\\ar_OM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_OM.dat',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('babel\\locale-data\\oc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\oc.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('babel\\locale-data\\iu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\iu.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('babel\\locale-data\\lrc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lrc.dat',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('babel\\locale-data\\bho.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bho.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('babel\\locale-data\\en_SI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SI.dat',
   'DATA'),
  ('babel\\locale-data\\en_JE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_JE.dat',
   'DATA'),
  ('babel\\locale-data\\yo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yo.dat',
   'DATA'),
  ('babel\\locale-data\\en_BB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BB.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('babel\\locale-data\\ar_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_001.dat',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('babel\\locale-data\\jbo_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jbo_001.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('babel\\locale-data\\kl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kl.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('babel\\locale-data\\cgg_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cgg_UG.dat',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('babel\\locale-data\\rhg_Rohg_MM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rhg_Rohg_MM.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('babel\\locale-data\\lg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lg.dat',
   'DATA'),
  ('babel\\locale-data\\fr_DZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_DZ.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('babel\\locale-data\\qu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\qu.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('babel\\locale-data\\en_AS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AS.dat',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('babel\\locale-data\\en_SZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SZ.dat',
   'DATA'),
  ('babel\\locale-data\\ab_GE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ab_GE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('babel\\locale-data\\es_SV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_SV.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('babel\\locale-data\\gaa_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gaa_GH.dat',
   'DATA'),
  ('babel\\locale-data\\nv_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nv_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('babel\\locale-data\\tk_TM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tk_TM.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('babel\\locale-data\\en_BW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BW.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('babel\\locale-data\\vai_Vaii.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vai_Vaii.dat',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('babel\\locale-data\\hu_HU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hu_HU.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('babel\\locale-data\\nnh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nnh.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('babel\\locale-data\\rof_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rof_TZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('babel\\locale-data\\ms_Arab_BN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_Arab_BN.dat',
   'DATA'),
  ('babel\\locale-data\\en_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_ER.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('babel\\locale-data\\mg_MG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mg_MG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('babel\\locale-data\\scn_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\scn_IT.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('babel\\locale-data\\fr_TD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_TD.dat',
   'DATA'),
  ('babel\\locale-data\\nl_AW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_AW.dat',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('babel\\locale-data\\es_VE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_VE.dat',
   'DATA'),
  ('babel\\locale-data\\bo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bo.dat',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('babel\\locale-data\\gu_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gu_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('babel\\locale-data\\ccp.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ccp.dat',
   'DATA'),
  ('babel\\locale-data\\qu_BO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\qu_BO.dat',
   'DATA'),
  ('babel\\locale-data\\pt_MO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_MO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('babel\\locale-data\\ses.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ses.dat',
   'DATA'),
  ('babel\\locale-data\\en_US_POSIX.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_US_POSIX.dat',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('babel\\locale-data\\pa_Arab_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pa_Arab_PK.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('babel\\locale-data\\ar_YE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_YE.dat',
   'DATA'),
  ('babel\\locale-data\\wo_SN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wo_SN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('babel\\locale-data\\ss.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ss.dat',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('babel\\locale-data\\twq_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\twq_NE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('babel\\locale-data\\eo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\eo.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('babel\\locale-data\\shn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shn.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_CM.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('babel\\locale-data\\ml.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ml.dat',
   'DATA'),
  ('babel\\locale-data\\en_VU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_VU.dat',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('babel\\locale-data\\haw_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\haw_US.dat',
   'DATA'),
  ('babel\\locale-data\\mk_MK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mk_MK.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('babel\\locale-data\\smj_SE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\smj_SE.dat',
   'DATA'),
  ('babel\\locale-data\\sr_Latn_BA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Latn_BA.dat',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('babel\\locale-data\\ln_CF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ln_CF.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('babel\\locale-data\\es_NI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_NI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('babel\\locale-data\\ast.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ast.dat',
   'DATA'),
  ('babel\\locale-data\\ga_IE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ga_IE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('babel\\locale-data\\en_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PK.dat',
   'DATA'),
  ('babel\\locale-data\\hy_AM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hy_AM.dat',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('babel\\locale-data\\pa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pa.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('babel\\locale-data\\vai_Latn_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vai_Latn_LR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('babel\\locale-data\\as_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\as_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('babel\\locale-data\\uz_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Latn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('babel\\locale-data\\qu_EC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\qu_EC.dat',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('babel\\locale-data\\ms_BN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_BN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('babel\\locale-data\\sa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sa.dat',
   'DATA'),
  ('babel\\locale-data\\fy.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fy.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hant.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hant.dat',
   'DATA'),
  ('babel\\locale-data\\sw_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sw_UG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('babel\\locale-data\\be_TARASK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\be_TARASK.dat',
   'DATA'),
  ('babel\\locale-data\\nyn_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nyn_UG.dat',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('babel\\locale-data\\la_VA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\la_VA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('babel\\locale-data\\no.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\no.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('babel\\locale-data\\km.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\km.dat',
   'DATA'),
  ('babel\\locale-data\\lmo_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lmo_IT.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('babel\\locale-data\\da_DK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\da_DK.dat',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('babel\\locale-data\\pt_LU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_LU.dat',
   'DATA'),
  ('babel\\locale-data\\pis_SB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pis_SB.dat',
   'DATA'),
  ('babel\\locale-data\\lb_LU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lb_LU.dat',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('babel\\locale-data\\nb_SJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nb_SJ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('babel\\locale-data\\oc_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\oc_FR.dat',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('babel\\locale-data\\doi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\doi.dat',
   'DATA'),
  ('babel\\locale-data\\hu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hu.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('babel\\locale-data\\ro_RO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ro_RO.dat',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('babel\\locale-data\\tok_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tok_001.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('babel\\locale-data\\sr_Cyrl_RS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Cyrl_RS.dat',
   'DATA'),
  ('babel\\locale-data\\es_CO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_CO.dat',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('babel\\locale-data\\sat_Olck_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sat_Olck_IN.dat',
   'DATA'),
  ('babel\\locale-data\\ug.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ug.dat',
   'DATA'),
  ('babel\\locale-data\\teo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\teo.dat',
   'DATA'),
  ('babel\\locale-data\\es_CL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_CL.dat',
   'DATA'),
  ('babel\\locale-data\\ss_SZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ss_SZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('babel\\locale-data\\so.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\so.dat',
   'DATA'),
  ('babel\\locale-data\\wbp.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wbp.dat',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('babel\\locale-data\\ar_SA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_SA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('babel\\locale-data\\de_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_IT.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('babel\\locale-data\\es_PE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_PE.dat',
   'DATA'),
  ('babel\\locale-data\\mer.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mer.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('babel\\locale-data\\gu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gu.dat',
   'DATA'),
  ('babel\\locale-data\\ar_QA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_QA.dat',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('babel\\locale-data\\kn_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kn_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('babel\\locale-data\\es_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_ES.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('babel\\locale-data\\ta_LK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ta_LK.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('babel\\locale-data\\tpi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tpi.dat',
   'DATA'),
  ('babel\\locale-data\\az.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('babel\\locale-data\\ms_Arab_MY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_Arab_MY.dat',
   'DATA'),
  ('babel\\locale-data\\es_PY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_PY.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('babel\\locale-data\\az_Arab_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Arab_IR.dat',
   'DATA'),
  ('babel\\locale-data\\bem_ZM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bem_ZM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('babel\\locale-data\\es_BO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_BO.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('babel\\locale-data\\gez_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gez_ET.dat',
   'DATA'),
  ('babel\\locale-data\\tok.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tok.dat',
   'DATA'),
  ('babel\\locale-data\\eu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\eu.dat',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('babel\\locale-data\\en_DG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_DG.dat',
   'DATA'),
  ('babel\\locale-data\\ba_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ba_RU.dat',
   'DATA'),
  ('babel\\locale-data\\si.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\si.dat',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('babel\\locale-data\\su_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\su_Latn.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('babel\\locale-data\\nd.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nd.dat',
   'DATA'),
  ('babel\\locale-data\\ar_JO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_JO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('babel\\locale-data\\xog.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\xog.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('babel\\locale-data\\bss_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bss_CM.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('babel\\locale-data\\en_AI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('babel\\locale-data\\en_NR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('babel\\locale-data\\pap.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pap.dat',
   'DATA'),
  ('babel\\locale-data\\bgn_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn_PK.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('babel\\locale-data\\sdh_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sdh_IR.dat',
   'DATA'),
  ('babel\\locale-data\\sv_SE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sv_SE.dat',
   'DATA'),
  ('babel\\locale-data\\brx_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\brx_IN.dat',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('babel\\locale-data\\ur.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ur.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('babel\\locale-data\\fr_CG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CG.dat',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('babel\\locale-data\\fo_FO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fo_FO.dat',
   'DATA'),
  ('babel\\locale-data\\pt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('babel\\locale-data\\bal_Latn_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bal_Latn_PK.dat',
   'DATA'),
  ('babel\\locale-data\\ps_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ps_PK.dat',
   'DATA'),
  ('babel\\locale-data\\en_Shaw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_Shaw.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('babel\\locale-data\\nnh_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nnh_CM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('babel\\locale-data\\aa_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\aa_ER.dat',
   'DATA'),
  ('babel\\locale-data\\ts_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ts_ZA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('babel\\locale-data\\bez.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bez.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_BF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_BF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('babel\\locale-data\\teo_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\teo_UG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('babel\\locale-data\\byn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\byn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('babel\\locale-data\\en_KI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_KI.dat',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('babel\\locale-data\\am_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\am_ET.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('babel\\locale-data\\vun.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vun.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('babel\\locale-data\\sd_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sd_Arab.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('babel\\locale-data\\smn_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\smn_FI.dat',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('babel\\locale-data\\hr_HR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hr_HR.dat',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('babel\\locale-data\\et.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\et.dat',
   'DATA'),
  ('babel\\locale-data\\ps.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ps.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('babel\\locale-data\\fr_RE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_RE.dat',
   'DATA'),
  ('babel\\locale-data\\en_SS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SS.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('babel\\locale-data\\sv_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sv_FI.dat',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('babel\\locale-data\\en_PH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PH.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hans_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hans_CN.dat',
   'DATA'),
  ('babel\\locale-data\\rn_BI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rn_BI.dat',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('babel\\locale-data\\ky.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ky.dat',
   'DATA'),
  ('babel\\locale-data\\kaj.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kaj.dat',
   'DATA'),
  ('babel\\locale-data\\en_CA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CA.dat',
   'DATA'),
  ('babel\\locale-data\\gsw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gsw.dat',
   'DATA'),
  ('babel\\locale-data\\ewo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ewo.dat',
   'DATA'),
  ('babel\\locale-data\\ha_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_NG.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\en_MW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MW.dat',
   'DATA'),
  ('babel\\locale-data\\si_LK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\si_LK.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('babel\\locale-data\\prg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\prg.dat',
   'DATA'),
  ('babel\\locale-data\\is_IS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\is_IS.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hant_HK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hant_HK.dat',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('babel\\locale-data\\bs.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bs.dat',
   'DATA'),
  ('babel\\locale-data\\raj_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\raj_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('babel\\locale-data\\twq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\twq.dat',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('babel\\locale-data\\bal.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bal.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('babel\\locale-data\\ast_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ast_ES.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('babel\\locale-data\\en_ZM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_ZM.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('babel\\locale-data\\gd_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gd_GB.dat',
   'DATA'),
  ('babel\\locale-data\\mt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mt.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('babel\\locale-data\\jmc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jmc.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('babel\\locale-data\\ru_MD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_MD.dat',
   'DATA'),
  ('babel\\locale-data\\pap_AW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pap_AW.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('babel\\locale-data\\rof.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rof.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('babel\\locale-data\\fr_HT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_HT.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('babel\\locale-data\\uk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uk.dat',
   'DATA'),
  ('babel\\locale-data\\mus_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mus_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('babel\\locale-data\\ka.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ka.dat',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('babel\\locale-data\\en_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_UG.dat',
   'DATA'),
  ('babel\\locale-data\\nus.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nus.dat',
   'DATA'),
  ('babel\\locale-data\\khq_ML.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\khq_ML.dat',
   'DATA'),
  ('babel\\locale-data\\yav_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yav_CM.dat',
   'DATA'),
  ('babel\\locale-data\\sma.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sma.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('babel\\locale-data\\mr_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mr_IN.dat',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('babel\\locale-data\\sid.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sid.dat',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('babel\\locale-data\\de_LI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_LI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('babel\\locale-data\\ko_KR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ko_KR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('babel\\locale-data\\ug_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ug_CN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('babel\\locale-data\\en_MT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MT.dat',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('babel\\locale-data\\es_CU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_CU.dat',
   'DATA'),
  ('babel\\locale-data\\tr_CY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tr_CY.dat',
   'DATA'),
  ('babel\\locale-data\\ru_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_RU.dat',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('babel\\locale-data\\gn_PY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gn_PY.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('babel\\locale-data\\de_LU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_LU.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_BF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_BF.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('babel\\locale-data\\ar_SY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_SY.dat',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('babel\\locale-data\\co_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\co_FR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('babel\\locale-data\\ia.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ia.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('babel\\locale-data\\en_AU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_AU.dat',
   'DATA'),
  ('babel\\locale-data\\moh_CA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\moh_CA.dat',
   'DATA'),
  ('babel\\locale-data\\en_SH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SH.dat',
   'DATA'),
  ('babel\\locale-data\\en_MP.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MP.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('babel\\locale-data\\rm.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rm.dat',
   'DATA'),
  ('babel\\locale-data\\ha.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha.dat',
   'DATA'),
  ('babel\\locale-data\\es_PH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_PH.dat',
   'DATA'),
  ('babel\\locale-data\\smj_NO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\smj_NO.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('babel\\locale-data\\ak_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ak_GH.dat',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('babel\\locale-data\\lo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lo.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('babel\\locale-data\\dje.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dje.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('babel\\locale-data\\bas.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bas.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('babel\\locale-data\\en_NA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NA.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('babel\\locale-data\\hi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hi.dat',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('babel\\locale-data\\en_MH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MH.dat',
   'DATA'),
  ('babel\\locale-data\\ta_MY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ta_MY.dat',
   'DATA'),
  ('babel\\locale-data\\sv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sv.dat',
   'DATA'),
  ('babel\\locale-data\\ksb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksb.dat',
   'DATA'),
  ('babel\\locale-data\\ta.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ta.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('babel\\locale-data\\cs.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cs.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('babel\\locale-data\\yi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yi.dat',
   'DATA'),
  ('babel\\locale-data\\es_HN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_HN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('babel\\locale-data\\rhg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rhg.dat',
   'DATA'),
  ('babel\\locale-data\\fur_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fur_IT.dat',
   'DATA'),
  ('babel\\locale-data\\en_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CM.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('babel\\locale-data\\yrl_BR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yrl_BR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('babel\\locale-data\\sw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sw.dat',
   'DATA'),
  ('babel\\locale-data\\mn_Mong.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mn_Mong.dat',
   'DATA'),
  ('babel\\locale-data\\guz.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\guz.dat',
   'DATA'),
  ('babel\\locale-data\\uk_UA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uk_UA.dat',
   'DATA'),
  ('babel\\locale-data\\jgo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jgo.dat',
   'DATA'),
  ('babel\\locale-data\\ceb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ceb.dat',
   'DATA'),
  ('babel\\locale-data\\ks_Arab_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ks_Arab_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_GW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_GW.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('babel\\locale-data\\mgh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mgh.dat',
   'DATA'),
  ('babel\\locale-data\\ksh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksh.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('babel\\locale-data\\es_PR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_PR.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('babel\\locale-data\\en_BI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('babel\\locale-data\\fil_PH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fil_PH.dat',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('babel\\locale-data\\cic.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cic.dat',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('babel\\locale-data\\ms_MY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_MY.dat',
   'DATA'),
  ('babel\\locale-data\\fil.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fil.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('babel\\locale-data\\shi_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shi_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\es_PA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_PA.dat',
   'DATA'),
  ('babel\\locale-data\\so_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\so_ET.dat',
   'DATA'),
  ('babel\\locale-data\\rhg_Rohg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rhg_Rohg.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('babel\\locale-data\\es_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_US.dat',
   'DATA'),
  ('babel\\locale-data\\en_BZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('babel\\locale-data\\lkt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lkt.dat',
   'DATA'),
  ('babel\\locale-data\\da.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\da.dat',
   'DATA'),
  ('babel\\locale-data\\lb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lb.dat',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_GN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_GN.dat',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('babel\\locale-data\\bgn_AF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn_AF.dat',
   'DATA'),
  ('babel\\locale-data\\mdf_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mdf_RU.dat',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('babel\\locale-data\\am.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\am.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('babel\\locale-data\\nl_CW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_CW.dat',
   'DATA'),
  ('babel\\locale-data\\sr_Cyrl_ME.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Cyrl_ME.dat',
   'DATA'),
  ('babel\\locale-data\\ebu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ebu.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('babel\\locale-data\\th.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\th.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('babel\\locale-data\\dz.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dz.dat',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('babel\\locale-data\\co.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\co.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('babel\\locale-data\\ha_Arab_SD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_Arab_SD.dat',
   'DATA'),
  ('babel\\locale-data\\bem.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bem.dat',
   'DATA'),
  ('babel\\locale-data\\uz_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Arab.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('babel\\locale-data\\raj.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\raj.dat',
   'DATA'),
  ('babel\\locale-data\\kgp_BR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kgp_BR.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('babel\\locale-data\\en_SL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SL.dat',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('babel\\locale-data\\sa_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sa_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('babel\\locale-data\\mn_Mong_MN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mn_Mong_MN.dat',
   'DATA'),
  ('babel\\locale-data\\eo_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\eo_001.dat',
   'DATA'),
  ('babel\\locale-data\\kok_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kok_IN.dat',
   'DATA'),
  ('babel\\locale-data\\rw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rw.dat',
   'DATA'),
  ('babel\\locale-data\\en_UM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_UM.dat',
   'DATA'),
  ('babel\\locale-data\\lg_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lg_UG.dat',
   'DATA'),
  ('babel\\locale-data\\gsw_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gsw_CH.dat',
   'DATA'),
  ('babel\\locale-data\\xh_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\xh_ZA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('babel\\locale-data\\su_Latn_ID.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\su_Latn_ID.dat',
   'DATA'),
  ('babel\\locale-data\\it_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\it_IT.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('babel\\locale-data\\ln_AO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ln_AO.dat',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('babel\\locale-data\\dz_BT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dz_BT.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('babel\\locale-data\\en_GG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GG.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('babel\\locale-data\\lag_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lag_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\cu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cu.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('babel\\locale-data\\tig.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tig.dat',
   'DATA'),
  ('babel\\locale-data\\ru_KG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_KG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('babel\\locale-data\\teo_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\teo_KE.dat',
   'DATA'),
  ('babel\\locale-data\\ms.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('babel\\locale-data\\vo_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vo_001.dat',
   'DATA'),
  ('babel\\locale-data\\dv_MV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dv_MV.dat',
   'DATA'),
  ('babel\\locale-data\\ia_001.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ia_001.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('babel\\locale-data\\vun_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vun_TZ.dat',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('babel\\locale-data\\ka_GE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ka_GE.dat',
   'DATA'),
  ('babel\\locale-data\\vai.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vai.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('babel\\locale-data\\root.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\root.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('babel\\locale-data\\ru_BY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_BY.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('babel\\locale-data\\en_BM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BM.dat',
   'DATA'),
  ('babel\\locale-data\\xog_UG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\xog_UG.dat',
   'DATA'),
  ('babel\\locale-data\\yue_Hans_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yue_Hans_CN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('babel\\locale-data\\st_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\st_ZA.dat',
   'DATA'),
  ('babel\\locale-data\\fr_GQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_GQ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('babel\\locale-data\\fr_BI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_BI.dat',
   'DATA'),
  ('babel\\locale-data\\tpi_PG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tpi_PG.dat',
   'DATA'),
  ('babel\\locale-data\\mai_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mai_IN.dat',
   'DATA'),
  ('babel\\locale-data\\en_JM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_JM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('babel\\locale-data\\khq.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\khq.dat',
   'DATA'),
  ('babel\\locale-data\\ii.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ii.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('babel\\locale-data\\nso_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nso_ZA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('babel\\locale-data\\mk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mk.dat',
   'DATA'),
  ('babel\\locale-data\\mg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mg.dat',
   'DATA'),
  ('babel\\locale-data\\bs_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bs_Latn.dat',
   'DATA'),
  ('babel\\locale-data\\sq_XK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sq_XK.dat',
   'DATA'),
  ('babel\\locale-data\\en_SD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SD.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('babel\\locale-data\\lv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lv.dat',
   'DATA'),
  ('babel\\locale-data\\tn_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tn_ZA.dat',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('babel\\locale-data\\vec.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vec.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('babel\\locale-data\\fr_CD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CD.dat',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('babel\\locale-data\\ki.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ki.dat',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('babel\\locale-data\\pis.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pis.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('babel\\locale-data\\en_NU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NU.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('babel\\locale-data\\nus_SS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nus_SS.dat',
   'DATA'),
  ('babel\\locale-data\\kk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kk.dat',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('babel\\locale-data\\syr_SY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\syr_SY.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('babel\\locale-data\\dje_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dje_NE.dat',
   'DATA'),
  ('babel\\locale-data\\ca_IT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca_IT.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('babel\\locale-data\\gsw_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gsw_FR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('babel\\locale-data\\seh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\seh.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('babel\\locale-data\\gsw_LI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gsw_LI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('babel\\locale-data\\dsb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dsb.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('babel\\locale-data\\tn_BW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tn_BW.dat',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('babel\\locale-data\\sms.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sms.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('babel\\locale-data\\lrc_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lrc_IQ.dat',
   'DATA'),
  ('babel\\locale-data\\ru_UA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_UA.dat',
   'DATA'),
  ('babel\\locale-data\\km_KH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\km_KH.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('babel\\locale-data\\ckb_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ckb_IR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('babel\\locale-data\\ur_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ur_PK.dat',
   'DATA'),
  ('babel\\locale-data\\nqo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nqo.dat',
   'DATA'),
  ('babel\\locale-data\\nds_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nds_DE.dat',
   'DATA'),
  ('babel\\locale-data\\xh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\xh.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('babel\\locale-data\\ta_SG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ta_SG.dat',
   'DATA'),
  ('babel\\locale-data\\lt_LT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lt_LT.dat',
   'DATA'),
  ('babel\\locale-data\\en_NL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NL.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('babel\\locale-data\\mi_NZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mi_NZ.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('babel\\locale-data\\en_KY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_KY.dat',
   'DATA'),
  ('babel\\locale-data\\mt_MT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mt_MT.dat',
   'DATA'),
  ('babel\\locale-data\\en_GM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('babel\\locale-data\\ru.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('babel\\locale-data\\fr_MU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MU.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('babel\\locale-data\\zh_Hans_MO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hans_MO.dat',
   'DATA'),
  ('babel\\locale-data\\en_NZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('babel\\locale-data\\nds.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nds.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('babel\\locale-data\\en_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CH.dat',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('babel\\locale-data\\la.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\la.dat',
   'DATA'),
  ('babel\\locale-data\\az_Cyrl_AZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Cyrl_AZ.dat',
   'DATA'),
  ('babel\\locale-data\\es_BZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_BZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('babel\\locale-data\\sr_Cyrl_BA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Cyrl_BA.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('babel\\locale-data\\om_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\om_KE.dat',
   'DATA'),
  ('babel\\locale-data\\zgh_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zgh_MA.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('babel\\locale-data\\gez_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gez_ER.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('babel\\locale-data\\de_AT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_AT.dat',
   'DATA'),
  ('babel\\locale-data\\nb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nb.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('babel\\locale-data\\ar_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_IQ.dat',
   'DATA'),
  ('babel\\locale-data\\ckb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ckb.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('babel\\locale-data\\yrl_CO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yrl_CO.dat',
   'DATA'),
  ('babel\\locale-data\\pt_BR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_BR.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('babel\\locale-data\\da_GL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\da_GL.dat',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('babel\\locale-data\\trw_PK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\trw_PK.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('babel\\locale-data\\sah.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sah.dat',
   'DATA'),
  ('babel\\locale-data\\bss.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bss.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('babel\\locale-data\\bs_Cyrl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bs_Cyrl.dat',
   'DATA'),
  ('babel\\locale-data\\sq_MK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sq_MK.dat',
   'DATA'),
  ('babel\\locale-data\\tt_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tt_RU.dat',
   'DATA'),
  ('babel\\locale-data\\sn_ZW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sn_ZW.dat',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('babel\\locale-data\\fr_GF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_GF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('babel\\locale-data\\shn_MM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shn_MM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('babel\\locale-data\\de.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('babel\\locale-data\\br.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\br.dat',
   'DATA'),
  ('babel\\locale-data\\nn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nn.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('babel\\locale-data\\mn_Mong_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mn_Mong_CN.dat',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('babel\\locale-data\\kw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kw.dat',
   'DATA'),
  ('babel\\locale-data\\ar_SS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_SS.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('babel\\locale-data\\wbp_AU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wbp_AU.dat',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('babel\\locale-data\\fr_NC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_NC.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('babel\\locale-data\\ms_SG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ms_SG.dat',
   'DATA'),
  ('babel\\locale-data\\asa_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\asa_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\ti.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ti.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('babel\\locale-data\\pt_PT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_PT.dat',
   'DATA'),
  ('babel\\locale-data\\en_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('babel\\locale-data\\kpe_GN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kpe_GN.dat',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('babel\\locale-data\\fr_SC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_SC.dat',
   'DATA'),
  ('babel\\locale-data\\bm_Nkoo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bm_Nkoo.dat',
   'DATA'),
  ('babel\\locale-data\\es.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('babel\\locale-data\\ts.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ts.dat',
   'DATA'),
  ('babel\\locale-data\\bgn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn.dat',
   'DATA'),
  ('babel\\locale-data\\ig.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ig.dat',
   'DATA'),
  ('babel\\locale-data\\rwk_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rwk_TZ.dat',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('babel\\locale-data\\osa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\osa.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('babel\\locale-data\\uz_Cyrl_UZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Cyrl_UZ.dat',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('babel\\locale-data\\wal_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wal_ET.dat',
   'DATA'),
  ('babel\\locale-data\\or.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\or.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('babel\\locale-data\\mua_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mua_CM.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('babel\\locale-data\\ln.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ln.dat',
   'DATA'),
  ('babel\\locale-data\\cv_RU.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cv_RU.dat',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('babel\\locale-data\\fr_CF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CF.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('babel\\locale-data\\bs_Latn_BA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bs_Latn_BA.dat',
   'DATA'),
  ('babel\\locale-data\\ln_CG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ln_CG.dat',
   'DATA'),
  ('babel\\locale-data\\sbp_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sbp_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\nr.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nr.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('babel\\locale-data\\sr_Latn_XK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Latn_XK.dat',
   'DATA'),
  ('babel\\locale-data\\sq_AL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sq_AL.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('babel\\locale-data\\ko.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ko.dat',
   'DATA'),
  ('babel\\locale-data\\kk_KZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kk_KZ.dat',
   'DATA'),
  ('babel\\locale-data\\pt_GQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_GQ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('babel\\locale-data\\st_LS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\st_LS.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('babel\\locale-data\\lu_CD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lu_CD.dat',
   'DATA'),
  ('babel\\locale-data\\en_GD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GD.dat',
   'DATA'),
  ('babel\\locale-data\\bho_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bho_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('babel\\locale-data\\ha_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ha_NE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('babel\\locale-data\\hy.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hy.dat',
   'DATA'),
  ('babel\\locale-data\\zu.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zu.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('babel\\locale-data\\sr_Cyrl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Cyrl.dat',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('babel\\locale-data\\bgc_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgc_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('babel\\locale-data\\hi_Latn_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hi_Latn_IN.dat',
   'DATA'),
  ('babel\\locale-data\\lmo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lmo.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('babel\\locale-data\\ku.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ku.dat',
   'DATA'),
  ('babel\\locale-data\\fr_TN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_TN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('babel\\locale-data\\uz_Latn_UZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\uz_Latn_UZ.dat',
   'DATA'),
  ('babel\\locale-data\\bez_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bez_TZ.dat',
   'DATA'),
  ('babel\\locale-data\\zh_Hant_MO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hant_MO.dat',
   'DATA'),
  ('babel\\locale-data\\rif.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rif.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('babel\\locale-data\\ko_KP.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ko_KP.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('babel\\locale-data\\yo_BJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yo_BJ.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('babel\\locale-data\\en_MV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MV.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('babel\\locale-data\\jv_ID.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\jv_ID.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('babel\\locale-data\\es_EC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_EC.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('babel\\locale-data\\bm.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bm.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('babel\\locale-data\\en_IE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_IE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('babel\\locale-data\\nl_SR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_SR.dat',
   'DATA'),
  ('babel\\locale-data\\vi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vi.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('babel\\locale-data\\luo_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\luo_KE.dat',
   'DATA'),
  ('babel\\locale-data\\ann_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ann_NG.dat',
   'DATA'),
  ('babel\\locale-data\\ny_MW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ny_MW.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('babel\\locale-data\\so_DJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\so_DJ.dat',
   'DATA'),
  ('babel\\locale-data\\cv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cv.dat',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('babel\\locale-data\\fr_MC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MC.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('babel\\locale-data\\en_TV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TV.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('babel\\locale-data\\apc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\apc.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('babel\\locale-data\\el_POLYTON.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\el_POLYTON.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('babel\\locale-data\\tk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tk.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('babel\\locale-data\\en_CY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_CY.dat',
   'DATA'),
  ('babel\\locale-data\\es_EA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_EA.dat',
   'DATA'),
  ('babel\\locale-data\\be.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\be.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('babel\\locale-data\\en_IM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_IM.dat',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('babel\\locale-data\\sk.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sk.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('babel\\locale-data\\pt_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_CH.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('babel\\locale-data\\ar_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_ER.dat',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_SN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_SN.dat',
   'DATA'),
  ('babel\\locale-data\\ii_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ii_CN.dat',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('babel\\locale-data\\sw_CD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sw_CD.dat',
   'DATA'),
  ('babel\\locale-data\\es_CR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_CR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('babel\\locale-data\\pt_AO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_AO.dat',
   'DATA'),
  ('babel\\locale-data\\ca_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca_ES.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('babel\\locale-data\\sl_SI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sl_SI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('babel\\locale-data\\sms_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sms_FI.dat',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('babel\\locale-data\\en_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_LR.dat',
   'DATA'),
  ('babel\\locale-data\\wa_BE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wa_BE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('babel\\locale-data\\kam_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kam_KE.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('babel\\locale-data\\haw.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\haw.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('babel\\locale-data\\mzn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mzn.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('babel\\locale-data\\om.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\om.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('babel\\locale-data\\os.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\os.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('babel\\locale-data\\luy.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\luy.dat',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('babel\\locale-data\\ky_KG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ky_KG.dat',
   'DATA'),
  ('babel\\locale-data\\br_FR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\br_FR.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('babel\\locale-data\\en_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GH.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('babel\\locale-data\\ga_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ga_GB.dat',
   'DATA'),
  ('babel\\locale-data\\en_TT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_TT.dat',
   'DATA'),
  ('babel\\locale-data\\fr_SY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_SY.dat',
   'DATA'),
  ('babel\\locale-data\\bgn_AE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bgn_AE.dat',
   'DATA'),
  ('babel\\locale-data\\es_AR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_AR.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('babel\\locale-data\\en_HK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_HK.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('babel\\locale-data\\nl_BE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_BE.dat',
   'DATA'),
  ('babel\\locale-data\\mas.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mas.dat',
   'DATA'),
  ('babel\\locale-data\\en_LS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_LS.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('babel\\locale-data\\bn_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bn_IN.dat',
   'DATA'),
  ('babel\\locale-data\\blt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\blt.dat',
   'DATA'),
  ('babel\\locale-data\\frr_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\frr_DE.dat',
   'DATA'),
  ('babel\\locale-data\\fo_DK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fo_DK.dat',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('babel\\locale-data\\en_IO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_IO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('babel\\locale-data\\sdh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sdh.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('babel\\locale-data\\fr_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CH.dat',
   'DATA'),
  ('babel\\locale-data\\en_GB.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GB.dat',
   'DATA'),
  ('babel\\locale-data\\se_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\se_FI.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('babel\\locale-data\\en_MG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_MG.dat',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('babel\\locale-data\\ig_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ig_NG.dat',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('babel\\locale-data\\nl_NL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nl_NL.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('babel\\locale-data\\ceb_PH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ceb_PH.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('babel\\locale-data\\ar_KM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_KM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('babel\\locale-data\\it.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\it.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('babel\\locale-data\\ar_EG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_EG.dat',
   'DATA'),
  ('babel\\locale-data\\en_DK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_DK.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('babel\\locale-data\\dua.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dua.dat',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('babel\\locale-data\\blt_VN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\blt_VN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('babel\\locale-data\\naq_NA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\naq_NA.dat',
   'DATA'),
  ('babel\\locale-data\\se_NO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\se_NO.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('babel\\locale-data\\kln_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kln_KE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('babel\\locale-data\\cad.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cad.dat',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('babel\\locale-data\\sd.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sd.dat',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('babel\\locale-data\\ee_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ee_GH.dat',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('babel\\locale-data\\zh_Hans_SG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zh_Hans_SG.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('babel\\locale-data\\sd_Deva_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sd_Deva_IN.dat',
   'DATA'),
  ('babel\\locale-data\\ccp_BD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ccp_BD.dat',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('babel\\locale-data\\cch_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cch_NG.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_NE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_NE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('babel\\locale-data\\sr_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Latn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('babel\\locale-data\\es_IC.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_IC.dat',
   'DATA'),
  ('babel\\locale-data\\seh_MZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\seh_MZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('babel\\locale-data\\az_Arab_TR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Arab_TR.dat',
   'DATA'),
  ('babel\\locale-data\\as.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\as.dat',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('babel\\locale-data\\asa.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\asa.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('babel\\locale-data\\sr_Cyrl_XK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sr_Cyrl_XK.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('babel\\locale-data\\lt.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lt.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('babel\\locale-data\\sat_Olck.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sat_Olck.dat',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('babel\\locale-data\\pcm_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pcm_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('babel\\locale-data\\en_BS.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BS.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('babel\\locale-data\\az_Latn_AZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Latn_AZ.dat',
   'DATA'),
  ('babel\\locale-data\\fr_WF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_WF.dat',
   'DATA'),
  ('babel\\locale-data\\ar_EH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_EH.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('babel\\locale-data\\cch.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\cch.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('babel\\locale-data\\en_NF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_NF.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('babel\\locale-data\\en_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_US.dat',
   'DATA'),
  ('babel\\locale-data\\pt_TL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_TL.dat',
   'DATA'),
  ('babel\\locale-data\\id.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\id.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('babel\\locale-data\\az_Cyrl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\az_Cyrl.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('babel\\locale-data\\en_BE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_BE.dat',
   'DATA'),
  ('babel\\locale-data\\trv_TW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\trv_TW.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('babel\\locale-data\\es_UY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_UY.dat',
   'DATA'),
  ('babel\\locale-data\\sid_ET.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sid_ET.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('babel\\locale-data\\yue_Hans.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yue_Hans.dat',
   'DATA'),
  ('babel\\locale-data\\moh.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\moh.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('babel\\locale-data\\quc_GT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\quc_GT.dat',
   'DATA'),
  ('babel\\locale-data\\hnj_Hmnp_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hnj_Hmnp_US.dat',
   'DATA'),
  ('babel\\locale-data\\es_GT.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_GT.dat',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('babel\\locale-data\\hi_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hi_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('babel\\locale-data\\en_FJ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_FJ.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('babel\\locale-data\\zu_ZA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\zu_ZA.dat',
   'DATA'),
  ('babel\\locale-data\\sat_Deva_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sat_Deva_IN.dat',
   'DATA'),
  ('babel\\locale-data\\pt_MZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_MZ.dat',
   'DATA'),
  ('babel\\locale-data\\gl_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gl_ES.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_GM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_GM.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('babel\\locale-data\\sat_Deva.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sat_Deva.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('babel\\locale-data\\ssy.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ssy.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('babel\\locale-data\\ksb_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ksb_TZ.dat',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('babel\\locale-data\\te_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\te_IN.dat',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('babel\\locale-data\\kaj_NG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kaj_NG.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('babel\\locale-data\\en_PG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PG.dat',
   'DATA'),
  ('babel\\locale-data\\shi_Latn_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shi_Latn_MA.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('babel\\locale-data\\fur.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fur.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('babel\\locale-data\\kpe_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kpe_LR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('babel\\locale-data\\dv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dv.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('babel\\locale-data\\en_150.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_150.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('babel\\locale-data\\ba.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ba.dat',
   'DATA'),
  ('babel\\locale-data\\iu_Latn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\iu_Latn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('babel\\locale-data\\en_KN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_KN.dat',
   'DATA'),
  ('babel\\locale-data\\bal_Arab.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bal_Arab.dat',
   'DATA'),
  ('babel\\locale-data\\es_GQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_GQ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('babel\\locale-data\\tn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('babel\\locale-data\\is.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\is.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('babel\\locale-data\\se_SE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\se_SE.dat',
   'DATA'),
  ('babel\\locale-data\\lkt_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lkt_US.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('babel\\locale-data\\en_PR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PR.dat',
   'DATA'),
  ('babel\\locale-data\\pl_PL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pl_PL.dat',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('babel\\locale-data\\fi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fi.dat',
   'DATA'),
  ('babel\\locale-data\\gez.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gez.dat',
   'DATA'),
  ('babel\\locale-data\\id_ID.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\id_ID.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('babel\\locale-data\\lrc_IR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lrc_IR.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('babel\\locale-data\\es_419.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_419.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('babel\\locale-data\\ru_KZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ru_KZ.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('babel\\locale-data\\fr_CA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_CA.dat',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('babel\\locale-data\\vai_Vaii_LR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\vai_Vaii_LR.dat',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('babel\\locale-data\\fr_TG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_TG.dat',
   'DATA'),
  ('babel\\locale-data\\rn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rn.dat',
   'DATA'),
  ('babel\\locale-data\\quc.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\quc.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('babel\\locale-data\\kde.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kde.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('babel\\locale-data\\szl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\szl.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('babel\\locale-data\\ff_Latn_SL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Latn_SL.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('babel\\locale-data\\bo_CN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bo_CN.dat',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('babel\\locale-data\\mni_Beng.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mni_Beng.dat',
   'DATA'),
  ('babel\\locale-data\\et_EE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\et_EE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('babel\\locale-data\\en_PW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_PW.dat',
   'DATA'),
  ('babel\\locale-data\\fr_BF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_BF.dat',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('babel\\locale-data\\saq_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\saq_KE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('babel\\locale-data\\en_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_DE.dat',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('babel\\locale-data\\fr_MR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MR.dat',
   'DATA'),
  ('babel\\locale-data\\ar_MR.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_MR.dat',
   'DATA'),
  ('babel\\locale-data\\af.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\af.dat',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('babel\\locale-data\\qu_PE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\qu_PE.dat',
   'DATA'),
  ('babel\\locale-data\\arn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\arn.dat',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('babel\\locale-data\\hnj_Hmnp.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hnj_Hmnp.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('babel\\locale-data\\fr_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_MA.dat',
   'DATA'),
  ('babel\\locale-data\\dav_KE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\dav_KE.dat',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('babel\\locale-data\\byn_ER.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\byn_ER.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('babel\\locale-data\\yrl_VE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yrl_VE.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('babel\\locale-data\\gv.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\gv.dat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('babel\\locale-data\\tg.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\tg.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('babel\\locale-data\\nb_NO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nb_NO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('babel\\locale-data\\yrl.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yrl.dat',
   'DATA'),
  ('babel\\locale-data\\oc_ES.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\oc_ES.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('babel\\locale-data\\es_DO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\es_DO.dat',
   'DATA'),
  ('babel\\locale-data\\mus.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mus.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('babel\\locale-data\\lag.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lag.dat',
   'DATA'),
  ('babel\\locale-data\\mdf.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mdf.dat',
   'DATA'),
  ('babel\\locale-data\\ve.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ve.dat',
   'DATA'),
  ('babel\\locale-data\\mni_Mtei_IN.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mni_Mtei_IN.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('babel\\locale-data\\wo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\wo.dat',
   'DATA'),
  ('babel\\locale-data\\kea.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kea.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('babel\\locale-data\\en_GY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_GY.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('babel\\locale-data\\kea_CV.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\kea_CV.dat',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('babel\\locale-data\\en_DM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_DM.dat',
   'DATA'),
  ('babel\\locale-data\\nn_NO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\nn_NO.dat',
   'DATA'),
  ('babel\\locale-data\\syr_IQ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\syr_IQ.dat',
   'DATA'),
  ('babel\\locale-data\\osa_US.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\osa_US.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_GH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_GH.dat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('babel\\locale-data\\ca_AD.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ca_AD.dat',
   'DATA'),
  ('babel\\locale-data\\ee.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ee.dat',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('babel\\locale-data\\mua.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mua.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('babel\\locale-data\\fr_BL.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_BL.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('babel\\locale-data\\lij.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\lij.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('babel\\locale-data\\pt_GW.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\pt_GW.dat',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('babel\\locale-data\\mi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mi.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm.dat',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('babel\\locale-data\\yue_Hant_HK.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\yue_Hant_HK.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('babel\\locale-data\\mgo.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mgo.dat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('babel\\locale-data\\be_BY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\be_BY.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('babel\\locale-data\\so_SO.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\so_SO.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('babel\\locale-data\\en_SG.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\en_SG.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('babel\\locale-data\\rif_MA.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\rif_MA.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('babel\\locale-data\\sn.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\sn.dat',
   'DATA'),
  ('babel\\locale-data\\ar_DZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_DZ.dat',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('babel\\locale-data\\mas_TZ.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\mas_TZ.dat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('babel\\locale-data\\de_DE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_DE.dat',
   'DATA'),
  ('babel\\locale-data\\de_CH.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_CH.dat',
   'DATA'),
  ('babel\\locale-data\\fi_FI.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fi_FI.dat',
   'DATA'),
  ('babel\\locale-data\\ff_Adlm_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ff_Adlm_CM.dat',
   'DATA'),
  ('babel\\locale-data\\ar_LY.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\ar_LY.dat',
   'DATA'),
  ('babel\\locale-data\\fr_PF.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\fr_PF.dat',
   'DATA'),
  ('babel\\locale-data\\hsb.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\hsb.dat',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('babel\\locale-data\\shi.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\shi.dat',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('babel\\locale-data\\de_BE.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\de_BE.dat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('babel\\locale-data\\bas_CM.dat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\babel\\locale-data\\bas_CM.dat',
   'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
