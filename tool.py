#import asyncio
import json
import re
#import socket
import sys
from tkinter import *
import os
import tkinter as tk
from urllib import parse

import ddddocr
from PIL import Image
import requests,time
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import base64
from bs4 import BeautifulSoup
#import pytesseract
#from requests_html import AsyncHTMLSession

# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

# 获取当前文件所在目录的路径
#current_dir = os.path.dirname(os.path.abspath(__file__))
print('当前文件所在目录的路径'+current_dir)
# 构建 tessdata 目录的路径
# tessdata_dir = os.path.join(current_dir, 'Tesseract-OCR', 'tessdata')
# testdata_dir_config = f'--tessdata-dir "{tessdata_dir}"'
#
# # 构建 Tesseract 可执行文件的路径
# tesseract_path = os.path.join(current_dir, 'Tesseract-OCR', 'tesseract.exe')

# 设置 Tesseract 可执行文件的路径
# pytesseract.pytesseract.tesseract_cmd = tesseract_path

# model_path = os.path.join(current_dir, "common.onnx")
# print(model_path)

class Tool:
    def __init__(self, output_textbox,account_entry,password_entry, root,organization_combobox):
        self.output_textbox = output_textbox
        self.account_entry = account_entry
        self.password_entry = password_entry
        self.root = root
        self.organization_combobox = organization_combobox
        # 选择的机构，默认为空
        self.selected_organization = ""
        # 绑定下拉框选择事件
        self.organization_combobox.bind("<<ComboboxSelected>>", self.read_data)

        self.L = 0  # 初始化 L
    def on_combobox_selected(self,even):
        # 下拉框选择事件处理
        self.selected_organization = self.organization_combobox.get()
        print(self.selected_organization)
        # 在选择机构变化时，调用读取数据的函数
        self.read_data()

    # 更新输出文本框的函数
    # def process_input(self, text, update_callback=None):
    #
    #     if update_callback is not None:
    #         self.parent.after(0, update_callback)
    #     else:
    #         # 在输出文本框中显示结果
    #         self.output_textbox.configure(state="normal")  # 设置文本框为可编辑状态
    #         self.output_textbox.insert(tk.END, text + '\n')  # 在文本框末尾插入新内容
    #         self.output_textbox.see(tk.END)  # 滚动到文本框末尾
    #         self.output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    #         self.root.update()  # 更新界面，确保文本框能及时显示新内容

    def process_input(self, text, L=None, update_callback=None):
        if update_callback is not None:
            self.parent.after(0, update_callback)
        else:
            # 如果 L 不为空，就更新已爬几件
            if L is not None:
                current_text = self.output_textbox.get("1.0", tk.END).strip()  # 获取文本并去掉末尾的换行符
                lines = current_text.split("\n")

                # 查找以 "已爬" 开头的行并更新 L 的值
                updated = False
                for i in range(len(lines)):
                    lines[i] = lines[i].strip()  # 去掉每行前后的空格
                    if lines[i].startswith("已爬"):
                        lines[i] = f"已爬{L}件"  # 更新已爬的件数
                        updated = True
                        break

                if not updated:
                    lines.append(f"已爬{L}件")  # 如果没有已爬行，添加新的

                # 清空文本框并插入更新后的内容
                self.output_textbox.configure(state="normal")
                self.output_textbox.delete("1.0", tk.END)
                self.output_textbox.insert(tk.END, "\n".join(lines).strip())
                self.output_textbox.configure(state="disabled")
                self.root.update()  # 更新界面

            else:
                # 如果 L 是空的，正常添加新内容
                self.output_textbox.configure(state="normal")
                self.output_textbox.insert(tk.END, text + '\n')  # 在文本框末尾插入新内容
                self.output_textbox.see(tk.END)  # 滚动到文本框末尾
                self.output_textbox.configure(state="disabled")
                self.root.update()  # 更新界面

    def update_L(self, new_L):
        self.L = new_L
        self.process_input('', L=self.L)  # 更新 L，不插入新内容，直接更新“已爬几件”

    def quit(self):
        # 获取当前 Python 进程 ID
        this_pid = os.getpid()

        # 关闭当前进程
        os.kill(this_pid, 9)
        # 销毁窗口以结束程序
        self.root.destroy()

    def aes_encrypt(self,srcs, key):
        cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
        encrypted_bytes = cipher.encrypt(pad(srcs.encode('utf-8'), AES.block_size))
        encrypted_text = base64.b64encode(encrypted_bytes).decode('utf-8')
        return encrypted_text

    # # 读取账号和密码的函数
    # def read_data(self):
    #     try:
    #         with open('user.txt', "r") as file:
    #             lines = file.readlines()
    #             if len(lines) > 0:
    #                 self.account_entry.delete(0, END)
    #                 self.account_entry.insert(0, lines[0].strip())
    #             if len(lines) > 1:
    #                 self.password_entry.delete(0, END)
    #                 self.password_entry.insert(0, lines[1].strip())
    #     except FileNotFoundError:
    #         pass
    #
    # # 保存账号和密码的函数
    # def save_data(self):
    #     account = self.account_entry.get()
    #     password = self.password_entry.get()
    #     with open('user.txt', "w") as file:
    #         file.write(account + "\n")
    #         file.write(password + "\n")

    # 读取账号和密码的函数
    def read_data(self,even=None):
        file_path = 'user.txt'
        # 下拉框选择事件处理
        self.selected_organization = self.organization_combobox.get()
        print(self.selected_organization)
        try:
            with open(file_path, "r") as file:
                lines = file.readlines()
                # 根据选择的机构进行匹配
                found_match = False  # 用于标记是否找到匹配项

                for line in lines:
                    data = line.strip().split(',')
                    if len(data) == 3 and data[0] == self.selected_organization:
                        self.account_entry.delete(0, END)
                        self.account_entry.insert(0, data[1])
                        self.password_entry.delete(0, END)
                        self.password_entry.insert(0, data[2])
                        found_match = True
                        break

                # 如果没有找到匹配项，清空账号密码栏
                if not found_match:
                    self.account_entry.delete(0, END)
                    self.password_entry.delete(0, END)
        except FileNotFoundError:
            pass

    # 保存账号和密码的函数
    def save_data(self):
        # 下拉框选择事件处理
        self.selected_organization = self.organization_combobox.get()
        account = self.account_entry.get()
        password = self.password_entry.get()
        file_path = 'user.txt'
        try:
            with open(file_path, "r") as file:
                lines = file.readlines()
                # 根据选择的机构进行匹配
                found = False
                for i, line in enumerate(lines):
                    data = line.strip().split(',')
                    if len(data) == 3 and data[0] == self.selected_organization:
                        lines[i] = f'{self.selected_organization},{account},{password}\n'
                        found = True
                        break

                if not found:
                    lines.append(f'{self.selected_organization},{account},{password}\n')

            with open(file_path, "w") as file:
                file.writelines(lines)
        except FileNotFoundError:
            with open(file_path, "w") as file:
                file.write(f'{self.selected_organization},{account},{password}\n')



    # 把下载完成的图片灰度化处理一下 提高识别准确率
    def image_er(self):
        image = Image.open('captcha.jpg').convert('L')
        table = []
        for i in range(256):
            if i < 70:
                table.append(0)
            else:
                table.append(1)
        image.point(table, '1').save('code.jpg')

    def getck(self,username,password,session,skip_url,substitutionorg=None):
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/cas/login'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded',

            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
            # 'Cookie': session
        }
        # response = requests.get(url, headers=headers, verify=False,proxies=proxies)#
        response = session.get(url, headers=headers, verify=False)  #
        r = response.text

        html = BeautifulSoup(r, 'lxml')
        lt = html.find('input', attrs={'name': 'lt'})['value']
        # print ('lt值为：'+str(lt))

        execution = html.find('input', attrs={'name': 'execution'})['value']
        # print ('execution值为：'+str(execution))

        response.close()

        jdptid = self.login(username,password,lt, execution,session,skip_url,substitutionorg)
        return jdptid
        # print (html.text)

    def getcaptcha(self,session):
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/cas/captcha.jpg'
        headers = {

            'Content-type': 'application/x-www-form-urlencoded',

            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
            # 'Cookie':session
        }
        # response = requests.get(url, headers=headers, verify=False,proxies=proxies)
        response = session.get(url, headers=headers, verify=False)
        # 把获取的二进制写成图片
        # with open('captcha.jpg', 'wb') as f:
        #     f.write(response.content)
        # self.image_er()
        # time.sleep(5)
        # captcha = pytesseract.image_to_string(Image.open("code.jpg"), lang='captcha').strip()
        # # print('原始：'+captcha)
        # self.process_input('原始：' + captcha)
        # dictionary = {'§': '5', 'a': '8', 'A': '4', 'T': '7', 'S': '5', 'Q': '0', 'O': '0', ' ': ''}
        # transTable = captcha.maketrans(dictionary)
        # captcha = captcha.translate(transTable)
        # # print('修正：'+captcha)
        # self.process_input('修正：' + captcha)
        # 把获取的二进制写成图片
        image_path = 'captcha.jpg'
        with open(image_path, 'wb') as f:
            f.write(response.content)
        # 使用ddddocr识别验证码，设置识别范围为数字
        ocr = ddddocr.DdddOcr()

        ocr.set_ranges("0123456789")  # 设置识别范围为数字
        with open(image_path, 'rb') as f:
            img_bytes = f.read()

        captcha = ocr.classification(img_bytes).strip()
        self.process_input('智能识别：' + captcha)
        # 删除验证码图片
        os.remove('captcha.jpg')
        #os.remove('code.jpg')
        return captcha

    def login(self,username,password,lt, execution,session,skip_url,substitutionorg=None):
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/cas/login?service=https://**********/portal/a/cas'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded',
            'Host': '**********',

            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
            # 'Cookie': session
        }
        captcha = self.getcaptcha(session)

        data = {
            'username': username,
            'password': password,
            'captcha': captcha,
            'showCaptcha': 'true',
            'lt': lt,
            'execution': execution,
            '_eventId': 'submit',
            'loginIdentifier': 'loginIdentifier123',
            'loginCode': '************',
            'thirdSign': '',
            'jdptSysId': '',
            'jdptLoginTag': '',
            'jdptReqTime': '',
            'jdptSign': '',
            'usr_bak': ''
        }
        # response = requests.post(url, headers=headers,params=data, verify=False,proxies=proxies,allow_redirects=False)
        response =session.post(url, headers=headers, params=data, verify=False, allow_redirects=False)
        r = response.text
        #print(response.headers)
        if r.find("登录成功，请稍侯") > 0:

            # print ('登录成功')
            self.process_input('登录成功')
            # ====================================
            #print(session.cookies.get_dict())
            #skip_url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist'
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',

                'Connection': 'keep-alive',
                'Content-type': 'application/x-www-form-urlencoded',
                'Host': '**********',
                'Referer': 'https://**********/cas/login?service=https://**********/portal/a/cas',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
                # 'Cookie': cookies
            }
            # req = requests.get(skip_url, headers=headers,verify=False,proxies=proxies)
            mainurl='https://**********/portal/a'
            #mainurl='https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
            # user_name_value='None'
            # orgName_value='None'
            # orgcode_value='None'
            #if skip_url != mainurl:
            req =session.get(mainurl, headers=headers, verify=False)
        #req = session.get(mainurl, headers=headers, verify=False)
        # 使用Beautiful Soup解析HTML内容
            soup = BeautifulSoup(req.text, 'html.parser')
            #print(req.text)


            if '请选择替班机构' in req.text:
                # 查找所有 a 标签
                links = soup.find_all('a', href=True)
                target_url = None

                for link in links:
                    link_text = link.get_text(strip=True)  # 提取 a 标签文本
                    href = link['href']

                    if substitutionorg and substitutionorg in link_text:  # 匹配文本内容
                        target_url = f"https://**********{href}"
                        break  # 只取第一个匹配的

                if target_url:
                    print(f"请求替班机构页面: {target_url}")
                    org_req = session.get(target_url, headers=headers, verify=False)
                    soup = BeautifulSoup(org_req.text, 'html.parser')
            # 找到特定ID为"userName"的<span>标签并提取值
            user_name_span = soup.find('span', id='userName')
            user_name_value = user_name_span.text.strip() if user_name_span else 'None'
            # user_name_span = soup.find('input', id='openWorkerName')
            # user_name_value = user_name_span.get('value') if user_name_span else 'None'

            self.process_input(user_name_value)

            orgName_span = soup.find('span', id='orgName')
            orgName_value = orgName_span.text.strip() if orgName_span else 'None'
            # orgName_span = soup.find('input', id='orgName')
            # orgName_value = orgName_span.get('value') if orgName_span else 'None'

            self.process_input(orgName_value)

            orgcode_span = soup.find('span', id='orgCode')
            orgcode_value = orgcode_span.text.strip() if orgcode_span else 'None'
            # orgcode_span = soup.find('input', id='orgCode')
            # orgcode_value = orgcode_span.get('value') if orgcode_span else 'None'

            self.process_input(orgcode_value)

            if '国际互换局' in orgName_value:
                jdptid = session.cookies.get_dict()['jdpt.session.id']
                self.saveOrgShopTeamSeat(jdptid,session)


            #req = session.get(skip_url, headers=headers, verify=False)
            #print(req.headers)
            req = session.get(skip_url, headers=headers, verify=False,allow_redirects = False)
            #print(req.headers)
            #print(req.text)
            #jdptid = re.findall(r'jdpt.session.id=(.+?);', req.headers['Set-Cookie'])[0]

            match = re.search(r'jdpt.session.id=(.+?);', req.headers.get('Set-Cookie', ''))

            if match:
                jdptid = match.group(1)
                print(f"Match found! jdptid: {jdptid}")
                session.cookies.set('jdpt.session.id', jdptid)
                headers = {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',

                    'Connection': 'keep-alive',
                    'Content-type': 'application/x-www-form-urlencoded',
                    'Host': '**********',
                    'Referer': 'https://**********/portal/a',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'

                }
                req = session.get(skip_url, headers=headers, verify=False)
                #print(req.text)
            else:
                jdptid = session.cookies.get_dict()['jdpt.session.id']
                print("No match found for jdpt.session.id in Set-Cookie header.")

            # if not jdptid:
            #     jdptid = session.cookies.get_dict()['jdpt.session.id']
            # else:
            #     session.cookies.set('jdpt.session.id', jdptid)
            #     req = session.get(skip_url, headers=headers, allow_redirects=False)
            #print (jdptid)
            return str(jdptid),user_name_value,orgcode_value
        else:
            # print ('登录失败')
            self.process_input('登录失败')
            return '0','',''

    def saveOrgShopTeamSeat(self,jdptid,session):
        url = 'https://**********/portal/a/basic/saveOrgShopTeamSeat'
        data = {
            'rs': '{"workShopCode":"SD51040034","workShopName":"默认车间","workShopGroupCode":"GJCK","workShopGroupName":"国际出口班","seatCode":"30001","seatName":"测试01"}'
        }
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Length': '79',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'DNT': '1',
            'Host': '**********',
            'Origin': 'https://**********',
            'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
            'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        response = session.post(url, headers=headers, data=data, verify=False)
        print(response.text)
        self.process_input(response.text)

    def getyycaptcha(self,session):
        requests.packages.urllib3.disable_warnings()
        url = 'http://*************/gdsdjygkxt/Kaptcha.jpg'
        headers = {

            'Content-type': 'application/x-www-form-urlencoded',

            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
            # 'Cookie':session
        }
        # response = requests.get(url, headers=headers, verify=False,proxies=proxies)
        response = session.get(url, headers=headers, verify=False)
        # # 把获取的二进制写成图片
        # with open('Kaptcha.jpg', 'wb') as f:
        #     f.write(response.content)
        # self.image_yyer()
        # time.sleep(5)
        # captcha = pytesseract.image_to_string(Image.open("code.jpg"), lang='captcha').strip()
        # # print('原始：'+captcha)
        # self.process_input('原始：' + captcha)
        # dictionary = {'§': '5', 'a': '8', 'A': '4', 'T': '7', 'S': '5', 'Q': '0', 'O': '0', ' ': ''}
        # transTable = captcha.maketrans(dictionary)
        # captcha = captcha.translate(transTable)
        # # print('修正：'+captcha)
        # self.process_input('修正：' + captcha)
        # # 删除验证码图片
        # os.remove('Kaptcha.jpg')
        # os.remove('code.jpg')
        # 把获取的二进制写成图片
        image_path = 'captcha.jpg'
        with open(image_path, 'wb') as f:
            f.write(response.content)
        # 使用ddddocr识别验证码，设置识别范围为数字
        ocr = ddddocr.DdddOcr()
        ocr.set_ranges("0123456789")  # 设置识别范围为数字
        with open(image_path, 'rb') as f:
            img_bytes = f.read()

        captcha = ocr.classification(img_bytes).strip()
        self.process_input('ddddocr识别：' + captcha)
        os.remove(image_path)
        return captcha

    def image_yyer(self):
        image = Image.open('Kaptcha.jpg').convert('L')
        table = []
        for i in range(256):
            if i < 70:
                table.append(0)
            else:
                table.append(1)
        image.point(table, '1').save('code.jpg')

    def loginyy(self,session):
        requests.packages.urllib3.disable_warnings()
        url = 'http://*************/gdsdjygkxt/login/login!login.action'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded',
            'Host': '*************',
            'Origin': 'http://*************',
            'Referer': 'http://*************/gdsdjygkxt/',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
            # 'Cookie': session
        }
        captcha = self.getyycaptcha(session)

        data = {
            'loginName': 'zhuxiaoying',
            'password': 'GDyzjd#123',
            'verifyCode': captcha
        }
        # response = requests.post(url, headers=headers,params=data, verify=False,proxies=proxies,allow_redirects=False)
        response = session.post(url, headers=headers, params=data, verify=False, allow_redirects=False)
        r = response.text
        # print (r)
        if r.find('校验码错误，请重新输入！') > 0:
            return '0'
        else:
            # print('登录成功')
            self.process_input('登录成功')
            return '1'


    def postlog(self,user,name,module,ip):
        requestUrl = 'http://************:8520/api/xyd/log'
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        formData = {
            "user": user,
            "username": name,
            "module": module,
            "ip": ip
        }
        data = parse.urlencode(formData, True)

        #response = requests.post(requestUrl, headers=headers, data=data)
        #print(response.text)
        try:
            response = requests.post(requestUrl, headers=headers, data=data)
            response.raise_for_status()  # 检查HTTP响应状态
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            self.process_input(f"请求失败: {e}")
            return None
        except requests.exceptions.HTTPError as e:
            print(f"HTTP错误: {e}")
            self.process_input(f"HTTP错误: {e}")
            return None

    # 登录函数
    async def newlogin(self,page, username, password, max_retries=5):
        login_url = "https://**********/cas/login"
        retries = 0

        while retries < max_retries:
            try:
                await page.goto(login_url)
                self.process_input("打开登录页面")

                # 填写用户名和密码
                await page.fill('#username', username)
                await page.fill('#password', password)

                # 获取验证码图片的src
                captcha_img = await page.query_selector('div#sec_code > img')
                captcha_src = await captcha_img.get_attribute('src')
                #self.process_input(f"验证码图片地址: {captcha_src}")

                # 下载验证码图片并识别
                image_path = 'captcha.jpg'
                await captcha_img.screenshot(path=image_path)
                #self.process_input(f"保存验证码图片到: {image_path}")

                ocr = ddddocr.DdddOcr()
                with open(image_path, "rb") as f:
                    img_bytes = f.read()
                captcha = ocr.classification(img_bytes).strip()
                self.process_input(f"验证码识别结果: {captcha}")

                # 填写验证码并提交表单
                await page.fill('#security', captcha)
                submit_button = await page.query_selector('input#login[type="submit"]')
                await submit_button.click()

                try:
                    await page.wait_for_selector("li[name='首页']", timeout=10000)
                    self.process_input("登录成功")

                    # 获取页面内容
                    #html_content = await page.content()  # 获取 HTML 内容
                    #print(html_content)  # 打印网页 HTML 内容

                    # 提取值
                    user_name_value = await page.locator("#user").inner_text()  # 获取“登录人”的值
                    orgName_value = await page.locator("#orgName").inner_text()  # 获取“机构简称”的值
                    orgcode_value = await page.locator("#orgCode").inner_text()  # 获取“机构代码”的值
                    self.process_input(f"""登录人: {user_name_value}\n机构简称: {orgName_value}\n机构代码: {orgcode_value}""")
                    # 删除验证码图片
                    os.remove(image_path)  # 删除验证码图片

                    #await page.wait_for_selector("#saveData", timeout=5000)  # 如果在5秒内没找到，抛出超时异常
                    #保存台席弹窗
                    #await txxz(page)


                    return user_name_value, orgName_value, orgcode_value
                    break
                except Exception as e:
                    #logging.error(f"登录失败或超时: {e}")
                    self.process_input(f"登录失败或超时: {e}")
                    retries += 1
                    # 如果超过最大重试次数，抛出异常
                    if retries == max_retries:
                        raise Exception("登录失败，已达到最大重试次数")
                    raise Exception("登录失败，检查验证码或其他问题")
                    #await page.reload()  # 重新加载页面#return None, None, None
            except Exception as e:
                retries += 1

                self.process_input(f"登录尝试失败，重试 {retries}/{max_retries} 次")

                if retries == max_retries:
                    raise Exception("登录失败，已达到最大重试次数")
        return None, None, None

    # 处理台席弹窗的函数
    async def txxz(self,page, retries=3):
        attempt = 0

        # 获取网络响应
        async def handle_response(response):
            # 判断响应的 URL 是否包含目标部分
            if any('saveOrgShopTeamSeat' in response.url):
                print(f"<< Status: {response.status} URL: {response.url}")
                try:
                    # 获取响应文本
                    r = await response.text()
                    json_data = json.loads(r)
                    if json_data['success'] == 'true':
                        print("操作成功")
                        self.process_input(json_data)
                    else:
                        print("操作失败")

                except Exception as e:
                    print(f"无法解析响应: {e}")

        while attempt < retries:
            try:
                # 等待“确定”按钮的出现（即进入班组的弹窗）
                await page.wait_for_selector("#saveData", timeout=5000)  # 如果在5秒内没找到，抛出超时异常
                page.on("response", handle_response)
                # 找到并点击“确定”按钮
                save_button = await page.query_selector("#saveData")
                if save_button:
                    await save_button.click()  # 点击“确定”按钮
                    print("点击了确定按钮，进入班组")

                    return  # 成功点击按钮，结束函数
                else:
                    print("未找到确定按钮，页面没有弹窗")
                    return  # 没有找到按钮，则不继续尝试
            except Exception as e:
                # 如果找不到弹窗元素，说明没有弹窗，重新加载页面
                print(f"没有弹窗或发生错误：{e}")
                print("重新加载页面...")
                await page.reload()
                attempt += 1  # 增加尝试次数

        print(f"尝试{retries}次后，仍未找到弹窗，退出。")  # 达到最大重试次数后退出