!(function($,win){
    Date.prototype.format = function(format) {
        var o = {
            'M+': this.getMonth() + 1,
            'd+': this.getDate(),
            'h+': this.getHours(),
            'm+': this.getMinutes(),
            's+': this.getSeconds(),
            'q+': Math.floor((this.getMonth() + 3) / 3),
            'S': this.getMilliseconds()
        }
        if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
        for (var k in o) if (new RegExp('(' + k + ')').test(format)) format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
        return format;
    }

    function createRoll(items){
        if(items.length==0){return false}
        var pre = '<div class="navbar_notice"><marquee class="notify_roll" scrollamount = "6" scrolldelay = "50" onmouseover = "this.stop()" onmouseout = "this.start()" id = "scrollNotice" >', post = '</marquee><span class="notify_roll_close" onclick="this.parentNode.style.display=\'none\'">×</span></div></div></div>', str = '';
        if(items.length==1){
            str = '<span class="notify_roll_item" title="' + items[0].bulletinContent + '"><span style="font-weight:bold">系统通知：</span>《' + items[0].bulletinTitle + '》' + items[0].bulletinContent + '</span>'
        }else{
            for (var i = 0, len = items.length; i < len; i++) {
                str += '<span class="notify_roll_item" title="' + items[i].bulletinContent + '"><span style="font-weight:bold">系统通知' + (i + 1) +'：</span>《' + items[i].bulletinTitle+'》'+ items[i].bulletinContent+'</span>'
            }
        }
        $(".navbar.navbar-fixed-top").prepend(pre+str+post)
    }
    
    function createSlide(items){
        if(items.length==0){return false}
        var pre = '<div class="notify_slide"><div class="notify_slide_title">系统通知（共'+items.length+'条）</div><div class="notify_slide_close" onclick="this.parentNode.style.display=\'none\'"></div><div class="notify_wrapper">', post ='</div></div>',str='';
        for(var i=0,len=items.length;i<len;i++){
            str += '<div class="notify_item"><div class="notify_title">' +(i+1)+'.《'+ items[i].bulletinTitle + '》</div><div class="notify_content">' + items[i].bulletinContent + '<div class="notify_note">' + new Date(items[i].statusTime).format("yyyy-MM-dd hh:mm:ss") + '</div></div></div>'
        }
        $("body").append(pre+str+post)
    }
    win.createNotify=function(results){
        if (!results || !results.items || results.items.length == 0) { return false }
        //var results = JSON.parse(results)
        var items=results.items;rolls=[],slides=[];
        for (var i = 0,len=items.length;i<len;i++){
            if (items[i].bulletinPosition == "LOGIN_ROLL") { rolls.push(items[i])}
            if (items[i].bulletinPosition == "LOGIN_SLIDER") { slides.push(items[i])}
        }
        createRoll(rolls)
        createSlide(slides)
    }

})(jQuery,window);

//升级到jquery1.12.1后，为了兼容jBox弹框打补丁
(function(jQuery) {
    if (jQuery.browser) return;
    jQuery.browser = {};
    jQuery.browser.mozilla = false;
    jQuery.browser.webkit = false;
    jQuery.browser.opera = false;
    jQuery.browser.msie = false;

    var nAgt = navigator.userAgent;
    jQuery.browser.name = navigator.appName;
    jQuery.browser.fullVersion = '' + parseFloat(navigator.appVersion);
    jQuery.browser.majorVersion = parseInt(navigator.appVersion, 10);
    var nameOffset, verOffset, ix;

    // In Opera, the true version is after "Opera" or after "Version"   
    if ((verOffset = nAgt.indexOf("Opera")) != -1) {
        jQuery.browser.opera = true;
        jQuery.browser.name = "Opera";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 6);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In MSIE, the true version is after "MSIE" in userAgent   
    else if ((verOffset = nAgt.indexOf("MSIE")) != -1) {
        jQuery.browser.msie = true;
        jQuery.browser.name = "Microsoft Internet Explorer";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 5);
    }
    // In Chrome, the true version is after "Chrome"   
    else if ((verOffset = nAgt.indexOf("Chrome")) != -1) {
        jQuery.browser.webkit = true;
        jQuery.browser.name = "Chrome";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 7);
    }
    // In Safari, the true version is after "Safari" or after "Version"   
    else if ((verOffset = nAgt.indexOf("Safari")) != -1) {
        jQuery.browser.webkit = true;
        jQuery.browser.name = "Safari";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 7);
        if ((verOffset = nAgt.indexOf("Version")) != -1)
            jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In Firefox, the true version is after "Firefox"   
    else if ((verOffset = nAgt.indexOf("Firefox")) != -1) {
        jQuery.browser.mozilla = true;
        jQuery.browser.name = "Firefox";
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 8);
    }
    // In most other browsers, "name/version" is at the end of userAgent   
    else if ((nameOffset = nAgt.lastIndexOf(' ') + 1) <
        (verOffset = nAgt.lastIndexOf('/'))) {
        jQuery.browser.name = nAgt.substring(nameOffset, verOffset);
        jQuery.browser.fullVersion = nAgt.substring(verOffset + 1);
        if (jQuery.browser.name.toLowerCase() == jQuery.browser.name.toUpperCase()) {
            jQuery.browser.name = navigator.appName;
        }
    }
    // trim the fullVersion string at semicolon/space if present   
    if ((ix = jQuery.browser.fullVersion.indexOf(";")) != -1)
        jQuery.browser.fullVersion = jQuery.browser.fullVersion.substring(0, ix);
    if ((ix = jQuery.browser.fullVersion.indexOf(" ")) != -1)
        jQuery.browser.fullVersion = jQuery.browser.fullVersion.substring(0, ix);

    jQuery.browser.majorVersion = parseInt('' + jQuery.browser.fullVersion, 10);
    if (isNaN(jQuery.browser.majorVersion)) {
        jQuery.browser.fullVersion = '' + parseFloat(navigator.appVersion);
        jQuery.browser.majorVersion = parseInt(navigator.appVersion, 10);
    }
    jQuery.browser.version = jQuery.browser.majorVersion;
})(jQuery);

/**
 * 获取浏览器信息
 * @return {string} result 低版本chrome返回"oldChrome",IE返回"ie"
 */
function getBrowserInfo() {
    var agent = navigator.userAgent,
        regChrome = new RegExp(/chrome\/[\d.]+/gi),
        result = ""
    if (regChrome.test(agent)) {
        var version = Number(agent.match(regChrome)[0].split("/")[1].split(".")[0])
        if (version < 59) {
            result = "oldChrome"
        }
    } else if ((agent.indexOf('MSIE') >= 0) || (agent.indexOf('Trident') >= 0)) {
        result = "ie"
    }
    return result
}

//检查浏览器，如果是IE，则建议更换成Chrome；如果是版本号低于59的Chrome，则建议升级。
function checkBrowser() {
    var browserInfo = getBrowserInfo()
    var tips = {
        oldChrome: { top: "您的Chrome浏览器版本过低，新一代的某些功能可能无法正常使用，请安装建议版本，", form: "您的浏览器版本过低，可能影响功能使用，请看顶部提示信息。" },
        ie: { top: "您正在使用IE浏览器，新一代的某些功能可能无法正常使用，建议安装Chrome浏览器，", form: "新一代的某些功能可能无法在IE中正常使用，请看顶部提示信息。" },
    }
    if (browserInfo !== "") {
        $("#versionTip").text(tips[browserInfo].top)
        $("#formBrowserUpdateTip").text(tips[browserInfo].form)
        $("#topBrowserUpdateTip").css({ display: "inline-block" })
        $("#formBrowserUpdateTip").css({ display: "inline-block" })
    }
}

/**
 * 打开软件下载弹框，切换到下载浏览器的面板
 * @return {void}
 */
function openBrowserDownloadPanel() {
    $("#pdaDownload").click();
    var openDlPanelTimer = setInterval(function() {
        var contentDocument = $($("iframe")[0].contentWindow.document)
        var targetTab = contentDocument.find('.nav li:eq(1)');
        if (targetTab.length !== 0) {
            //tab方法在下载页本身能正常使用，但通过父页面中执行无效，故新增setUniqueEleActive方法
            //targetTab.tab('show')
            setUniqueEleActive(targetTab, "active")
            setUniqueEleActive(contentDocument.find("#chrome"), "active")
            clearInterval(openDlPanelTimer)
            /**
             * 目标元素增加样式类，其兄弟元素均删除该样式类
             * @param {jQuery Object} ele jQuery对象
             * @param {string} className 样式类名
             * @return {void}
             */
            function setUniqueEleActive(ele, className) {
                ele.siblings().each(function() {
                    $(this).removeClass(className)
                })
                ele.addClass(className)
            }
        }
    }, 100)
}

// modified by Lemon on 20210329 暂时去掉这个浏览器版本提示
$(".brand").after('<span id="topBrowserUpdateTip" style="float:left;width:440px;display:none;margin-left:10px;padding-top:4px;color:#f00;font-size:14px;"><span id="versionTip"></span>点击<span class="link" style="color:#2196F3;cursor: pointer;" onclick="openBrowserDownloadPanel()">此处</span>打开下载页。</span>')
$(".footer").css({position:"relative"}).prepend('<span id="formBrowserUpdateTip" style="position:absolute;left:0;width:100%;text-align:center;top:-35px;color:#f00;display:none"></span>')
checkBrowser()
