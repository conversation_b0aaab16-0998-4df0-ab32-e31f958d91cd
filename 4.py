import cx_Oracle
import pandas as pd


def get_db_connection():
    dsn_tns = cx_Oracle.makedsn('10.194.69.26', '1521', service_name='gkxt')
    return cx_Oracle.connect(user='c##zxjtotal', password='Gzyqzxj2019.', dsn=dsn_tns)


def sync_excel_to_oracle(excel_path):
    conn = None
    cursor = None
    try:
        # 1. 读取Excel
        df = pd.read_excel(excel_path, dtype={0: str})
        df = df.where(pd.notnull(df), None)

        if df.shape[1] < 7:
            raise ValueError("Excel需要包含至少7列数据")

        # 2. 建立数据库连接
        conn = get_db_connection()
        cursor = conn.cursor()

        # 3. 查询已有的 (khbh, khdw) 对
        existing_keys = set()
        # 提取非空的KH编号和单位
        key_pairs = df.iloc[:, [0, 3]] \
                      .dropna(how='any') \
                      .astype(str) \
                      .apply(lambda row: (row[0].strip(), row[1].strip()), axis=1) \
                      .unique().tolist()

        batch_size = 1000
        for i in range(0, len(key_pairs), batch_size):
            batch = key_pairs[i:i + batch_size]
            # 占位符
            ph = ",".join([f":{j*2+1},:{j*2+2}" for j in range(len(batch))])
            # 构造平铺后的参数列表 [khbh1,khdw1, khbh2,khdw2, ...]
            flat_params = [item for pair in batch for item in pair]
            sql = f"""
                SELECT khbh, khdw
                  FROM TB_ZLGK_FWZLGL_KFGZ_KHGL_KHBZ
                 WHERE (khbh, khdw) IN (
                   {', '.join(['(:%d, :%d)' % (j*2+1, j*2+2) for j in range(len(batch))])}
                 )
            """
            cursor.execute(sql, flat_params)
            for row in cursor:
                existing_keys.add((row[0].strip(), row[1].strip()))

        # 4. 预处理函数
        def process_record(row_data, row_num):
            try:
                khbh = str(row_data[0]).strip()
                khdw = str(row_data[3]).strip() if row_data[3] is not None else None
                if not khbh or not khdw:
                    raise ValueError("khbh 和 khdw 都不能为空")
                return {
                    'KHBH': khbh,
                    'KHNR': str(row_data[1]) if row_data[1] is not None else None,
                    'KHJE': str(row_data[2]).strip() if row_data[2] is not None else None,
                    'KHDW': khdw,
                    'ZBJX': str(row_data[4]) if row_data[4] is not None else None,
                    'BZ':   str(row_data[5]) if row_data[5] is not None else None,
                    'KHBZ': str(row_data[6]) if row_data[6] is not None else None
                }
            except Exception as e:
                raise ValueError(f"第{row_num}行数据处理失败: {e}")

        # 5. 分拆更新与插入数据
        update_data = []
        insert_data = []
        error_log = []

        for idx, row in enumerate(df.itertuples(index=False), start=1):
            try:
                rec = process_record(row, idx)
                key = (rec['KHBH'], rec['KHDW'])
                if key in existing_keys:
                    update_data.append(rec)
                else:
                    insert_data.append(rec)
            except Exception as e:
                error_log.append(str(e))

        # 6. 批量更新
        if update_data:
            cursor.executemany("""
                UPDATE TB_ZLGK_FWZLGL_KFGZ_KHGL_KHBZ
                   SET KHNR = :KHNR,
                       KHJE = :KHJE,
                       ZBJX = :ZBJX,
                       BZ   = :BZ,
                       KHBZ = :KHBZ
                 WHERE KHBH = :KHBH
                   AND KHDW = :KHDW
            """, update_data, batcherrors=True)

        # 7. 批量插入
        if insert_data:
            cursor.executemany("""
                INSERT INTO TB_ZLGK_FWZLGL_KFGZ_KHGL_KHBZ
                  (KHBH, KHNR, KHJE, KHDW, ZBJX, BZ, KHBZ)
                VALUES
                  (:KHBH, :KHNR, :KHJE, :KHDW, :ZBJX, :BZ, :KHBZ)
            """, insert_data, batcherrors=True)

        # 8. 错误输出
        if error_log:
            print("预处理异常：")
            print("\n".join(error_log))

        batch_errors = cursor.getbatcherrors() or []
        if batch_errors:
            print("数据库操作异常：")
            for err in batch_errors:
                print(f"第{err.offset+1}行: ORA-{err.code} {err.message}")

        conn.commit()
        print(f"同步完成：更新 {len(update_data)} 条，插入 {len(insert_data)} 条。")

    except Exception as e:
        if conn:
            conn.rollback()
        print("操作终止：", e)
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


if __name__ == "__main__":
    sync_excel_to_oracle("222.xlsx")
