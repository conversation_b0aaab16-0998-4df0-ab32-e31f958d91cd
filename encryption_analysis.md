# 加密文件及请求参数分析

## 分析目标
本分析旨在揭示新一代邮政查询系统中的加密机制，特别关注以下内容：
1. `/cas/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js` 核心加密文件
2. `enable_3bg4b5fckQas` 和 `3bg4b5fckQasP` Cookie
3. `MG9Olv7Z` 请求参数的生成过程

## 核心发现

通过对日志和捕获到的JavaScript文件分析，发现以下关键内容：

### 主要加密文件
- `/cas/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js` - 主要混淆加密文件
- `/cas/js/aes/aes-min.js` - AES加密库
- `/cas/js/aes/jdpt_web_aes.js` - 网站AES加密实现

### 关键函数
1. `function _$jX(_$gk,_$fC,_$ck)` - 这是主要的混淆函数，出现在`MWDdIri1zTyE.632fab5.js`文件中
2. `Z8XHJJY.bmF0aXZlRmlVyUHJ()` - 这个函数在eval调用中频繁出现，可能负责生成Cookie或处理加密

### Cookie分析
在日志中观察到两个关键Cookie：
1. `enable_3bg4b5fckQas=true` - 这个Cookie似乎是启用加密功能的标志
2. `3bg4b5fckQasP` - 包含一个非常长的Base64编码值，这个值在每次请求中都会变化，很可能是加密的时间戳或会话ID

```
Cookie示例:
3bg4b5fckQasP=0fEjqBEvMkBPye4JVy3svKPFGDmLL3SMuQZPEr4a0B6gS6PEcsUjmo7y6ckGNZNAJZFz.A2aXztiwzstbwZrow5AC_HWQGeruEKM3cDYqGWKJD.rm3AqvtvO0sNH6cTLpkUx5FFnscxtjd9WyY19Q3cfegDHyZNuKiFa5NyucH2YGPQE5qJa7WTKELtcMXV5Ykl.c6XbFanstc5JswKLOpRjRruOpdIisdaZoR4ph1IwPvQu1dn0RByxYYoopt31tGVXCyJV555omz1.tgno64d0W13fEhppKQz5WfxT_25CXSgxd8.eahMQgpm._2HI8emTw8ORIF9oq.qhlCtw0LEeMVgywZbFko8ZM1n1n8M67.GZn1EJIXI.CWu5jonbxRziDE.jaN9qkc.ajTNLrXCkOpCPxvRWwcKp7wAbtm_pk6saVqgT4Xz8AbcxJ9I4y
```

### 加密流程分析

根据捕获到的eval调用和JavaScript执行流程，推断加密过程如下：

1. 页面加载时，首先加载核心加密文件`MWDdIri1zTyE.632fab5.js`
2. 该文件通过eval执行一系列混淆代码，设置`enable_3bg4b5fckQas=true` Cookie
3. 随后生成复杂的`3bg4b5fckQasP` Cookie值，这个值可能包含了时间戳、随机数等信息
4. 当发送API请求时，这些Cookie值被用来生成`MG9Olv7Z`参数

### 函数调用链

经分析，加密参数生成过程可能如下：
```
页面加载 -> 加载MWDdIri1zTyE.632fab5.js -> 执行_$jX函数 -> 
通过eval执行Z8XHJJY.bmF0aXZlRmlVyUHJ() -> 
生成Cookie -> 使用Cookie值生成MG9Olv7Z参数 -> 请求API
```

### 混淆技术分析

`MWDdIri1zTyE.632fab5.js`文件使用了以下混淆技术：
1. 变量名混淆（使用_$开头的变量名）
2. 函数嵌套和闭包
3. 字符编码转换
4. Base64编码/解码
5. 使用eval动态执行代码

## 破解建议

要模拟生成正确的请求参数，建议采取以下步骤：

1. 在请求前设置相同的Cookie(`enable_3bg4b5fckQas=true`)
2. 拦截并分析`3bg4b5fckQasP` Cookie的生成过程
3. 模拟相同的流程生成`MG9Olv7Z`参数
4. 如果需要完全理解加密算法，建议对`_$jX`函数进行逐行分析和调试

## 进一步研究方向

1. 使用浏览器开发者工具设置断点，追踪`MG9Olv7Z`参数的精确生成位置
2. 对`3bg4b5fckQasP` Cookie的值进行解码分析，尝试理解其结构
3. 对`bmF0aXZlRmlVyUHJ`函数（可能是Base64编码的函数名）进行解码和分析
4. 更精确地捕获网络请求中的`MG9Olv7Z`参数并分析其变化规律 