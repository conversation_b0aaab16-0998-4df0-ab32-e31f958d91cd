
//二维码登录相关功能

var apiUrl = '' //api地址配置
var countdownTime = 60 //轮询时间
var scanTimer //状态检测定时器
var countTimer //倒计时定时器
var scanCodeId = '' // 二维码codeId
var statusResCodeId = '' //二维码登录状态接收的codeId

//获取二维码ID接口

function qrLoginGetCodeId() {
    return	$.ajax({
        type: 'GET',
        url: apiUrl+"/jdpt/third/qrLoginGetCodeId",
        error: function () {}
    });
}
// 处理用户登录
function handleUserLogin(statusRes) {
     // 登录状态正确 中断检查
    initProcess()
    statusResCodeId = statusRes.codeId
    var userList = statusRes.slacct.split(',')
    if (userList.length > 1) {
        // 至少俩个用户
        chooseUser(userList)
    } else {
        //直接登录
        var p = { codeId: statusRes.codeId , usr: ''}
        qrLoginGoHome(p)
    }
}
//二维码登录状态查询接口
function qrLoginCheckStatus(checkExpire,p) {
    $.ajax({
        type: 'GET',
        url: apiUrl + "/jdpt/third/qrLoginCheckStatus?codeId=" + scanCodeId,
        success: function (statusRes) {
            var status = statusRes.status
            //-1：扫码成功：未绑定账号 -2 : 未扫码登录  -3 ；二维码已过期
            if (status !== -2) {
                openRefresh('扫码已完成')
            }
            if (status == 0) {
                // 切换方案
                checkExpire ? qrLoginGoHome(p) :  handleUserLogin(statusRes)
            } else {
                // 提示
                status !== -2 ? setScanMsg(statusRes.msg) : ''
            }
        },
        error: function () { }
    });
}
//CAS登录重定向
function qrLoginGoHome(p) {
    var redirect = apiUrl + "/jdpt/third/qrLoginGoHome?codeId=" + p.codeId + "&usr=" + p.usr
    window.location.href = redirect

};
function chooseUserSubmit (v, h, f) {
    if(v==1){
        //用户选择
        var userVal = $('input:radio[name="userName"]:checked').val();
        var p = { codeId: statusResCodeId, usr: userVal }
        // 检测redis 3min 是否过期
        qrLoginCheckStatus(true,p)
       
    }
    return true
}

// 多用户选择
function chooseUser(arr) {
    var radioHtml = ''
    for (var index = 0; index < arr.length; index++) {
        var user = arr[index]
        radioHtml +=  "<label style='display: inline-block;margin:5px'><input class='userName' name='userName'  type='radio' value="+user+"><span style='vertical-align: sub;margin:10px'>"+user+"</span></label>"
    }
    var html = '<div class="user-box" style="padding-left: 20px;">'+radioHtml+'</div>';
    var button = { '确定': 1}
    $.jBox(html, { title: "请选择用户工号", buttons: button, submit: chooseUserSubmit,width : 350,height : 200,opacity: 0.8, });
    $('input:radio').eq(0).attr('checked', true);
}
//通过codeId获取二维码图片
function getQRCodeImg(apiPath) {
    apiUrl = apiPath
    qrLoginGetCodeId().then(function (codeRes) {
        if (codeRes.status == 0) {
            scanCodeId = codeRes.codeId
            $('.scan-img').attr('src', apiUrl+"/jdpt/third/qrLoginCreateQRCodeImage?codeId="+scanCodeId)
            scanTimeout()
        }
    })

}
// 二维码生成后 开启60s扫码等待
function scanTimeout() {
    initProcess()
    countdownTime = 60
    $('.scancodeInvalid').hide();
    // 2s 查一次状态
    scanTimer = setInterval(function () {
        qrLoginCheckStatus(false,{})
    }, 2000);
    // 倒计时1min
    countTimer= setInterval(function () {
        countdownTime--
        if (countdownTime < 1) {
            openRefresh('二维码已失效')
        }
        var countVal = countdownTime < 1 ? '':' '+countdownTime +'s'
        $('#timeCount').text(countVal)
    }, 1000);
   
}
// 开启点击刷新层
function openRefresh(msg) {
    $('.scancodeInvalid').show();
    $('.expire-time').text(msg)
    $('#timeCount').text('')
    initProcess()
}
//重新开始
function refresh() {
    closeScanMsg()
    initProcess()
    getQRCodeImg(apiUrl)
}
//初始化
function initProcess() {
    clearTimeout(scanTimer)
    clearTimeout(countTimer)
}
//二维码登录入口
function qrcodeLogin(apiPath) {
   
    getQRCodeImg(apiPath)
}
function closeScanMsg() {
    $('#scan_msg').text('')
    $('#scan_msg').hide()
}
function setScanMsg(msg) {
    $('#scan_msg').show()
    $('#scan_msg').text(msg)
}