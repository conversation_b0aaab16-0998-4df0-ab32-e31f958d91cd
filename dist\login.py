import asyncio
from playwright.async_api import async_playwright
import ddddocr
import logging
from tabulate import tabulate
from datetime import datetime
import os  

import requests

# WXPusher 配置
app_token = "AT_a8J0jBzJ53Yy2BfQw509NQIORKvbpoVa"  # 在WXPusher后台获取
uids = []  # 接收消息的用户UID，可以是多个
topic_ids = [36793]  # 如果使用主题推送，可以填写主题ID


logging.basicConfig(level=logging.INFO, format="[%(asctime)s][%(levelname)s] %(message)s")



# 推送消息函数
def send_wxpusher_message(app_token, uids, content, topic_ids=None):
    url = "https://wxpusher.zjiecode.com/api/send/message"
    payload = {
        "appToken": app_token,
        "content": content,
        
        "summary":"在局邮件逾限情况",#消息摘要，显示在微信聊天页面或者模版消息卡片上，限制长度20(微信只能显示20)，可以不传，不传默认截取content前面的内容。
        "contentType": 2,  # 2 表示 HTML 消息
        "uids": uids,
    }
    if topic_ids:
        payload["topicIds"] = topic_ids

    response = requests.post(url, json=payload)
    if response.status_code == 200:
        print("消息发送成功:", response.json())
    else:
        print("消息发送失败:", response.status_code, response.text)


# 生成竖式 HTML 表格
def generate_html_table(custom_mapping, data):
    # 获取当前时间并格式化为 yyyy-mm-dd hh:mm:ss
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    # 构建 HTML 表格
    html_table = f"""
    <style>
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f4f4f4;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .red-row {{
            color: red;  /* 红色文字 */
        }}
    </style>
    <p>截止时间: {current_time}</p>  <!-- 显示截止时间 -->
    
    <table>
        <thead>
            <tr>
                <th>指标</th>
    """

    # 添加列标题
    for i in range(len(data)):
        html_table += f"<th>数据</th>"
    html_table += "</tr></thead><tbody>"

    # 添加竖式数据，并标记第七行数据为红色
    for row_idx, (header, index) in enumerate(custom_mapping.items()):
        html_table += "<tr>"
        html_table += f"<th>{header}</th>"  # 每行的 header
        for col_idx, row_data in enumerate(data):
            # 第七行数据标红
            if row_idx == 6:  # 第七行
                html_table += f'<td class="red-row">{row_data[index]}</td>'
            else:
                html_table += f"<td>{row_data[index]}</td>"
        html_table += "</tr>"

    html_table += "</tbody></table>"
    return html_table

async def simulate_login(username, password, max_retries=5):
    # 使用 Playwright 启动浏览器
    async with async_playwright() as p:
        browser = await p.firefox.launch(headless=True, args=["--no-sandbox", "--disable-setuid-sandbox"])
        
        # 创建一个浏览器上下文，设置忽略 HTTPS 错误
        context = await browser.new_context(bypass_csp=True,ignore_https_errors=True)
        page = await context.new_page()
        
                # 设置 User-Agent 模拟正常浏览器
        await context.set_extra_http_headers({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "en-US,en;q=0.5",
            "Referer": "https://**********/cas/login"
        })

        # 禁用 Playwright 默认的自动化标识
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
        """)
        # 监听浏览器的控制台日志
        def handle_console(msg):
            logging.info(f"Console message: {msg.text}")
        
        #page.on('console', handle_console)
        try:
            count=0
            login=True
            while login and count<6:
                # 打开登录页面
                login_url = "https://**********/cas/login"
                await page.goto(login_url)
                logging.info("打开登录页面")

                # 填写用户名和密码
                await page.fill('#username', username)
                await page.fill('#password', password)

                # 获取验证码图片的 src
                captcha_img = await page.query_selector('div#sec_code > img')
                captcha_src = await captcha_img.get_attribute('src')
                logging.info(f"验证码图片地址: {captcha_src}")

                # 下载验证码图片
                image_path = 'captcha.jpg'
                await captcha_img.screenshot(path=image_path)
                logging.info(f"保存验证码图片到: {image_path}")

                # 使用 ddddocr 识别验证码
                ocr = ddddocr.DdddOcr()
                with open(image_path, "rb") as f:
                    img_bytes = f.read()
                captcha = ocr.classification(img_bytes).strip()
                logging.info(f"验证码识别结果: {captcha}")

                # 填写验证码
                await page.fill('#security', captcha)

                # 提交表单（模拟点击登录按钮）
                submit_button = await page.query_selector('input#login[type="submit"]')
                await submit_button.click()

                
                # 等待页面跳转完成
                try:
                    await page.wait_for_selector("li[name='首页']", timeout=10000)  # 等待首页标识加载
                    logging.info("登录成功")
                    login=False
                    # 删除验证码图片
                    if os.path.exists(image_path):
                        os.remove(image_path)
                        logging.info(f"验证码图片已删除: {image_path}")
                    else:
                        logging.warning(f"验证码图片未找到: {image_path}")
                except Exception as e:
                    logging.error(f"登录失败或超时: {e}")
                    count += 1
            # 检查是否登录成功
            content = await page.content()
            if "首页" in content:
                #logging.info("登录成功")
                # 获取 Cookies
                cookies = await page.context.cookies()
                session_cookies = {cookie['name']: cookie['value'] for cookie in cookies}
                #logging.info(f"Session Cookies: {session_cookies}")
                # 获取 jdpt.session.id
                jdpt_session_id = session_cookies.get('jdpt.session.id', None)
                if jdpt_session_id:
                    logging.info(f"获取到的 jdpt.session.id: {jdpt_session_id}")
                    # 将 jdpt.session.id 添加到 cookies
                    await context.add_cookies([{
                        "name": "jdpt.session.id",
                        "value": jdpt_session_id,
                        "domain": "**********",  # 确保使用正确的域名
                        "path": "/"  # 可以设置 path，确保 cookie 对整个域名有效
                    }])
                logging.info("成功将 jdpt.session.id 添加到 cookies")
                # 在这里模拟点击查询按钮并输入关键字
                

                # 访问目标页面
                await page.goto('https://**********/process-web/a/mpm/mpmProCenterMailMonitoring/tolist')


                # 4. 等待页面跳转
                await page.wait_for_selector('#provinceCode', timeout=10000)  # 等待下拉框可见
                
                # content = await page.content()
                # logging.info(content)
                logging.info("页面跳转成功")
                
                # 选择“按处理中心”单选框
                await page.click('#tjfsStyle3')  # 选择第三个单选框
                
                

                # 选择省份 (广东省)
                await page.click('[data-id="provinceCode"]')  # 点击省份下拉按钮

                # 输入搜索关键词 "广东" 来过滤选项
                await page.fill('.bs-searchbox input[type="text"]', '广东')  # 填入搜索框

                # 等待下拉菜单出现并且筛选出“广东省(440000)”选项
                await page.wait_for_selector('.dropdown-menu.inner li a span.text:has-text("广东省(440000)")')

                # 点击选择“广东省(440000)”选项
                await page.click('.dropdown-menu.inner li a span.text:has-text("广东省(440000)")')
                
                await page.wait_for_timeout(500)  # 等待 1 秒，确保元素稳定
               # 选择城市 (广州市)
                city_dropdown = page.locator('[data-id="cityCode"]')  # 定位到城市下拉框
                await city_dropdown.click()  # 点击城市下拉按钮
                
                await page.click('.dropdown-menu.inner li a span.text:has-text("广州市(440100)")')  # 点击“广州市”
                await page.wait_for_timeout(500)  # 等待 1 秒，确保元素稳定
                # 选择组织 (广州广航)
                org_dropdown = page.locator('[data-id="orgCode"]')  # 定位到组织下拉框
                await org_dropdown.click()  # 点击组织下拉按钮
                
                await page.click('.dropdown-menu.inner li a span.text:has-text("广州广航(特快进出)(51000061)")')  # 点击“广州广航”                
                # 此时，选择已经完成，您可以进行其他操作或验证选择是否成功
                # 点击查询按钮
                await page.click('#btnSubmit')  # 使用 id 来定位按钮
                # 等待包含文本 "24-48小时" 的元素加载
                await page.wait_for_selector('.th-inner.sortable.both:has-text("24-48小时")')
                
                
                # 获取页面内容
                # html_content = await page.content()  # 获取 HTML 内容
                # print(html_content)  # 打印网页 HTML 内容
                
                # 获取并打印 <th> 标签中的 <div class="th-inner"> 的值
                # th_inner_value = await page.locator('th[data-field="overratedNum10Sort"] .th-inner').inner_text()
                # print(f"24-48小时: {th_inner_value}件")
                # 提取表头
                headers = await page.query_selector_all('#myContentTable thead th .th-inner')
                header_texts = [await header.inner_text() for header in headers]  # 获取表头文本

                # 提取表格行数据
                rows = await page.query_selector_all('#myContentTable tbody tr')
                data = []
                for row in rows:
                    cells = await row.query_selector_all('td')
                    cell_texts = [await cell.inner_text() for cell in cells]  # 获取每个单元格的文本
                    data.append(cell_texts)

                #print("Headers:", header_texts)  # 打印表头文本
                #print("Table Data:", data)  # 打印表格数据
                
                # 自定义表头和对应的字段索引（注意索引从 0 开始）
                custom_mapping = {
                    # '序号': 0,
                    # '省份': 1,
                    # '地市': 2,
                    # '处理中心': 3,
                    '已入局未发运': 4,
                    '已逾限邮件量': 5,
                    '已逾限比重': 6,
                    '已解车未扫描': 7,
                    '已扫描未配发':8,
                    '已配发未封车':9,
                    '24小时以上邮件量':10,
                    '24小时以上比重': 11,
                    '24-48小时':12,
                    '48-72小时':13,
                    '72小时以上':14,
                    '12-24小时':15,
                    '8-12小时':16,
                    '4-8小时':17,
                    '2-4小时':18,
                    '0-2小时':19,
                    '7天以上':20
                    
                }

                # 根据自定义表头映射，重新排序数据
                vertical_table = []
                for header, index in custom_mapping.items():
                    row = [header]  # 表头
                    for row_data in data:
                        row.append(row_data[index])  # 按映射索引取值
                    vertical_table.append(row)

                # 设置竖式展示的表头
                vertical_headers = ["名称"] + ["数据"]#[f"数据 {i + 1}" for i in range(len(data))]

                # 绘制竖式表格
                table = tabulate(vertical_table, headers=vertical_headers, tablefmt="grid")
                print(table)
                if data[0][10]!='0':
                    html_content = generate_html_table(custom_mapping, data)
                    # 推送表格内容
                    send_wxpusher_message(app_token, uids, f"<h2>在局邮件逾限情况</h2>{html_content}", topic_ids)
                else:
                    print('不推送')
                # await page.wait_for_selector('#provinceCode', timeout=100000)  # 等待下拉框可见
            else:
                logging.error("登录失败，可能是验证码错误或其他问题")
                return None

        except Exception as e:
            logging.error(f"错误: {e}")
        finally:
            logging.info("登录流程完成，浏览器保持开启，继续操作。")
            pass  # 暂时不关闭浏览器，等待手动关闭

# 调用函数
# username = "GH003035"
# password = "Gzamc#381802"
with open('user.txt', "r") as file:
    lines = file.readlines()
    if len(lines) >= 3:  # 确保文件至少有三行
        data = lines[2].strip().split(',')  # 获取第三行数据
        if len(data) >= 3:  # 确保数据足够
            username = data[1]  # 第二个位置
            password = data[2]  # 第三个位置
            print(f"Username: {username}, Password: {password}")
        else:
            print("第三行数据格式不正确，无法获取账号和密码")
    else:
        print("文件行数不足，无法读取第三行")
asyncio.get_event_loop().run_until_complete(simulate_login(username, password))
