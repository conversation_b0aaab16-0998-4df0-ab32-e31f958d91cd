import asyncio
import csv
import io
import json
import os
import socket
import sys
import tkinter as tk
import traceback
from urllib import parse

import aiohttp
from bs4 import BeautifulSoup
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from os import path
import random
import cx_Oracle
import requests, time
import datetime

import pandas
import re
import threading
import concurrent.futures
from playwright.async_api import async_playwright
from tkcalendar import DateEntry

from tool import Tool
from itertools import product
orauser = 'hgzs'
orapass = 'gjyj_hgzs'
oradb = '***********:1521/gjinter'
# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

# 设置 Oracle 客户端路径
#os.environ['PATH'] = os.path.join(current_dir, 'instantclient_12_1')



def get_input_value(soup, name, default=''):
    element = soup.find('input', attrs={'name': name})
    return element['value'] if element else default

def getmailmg(mailno):

    # cnt = cursor.execute("select count(1) from tb_mail_info_head@gjpy72 where v_mailno=" + "'" + mailno + "'")
    # n = str(cnt.fetchall()[0])
    # n = re.findall(r'\d+', str(n))[0]
    n = '0'
    try:
        with cx_Oracle.connect(orauser, orapass, oradb) as connection:
            with connection.cursor() as cursor:
                sql = "SELECT COUNT(1) FROM tb_mail_info_head@gjpy72 WHERE v_mailno = :1"
                cursor.execute(sql, (mailno,))
                cnt = cursor.fetchone()[0]  # 获取计数结果
                n = str(cnt)
                n = re.findall(r'\d+', n)[0]

    except Exception as e:
        print(f"Error fetching mail count for mailno: {mailno}")
        traceback.print_exc()
    # print (n)
    new_dataY = []  # 新建一个列表
    new_dataN = []  # 新建一个列表
    if n == '0':
        rm = random.randint(10, 99)
        curr_time = datetime.datetime.now().strftime('%Y%m%d%H%M%S-%f')

        guid = 'pqxyd-pac101' + '-' + curr_time + str(rm)
        # print (guid)
        requests.packages.urllib3.disable_warnings()
        if '出口' in selected2.get():
            url = 'https://**********/intprep-web/a/intprep/exchangeReplace/list'
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',

                'Connection': 'keep-alive',
                'Content-type': 'application/x-www-form-urlencoded',
                'Host': '**********',
                'Referer': 'https://**********/intprep-web/a/intprep/exchangeReplace/list',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
                'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
            }
            data = {
                'itemId': mailno,
                'errorInfo': ''
            }
            response = session.post(url, headers=headers, params=data, verify=False)
            r = response.text
            html = BeautifulSoup(r, 'lxml')

            signway = html.find('tbody', {'id': 'signwaybillreturned_tbody'})
            if 'cusDeclareInfoOrigin' in str(signway):
                sbxxly = html.find('select', {'name': 'cusDeclareInfoOrigin'})  # 申报信息来源
                sbxxly = re.findall(r'selected="" value="(.+?)"', str(sbxxly))
                if sbxxly:
                    sbxxly = sbxxly[0]
                else:
                    sbxxly = ''
                sbhg = html.find('input', attrs={'name': 'custCode'})['value']  # 申报海关
                yzqymc = html.find('input', attrs={'name': 'postEntName'})['value']  # 邮政企业名称
                yzqydm = html.find('input', attrs={'name': 'postEntCode'})['value']  # 邮政企业代码
                js = html.find('input', attrs={'name': 'itemNum'})['value']  # 件数
                hgyjtsbz = html.find('select', attrs={'name': 'custSpecItemFlag'})  # 海关邮件特殊标志
                hgyjtsbz = re.findall(r'selected="" value="(.+?)"', str(hgyjtsbz))
                if hgyjtsbz:
                    hgyjtsbz = hgyjtsbz[0]
                else:
                    hgyjtsbz = ''
                jck = html.find('select', attrs={'name': 'impExp'})  # 进/出口
                jck = re.findall(r'selected="" value="(.+?)"', str(jck))
                if jck:
                    jck = jck[0]
                else:
                    jck = ''
                hggjdm = html.find('select', attrs={'name': 'nationCodess'})  # 海关国家代码
                nationCode = html.find('input', attrs={'name': 'nationCode'})['value']  # 原寄局list表
                hggjdm = re.findall(r'selected="" value="(.+?)"', str(hggjdm))
                if hggjdm:
                    hggjdm = hggjdm[0]
                else:
                    hggjdm = ''
                jdj = html.find('input', attrs={'name': 'destOrgName'})['value']  # 寄达局
                sblb = html.find('select', attrs={'name': 'custDeclareClassCode'})  # 申报类型
                sblb = re.findall(r'selected="" value="(.+?)"', str(sblb))
                if sblb:
                    sblb = sblb[0]
                else:
                    sblb = ''
                yjzl = html.find('select', attrs={'name': 'bizSect'})  # 邮件种类
                yjzl = re.findall(r'selected="" value="(.+?)"', str(yjzl))
                if yjzl:
                    yjzl = yjzl[0]
                else:
                    yjzl = ''
                yjj = html.find('input', attrs={'name': 'originOrgName'})['value']  # 原寄局
                crjsj = html.find('input', attrs={'name': 'crossBoundTime'})['value']  # 出入境时间
                zjlx = html.find('select', attrs={'name': 'senderIdClassCode'})  # 证件类型
                zjlx = re.findall(r'selected="" value="(.+?)"', str(zjlx))
                if zjlx:
                    zjlx = zjlx[0]
                else:
                    zjlx = ''
                jjrzjhm = html.find('input', attrs={'name': 'snederIdNo'})['value']  # 寄件人证件号码
                jjrdh = html.find('input', attrs={'name': 'senderPhone'})['value']  # 寄件人电话
                jjrqymc = html.find('input', attrs={'name': 'senderCompany'})['value']  # 寄件人企业名称
                jjrxm = html.find('input', attrs={'name': 'senderName'})['value'].replace('NULL', 'NONAME')  # 寄件人姓名
                jjrdz = html.find('input', attrs={'name': 'senderAddr'})['value']  # 寄件人地址
                sjrzjlx = html.find('select', attrs={'name': 'recipientIdClassCode'})  # 收件人证件类型
                sjrzjlx = re.findall(r'selected="" value="(.+?)"', str(sjrzjlx))
                if sjrzjlx:
                    sjrzjlx = sjrzjlx[0]
                else:
                    sjrzjlx = ''
                sjrzjhm = html.find('input', attrs={'name': 'recipientIdNo'})['value']  # 收件人证件号码
                sjrdh = html.find('input', attrs={'name': 'recipientAddress'})['value']  # 收件人电话
                sjrqymc = html.find('input', attrs={'name': 'recipientCompany'})['value']  # 收件人企业名称
                sjrxm = html.find('input', attrs={'name': 'recName'})['value'].replace('"', '').replace("'",
                                                                                                        '')  # 收件人姓名
                sjrdz = html.find('input', attrs={'name': 'recAddress'})['value'].replace('"', '').replace("'",
                                                                                                           '')  # 收件人地址
                zl = html.find('input', attrs={'name': 'totalWeight'})['value']  # 重量
                zl = float(zl) * 0.001
                zl = str(zl)
                sbbz = html.find('select', attrs={'name': 'declareCurrType'})  # 申报币种
                sbbz = re.findall(r'selected="" value="(.+?)"', str(sbbz))
                if sbbz:
                    sbbz = sbbz[0]
                else:
                    sbbz = ''
                yzbz = html.find('input', attrs={'name': 'postageCurrType'})['value']  # 邮资币种

                yzaybzdjz = html.find('input', attrs={'name': 'postageFee'})['value']  # 邮资按邮币制的价值
                zywpmc = html.find('input', attrs={'name': 'contentDescription'})['value']  # 主要物品名称
                bz = html.find('input', attrs={'name': 'remTxt'})['value']  # 备注

                tbody = html.find('tbody', attrs={'id': 'btbody'})

                bizSerno = tbody.find_all('input', {'name': 'bizSerno'})
                bizName = tbody.find_all('input', {'name': 'bizName'})
                custDeclareSortCode = tbody.find_all('input', {'name': 'custDeclareSortCode'})
                goodsNum = tbody.find_all('input', {'name': 'goodsNum'})
                meterUnit = tbody.find_all('select', {'name': 'meterUnit'})
                meterUnit = re.findall(r'selected="" value="(.+?)"', str(meterUnit))
                declareTotalValue = tbody.find_all('input', {'name': 'declareTotalValue'})
                # specModel=tbody.find_all('input',{'name':'specModel'})
                sumtotalvalue = 0.00


                try:
                    with cx_Oracle.connect(orauser, orapass, oradb) as connection:
                        with connection.cursor() as cursor:
                            # 遍历 meterUnit 并插入 tb_mail_info_list
                            for k in range(len(meterUnit)):
                                Serno = re.findall(r'value="(.+?)"', str(bizSerno[k]))[0]
                                # Name = re.findall(r'value="(.+?)"', str(bizName[k]))[0].replace('NULL', 'NONAME')
                                matches = re.findall(r'value="(.+?)"', str(bizName[k]))
                                if matches:
                                    Name = matches[0].replace('NULL', 'NONAME')
                                else:
                                    Name = 'NONAME'

                                DeclareSortCode = re.findall(r'value="(.+?)"', str(custDeclareSortCode[k]))
                                DeclareSortCode = DeclareSortCode[0] if DeclareSortCode else '00000000'
                                Num = re.findall(r'value="(.+?)"', str(goodsNum[k]))[0]
                                Unit = meterUnit[k]
                                TotalValue = re.findall(r'value="(.+?)"', str(declareTotalValue[k]))[0]
                                sumtotalvalue += float(TotalValue)

                                insert_into_tb_mail_info_list(cursor, guid, mailno, Serno, Name, DeclareSortCode,
                                                              nationCode, Num, Unit, TotalValue)

                            # 将总价值转换为字符串并插入 tb_mail_info_head
                            sumtotalvalue = str(sumtotalvalue)
                            insert_into_tb_mail_info_head(cursor, guid, mailno, sbxxly, sbhg, yzqydm, yzqymc, jck,
                                                          nationCode, crjsj,
                                                          sblb, yjzl, yjj, jdj, jjrxm, jjrdz, jjrdh, jjrqymc, zjlx,
                                                          jjrzjhm,
                                                          sjrxm,
                                                          sjrdz, sjrdh, sjrqymc, sjrzjlx, sjrzjhm, zl, sbbz, yzbz,
                                                          yzaybzdjz, js,
                                                          zywpmc, bz, sumtotalvalue, hgyjtsbz)

                            # 提交事务
                            connection.commit()
                            print("Data inserted successfully.")

                except cx_Oracle.DatabaseError as e:
                    print("Database connection error:", e)
                    traceback.print_exc()

                tool.process_input(mailno + '新一代有信息')

            else:
                tool.process_input(mailno + '无信息')

        else:
            url = 'https://**********/intprep-web/a/intprep/impintcustmailbase/toEdit'
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',

                'Connection': 'keep-alive',
                'Content-type': 'application/x-www-form-urlencoded',
                'Host': '**********',
                'Referer': 'https://**********/intprep-web/a/intprep/impintcustmailbase/list',
                'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
                'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
            }
            data = {
                'flag': 'R',
                'showSpecial': '',
                'specialDestCodeStr': '',
                'myCustCode': '',
                'countryInfo': '',
                'weightPara': '',
                'orgCityCode': '',
                'lastInlandTransType': '',
                'itemId': mailno,
                'standLabel': '0',
                'isAuto': '1',
                'modifyName':'',
                'errorInfo': ''
            }
            response = session.post(url, headers=headers, params=data, verify=False)
            r = response.text
            html = BeautifulSoup(r, 'lxml')

            signway = html.find('tbody', {'id': 'signwaybillreturned_tbody'})
            if 'custCode' in str(signway):
                sbxxly = html.find('select', {'name': 'cusDeclareInfoOrigin'})  # 申报信息来源
                sbxxly = re.findall(r'selected="" value="(.+?)"', str(sbxxly))
                if sbxxly:
                    sbxxly = sbxxly[0]
                else:
                    sbxxly = '2'
                sbhg = html.find('input', attrs={'name': 'custCode'})['value']  # 申报海关
                yzqymc = html.find('input', attrs={'name': 'regionName'})['value']  # 邮政企业名称
                yzqydm = html.find('input', attrs={'name': 'regionCode'})['value']  # 邮政企业代码
                js = html.find('input', attrs={'name': 'itemNum'})['value']  # 件数
                hgyjtsbz = html.find('select', attrs={'name': 'custSpecItemFlag'})  # 海关邮件特殊标志
                hgyjtsbz = re.findall(r'selected="" value="(.+?)"', str(hgyjtsbz))
                if hgyjtsbz:
                    hgyjtsbz = hgyjtsbz[0]
                else:
                    hgyjtsbz = ''
                jck = 'I'
                hggjdm = html.find('select', attrs={'name': 'nationCodess'})  # 海关国家代码
                nationCode = html.find('input', attrs={'name': 'nationCode'})['value']  # 原寄局list表
                hggjdm = re.findall(r'selected="" value="(.+?)"', str(hggjdm))
                if hggjdm:
                    hggjdm = hggjdm[0]
                else:
                    hggjdm = ''
                jdj = html.find('input', attrs={'name': 'destOrgName'})['value']  # 寄达局
                sblb = html.find('select', attrs={'name': 'custDeclareClassCode'})  # 申报类型
                sblb = re.findall(r'selected="" value="(.+?)"', str(sblb))
                if sblb:
                    sblb = sblb[0]
                else:
                    sblb = ''
                yjzl = 'E'
                yjj = html.find('input', attrs={'name': 'originOrgName'})['value']  # 原寄局
                crjsj = html.find('input', attrs={'name': 'crossBoundTime'})['value']  # 出入境时间
                zjlx = html.find('select', attrs={'name': 'senderIdClassCode'})  # 证件类型
                zjlx = re.findall(r'selected="" value="(.+?)"', str(zjlx))
                if zjlx:
                    zjlx = zjlx[0]
                else:
                    zjlx = ''
                jjrzjhm = get_input_value(html, 'snederIdNo')  # 寄件人证件号码
                jjrdh = get_input_value(html, 'senderPhone')  # 寄件人电话
                jjrqymc = get_input_value(html, 'senderCompany')  # 寄件人企业名称
                jjrxm = html.find('input', attrs={'name': 'senderName'})['value'].replace('NULL', 'NONAME')  # 寄件人姓名
                jjrdz = html.find('input', attrs={'name': 'senderAddr'})['value']  # 寄件人地址
                sjrzjlx = html.find('select', attrs={'name': 'recipientIdClassCode'})  # 收件人证件类型
                sjrzjlx = re.findall(r'selected="" value="(.+?)"', str(sjrzjlx))
                if sjrzjlx:
                    sjrzjlx = sjrzjlx[0]
                else:
                    sjrzjlx = ''
                #sjrzjhm = html.find('input', attrs={'name': 'recipientIdNo'})['value']  # 收件人证件号码
                sjrzjhm=get_input_value(html, 'recipientIdNo')# 收件人证件号码
                sjrdh = html.find('input', attrs={'name': 'recipientAddress'})['value']  # 收件人电话
                #sjrqymc = html.find('input', attrs={'name': 'recipientCompany'})['value']  # 收件人企业名称
                sjrqymc = get_input_value(html, 'recipientCompany')  # 收件人企业名称
                sjrxm = html.find('input', attrs={'name': 'recName'})['value'].replace('"', '').replace("'",
                                                                                                        '')  # 收件人姓名
                sjrdz = html.find('input', attrs={'name': 'recAddress'})['value'].replace('"', '').replace("'",
                                                                                                           '')  # 收件人地址
                zl = html.find('input', attrs={'name': 'weight'})['value']  # 重量
                #zl = float(zl) * 0.001
                zl = str(zl)
                sbbz = html.find('select', attrs={'name': 'declareCurrType'})  # 申报币种
                sbbz = re.findall(r'selected="" value="(.+?)"', str(sbbz))
                if sbbz:
                    sbbz = sbbz[0]
                else:
                    sbbz = ''
                #yzbz = html.find('input', attrs={'name': 'postageCurrType'})['value']  # 邮资币种
                yzbz = html.find('select', attrs={'name': 'postageCurrType'})  # 邮资币种
                yzbz = re.findall(r'selected="" value="(.+?)"', str(yzbz))
                if yzbz:
                    yzbz = yzbz[0]
                else:
                    yzbz = ''
                yzaybzdjz = html.find('input', attrs={'name': 'postageFee'})['value']  # 邮资按邮币制的价值

                bz = html.find('input', attrs={'name': 'remTxt'})['value']  # 备注

                tbody = html.find('tbody', attrs={'id': 'btbody'})
                zywpmc = tbody.find('input', attrs={'name': 'contentbizName'})['value']  # 主要物品名称
                bizSerno = tbody.find_all('input', {'name': 'bizSerno'})
                bizName = tbody.find_all('input', {'name': 'bizName'})
                custDeclareSortCode = tbody.find_all('input', {'name': 'custDeclareSortCode'})
                goodsNum = tbody.find_all('input', {'name': 'goodsNum'})
                meterUnit = tbody.find_all('select', {'name': 'meterUnit'})
                meterUnit = re.findall(r'selected="" value="(.+?)"', str(meterUnit))
                declareTotalValue = tbody.find_all('input', {'name': 'declareTotalValue'})
                # specModel=tbody.find_all('input',{'name':'specModel'})
                sumtotalvalue = 0.00


                try:
                    with cx_Oracle.connect(orauser, orapass, oradb) as connection:
                        with connection.cursor() as cursor:
                            # 遍历 meterUnit 并插入 tb_mail_info_list
                            for k in range(len(meterUnit)):
                                Serno = re.findall(r'value="(.+?)"', str(bizSerno[k]))[0]
                                # Name = re.findall(r'value="(.+?)"', str(bizName[k]))[0].replace('NULL', 'NONAME')
                                matches = re.findall(r'value="(.+?)"', str(bizName[k]))
                                if matches:
                                    Name = matches[0].replace('NULL', 'NONAME')
                                else:
                                    Name = 'NONAME'

                                DeclareSortCode = re.findall(r'value="(.+?)"', str(custDeclareSortCode[k]))
                                DeclareSortCode = DeclareSortCode[0] if DeclareSortCode else '00000000'
                                Num = re.findall(r'value="(.+?)"', str(goodsNum[k]))[0]
                                Unit = meterUnit[k]
                                TotalValue = re.findall(r'value="(.+?)"', str(declareTotalValue[k]))[0]
                                sumtotalvalue += float(TotalValue)

                                insert_into_tb_mail_info_list(cursor, guid, mailno, Serno, Name, DeclareSortCode,
                                                              nationCode, Num, Unit, TotalValue)

                            # 将总价值转换为字符串并插入 tb_mail_info_head
                            sumtotalvalue = str(sumtotalvalue)
                            insert_into_tb_mail_info_head(cursor, guid, mailno, sbxxly, sbhg, yzqydm, yzqymc, jck,
                                                          nationCode, crjsj,
                                                          sblb, yjzl, yjj, jdj, jjrxm, jjrdz, jjrdh, jjrqymc, zjlx,
                                                          jjrzjhm,
                                                          sjrxm,
                                                          sjrdz, sjrdh, sjrqymc, sjrzjlx, sjrzjhm, zl, sbbz, yzbz,
                                                          yzaybzdjz, js,
                                                          zywpmc, bz, sumtotalvalue, hgyjtsbz)

                            # 提交事务
                            connection.commit()
                            print("Data inserted successfully.")

                except cx_Oracle.DatabaseError as e:
                    print("Database connection error:", e)
                    traceback.print_exc()

                tool.process_input(mailno + '新一代有信息')

            else:
                tool.process_input(mailno + '无信息')

    else:
        tool.process_input(mailno+'信息已存在，跳过')
        #print(mailno + '信息已存在，跳过')

async def getinmailtrace(parent,mailno,cookies):
    global L
    # print ('正在处理:'+mailno)
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/getCustomState'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Length': '20',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'DNT': '1',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        # 'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'itemId': mailno

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, data=data, verify=False)
    r = response.text
    #print(r)

    soup = BeautifulSoup(r, 'lxml')
    # 找到第一个 tbody 标签
    tbody = soup.find('tbody')
    # print(tbody)
    if tbody:
        # 找到 tbody 内的所有 td 标签，并取出第七个 td 的文本内容

        tds = tbody.find_all('td')
        if len(tds) >= 7:
            #seventh_td_content = tds[6].get_text()

            # print(seventh_td_content)

            agentcode=tds[3].get_text().strip()

            customScodes=tds[5].get_text().strip()
            if '是' in tds[7].get_text():
                dutyFlag = '1'
            else:
                dutyFlag = '0'

    latest_return_time = None
    tbody = soup.find_all('tbody')[1]  # Get the second tbody
    if tbody:
        tr = tbody.find('tr')
        if tr:
            td = tr.find_all('td')[1]
            if td:
                latest_return_time = datetime.datetime.strptime(td.get_text().strip(), '%Y-%m-%d %H:%M:%S')

    # Get the latest return time from the database
    with cx_Oracle.connect(orauser, orapass, oradb) as connection:
        with connection.cursor() as cursor:
            db_latest_return_time = get_latest_return_time_from_db(cursor, mailno)

            # Compare the dates and insert new data if necessary
            if latest_return_time and (not db_latest_return_time or latest_return_time >db_latest_return_time):
                tds = tr.find_all('td')
                returnStatus = tds[2].get_text().strip()
                returnInfo = tds[3].get_text().strip()
                print(mailno,returnStatus,dutyFlag,latest_return_time.strftime('%Y-%m-%d %H:%M:%S'),customScodes,returnInfo,agentcode)
                # insert_into_tb_mail_result(cursor, mailno, returnStatus, dutyFlag,latest_return_time.strftime('%Y-%m-%d %H:%M:%S'), customScodes, returnInfo,agentcode)
                await insert_data_via_api(mailno, returnStatus, dutyFlag, latest_return_time.strftime('%Y-%m-%d %H:%M:%S'), customScodes, returnInfo, agentcode)
                connection.commit()
                parent.after(0, tool.process_input(f"{mailno}有新回执"))
            else:
                parent.after(0, tool.process_input(f"{mailno}无新回执"))
    L += 1
    # parent.after(0, tool.update_L(str(L)))



async def getoutmailtrace(parent,mailno,cookies):
    global L
    #print ('正在处理:'+mailno)
    try:
        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/intproc-web/a/intproc/expcustomsquery/querydomunpexpcustoms'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        }
        data = {

            'itemId': mailno,
            'cusReleaseFlag': ''

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)
        #print(r)

        if jsonObj.get("detail")  and len(jsonObj["detail"]) > 0:

            customScodes = jsonObj["detail"][0]["custCode"]

            latest_return_time = datetime.datetime.strptime(jsonObj["detail"][0]["respTimeStr"], '%Y-%m-%d %H:%M:%S')
            returnStatus = jsonObj["detail"][0]["custReceiptCode"]
            returnInfo = jsonObj["detail"][0]["custReceiptInfo"]
            agentcode = jsonObj["detail"][0]["declareUnitCode"]
            if '否' in jsonObj["detail"][0]["taxFlag"]:
                dutyFlag = '0'
            else:
                dutyFlag = '1'
            # Get the latest return time from the database
            with cx_Oracle.connect(orauser, orapass, oradb) as connection:
                with connection.cursor() as cursor:
                    db_latest_return_time = get_latest_return_time_from_db(cursor, mailno)

                    # Compare the dates and insert new data if necessary
                    if latest_return_time and (not db_latest_return_time or latest_return_time > db_latest_return_time):
                        print(mailno, returnStatus, dutyFlag, latest_return_time.strftime('%Y-%m-%d %H:%M:%S'),
                              customScodes,
                              returnInfo, agentcode)
                        # insert_into_tb_mail_result(cursor, mailno, returnStatus, dutyFlag,latest_return_time.strftime('%Y-%m-%d %H:%M:%S'), customScodes, returnInfo,agentcode)
                        await insert_data_via_api(mailno, returnStatus, dutyFlag,
                                                  latest_return_time.strftime('%Y-%m-%d %H:%M:%S'),
                                                  customScodes, returnInfo, agentcode)
                        connection.commit()
                        parent.after(0, tool.process_input(f"{mailno}有新回执"))
                    else:
                        parent.after(0, tool.process_input(f"{mailno}无新回执"))
        else:
            parent.after(0, tool.process_input(f"{mailno}无出口回执信息"))
            pass

    except Exception as e:
        print("An error occurred while processing " + mailno)
        traceback.print_exc()
    L += 1
    # parent.after(0, tool.update_L(str(L)))


def insert_into_tb_mail_info_list(cursor, guid, mailno, Serno, Name, DeclareSortCode, nationCode, Num, Unit, TotalValue):
    sql = """
        INSERT INTO tb_mail_info_list@gjpy72 
        (v_guid, v_mailno, n_gno, v_gname, v_gmodel, v_codets, v_oricountry, n_qty, v_unit, n_value) 
        VALUES (:1, :2, :3, :4, '', :5, :6, :7, :8, :9)
    """
    try:
        cursor.execute(sql, (guid, mailno, Serno, Name, DeclareSortCode, nationCode, Num, Unit, TotalValue))
    except Exception as e:
        print(f"Error inserting into tb_mail_info_list for mailno: {mailno}")
        traceback.print_exc()


def insert_into_tb_mail_info_head(cursor, guid, mailno, sbxxly, sbhg, yzqydm, yzqymc, jck, nationCode, crjsj, sblb,
                                  yjzl, yjj, jdj, jjrxm, jjrdz, jjrdh, jjrqymc, zjlx, jjrzjhm, sjrxm, sjrdz, sjrdh,
                                  sjrqymc, sjrzjlx, sjrzjhm, zl, sbbz, yzbz, yzaybzdjz, js, zywpmc, bz, sumtotalvalue,
                                  hgyjtsbz):
    sql = """
        INSERT INTO tb_mail_info_head@gjpy72 
        (v_guid, v_apptype, d_apptime, v_mailno, v_declsource, v_dport, v_agentcode, v_agentname, v_ieflag, v_country, 
        d_iedate, v_dtype, v_mailtype, v_sendoffice, v_recvoffice, v_sendname, v_sendaddress, v_sendtel, v_sendcompany, 
        v_sendidtype, v_sendid, v_recvname, v_recvaddress, v_recvtel, v_recvcompany, v_recvidtype, v_recvid, 
        n_totalwt, v_currcode, v_postagecurrcode, n_postage, n_packnum, v_maingname, v_note, v_read_flag, 
        N_TOTALVALUE, v_specmailtag)
        VALUES (:1, '1', sysdate, :2, :3, :4, :5, :6, :7, :8, to_date(:9, 'yyyy-mm-dd hh24:mi:ss'), :10, :11, :12, 
        :13, :14, :15, :16, :17, :18, :19, :20, :21, :22, :23, :24, :25, :26, :27, :28, :29, :30, :31, :32, 'Y', 
        :33, :34)
    """
    try:
        cursor.execute(sql, (guid, mailno, sbxxly, sbhg, yzqydm, yzqymc, jck, nationCode, crjsj, sblb, yjzl, yjj, jdj,
                             jjrxm, jjrdz, jjrdh, jjrqymc, zjlx, jjrzjhm, sjrxm, sjrdz, sjrdh, sjrqymc, sjrzjlx,
                             sjrzjhm, zl, sbbz, yzbz, yzaybzdjz, js, zywpmc, bz, sumtotalvalue, hgyjtsbz))
    except Exception as e:
        print(f"Error inserting into tb_mail_info_head for mailno: {mailno}")
        traceback.print_exc()


def insert_into_tb_mail_result(cursor, mailno, returnStatus, dutyFlag, returnTime, customScodes,
                               returnInfo,agentcode):
    sql = """
        INSERT INTO tb_mail_result@gjpy72 
        (v_mailno, v_returnstatus, v_dutyflag, d_returntime, v_customscode, v_returninfo,v_agentcode)
        VALUES (:1, :2, :3,  to_date(:4, 'yyyy-mm-dd hh24:mi:ss'), :5,:6, :7)
    """
    try:
        #print(f"Executing SQL: {sql}")
        #print(f"With parameters: {mailno}, {returnStatus}, {dutyFlag}, {returnTime}, {customScodes}, {returnInfo}, {agentcode}")
        cursor.execute(sql, (cursor, mailno, returnStatus, dutyFlag, returnTime, customScodes,returnInfo,agentcode))
    except cx_Oracle.DatabaseError as e:
        error, = e.args
        print(f"Error inserting into tb_mail_result for mailno: {mailno}")
        print(f"Oracle-Error-Code: {error.code}")
        print(f"Oracle-Error-Message: {error.message}")
        traceback.print_exc()

# def insert_data_via_api(mailno, returnStatus, dutyFlag, returnTime, customScodes, returnInfo, agentcode):
#     # 请求接口的 URL 和 headers
#     requestUrl = 'http://************:8520/api/xyd/crhz'
#     headers = {
#         'Content-Type': 'application/x-www-form-urlencoded'
#     }
#     # if len(agentcode) > 18:
#     #     print(f"Warning: agentcode length exceeds 18 characters, truncating to fit.")
#     #     agentcode = agentcode[:18]
#     #     print(f"Truncated agentcode: {agentcode}")
#     formData = {
#         "mailno": mailno,
#         "returnStatus": returnStatus,
#         "dutyFlag": dutyFlag,
#         "returnTime": returnTime,
#         "customScodes": customScodes,
#         "returnInfo": returnInfo,
#         "agentcode": agentcode
#     }
#     data = parse.urlencode(formData, True)
#     response = requests.post(requestUrl, headers=headers, data=data)
#     if response.status_code == 200:
#         print(f"Successfully inserted data for mailno: {mailno}")
#     else:
#         print(f"Failed to insert data for mailno: {mailno}, Status Code: {response.status_code}, Response: {response.text}")



async def insert_data_via_api(mailno, returnStatus, dutyFlag, returnTime, customScodes, returnInfo, agentcode):
    requestUrl = 'http://************:8520/api/xyd/crhz'
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    formData = {
        "mailno": mailno,
        "returnStatus": returnStatus,
        "dutyFlag": dutyFlag,
        "returnTime": returnTime,
        "customScodes": customScodes,
        "returnInfo": returnInfo,
        "agentcode": agentcode
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(requestUrl, headers=headers, data=formData) as response:
            if response.status == 200:
                print(f"Successfully inserted data for mailno: {mailno}")
            else:
                print(f"Failed to insert data for mailno: {mailno}, Status Code: {response.status}, Response: {await response.text()}")
# Function to fetch the latest return time from the database
def get_latest_return_time_from_db(cursor, mailno):
    sql = "SELECT MAX(d_returntime) FROM tb_mail_result@gjpy72 WHERE v_mailno = :mailno"
    cursor.execute(sql, {'mailno': mailno})
    result = cursor.fetchone()
    return result[0] if result else None

def getlastresult(datalist,cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]



    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = "SELECT v_mailno as 邮件号码, v_returnstatus as 回执代码," \
                    "(case when v_dutyflag='0' then '免征' else '征税' end )as 征税标识," \
                    "d_returntime as 回执时间,v_customscode as 申报关区代码,v_customname as 申报关区," \
                    "v_returninfo as 回执说明 " \
                    "FROM (SELECT ROW_NUMBER() OVER(PARTITION BY v_mailno ORDER BY d_returntime desc) rn," \
                    "t.*FROM (select t1.*,u.v_customname from tb_mail_result@gjpy72 t1,TB_PARA_CUSTOMZONE@gjpy72 u  " \
                    "where  t1.v_customscode=u.v_customcode(+) and t1.v_mailno in ("+ mail_numbers_str + ") ) t) WHERE  rn=1 "
        #df = pandas.read_sql(sql_query, connection)
        cnt = cursor.execute(sql_query)
        li = cnt.fetchall()

        print(li)
        results.append(li)
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results




def getallresult(datalist,cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]



    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = "SELECT v_mailno as 邮件号码, v_returnstatus as 回执代码," \
                    "(case when v_dutyflag='0' then '免征' else '征税' end )as 征税标识," \
                    "d_returntime as 回执时间,v_customscode as 申报关区代码,v_customname as 申报关区," \
                    "v_returninfo as 回执说明 " \
                    "FROM (SELECT ROW_NUMBER() OVER(PARTITION BY v_mailno ORDER BY d_returntime desc) rn," \
                    "t.* FROM (select t1.*,u.v_customname from tb_mail_result@gjpy72 t1,TB_PARA_CUSTOMZONE@gjpy72 u  " \
                    "where  t1.v_customscode=u.v_customcode(+) and t1.v_mailno in ("+ mail_numbers_str + ") ) t) WHERE  1=1 "
        #df = pandas.read_sql(sql_query, connection)
        cnt = cursor.execute(sql_query)
        li = cnt.fetchall()

        print(li)
        results.append(li)
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results


def getzztyresult(datalist,cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]



    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = "SELECT v_mailno as 邮件号码, v_returnstatus as 回执代码," \
                    "(case when v_dutyflag='0' then '免征' else '征税' end )as 征税标识," \
                    "d_returntime as 回执时间,v_customscode as 申报关区代码,v_customname as 申报关区," \
                    "v_returninfo as 回执说明 " \
                    "FROM (SELECT ROW_NUMBER() OVER(PARTITION BY v_mailno ORDER BY d_returntime asc) rn," \
                    "t.*FROM (select t1.*,u.v_customname from tb_mail_result@gjpy72 t1,TB_PARA_CUSTOMZONE@gjpy72 u  " \
                    "where  t1.v_customscode=u.v_customcode(+) and t1.v_mailno in ("+ mail_numbers_str + ") and t1.v_returnstatus='21' ) t) WHERE  rn=1 "
        #df = pandas.read_sql(sql_query, connection)
        cnt = cursor.execute(sql_query)
        li = cnt.fetchall()

        print(li)
        results.append(li)
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results

def getdzsresult(datalist,cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]



    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = "SELECT v_mailno as 邮件号码, v_returnstatus as 回执代码," \
                    "(case when v_dutyflag='0' then '免征' else '征税' end )as 征税标识," \
                    "d_returntime as 回执时间,v_customscode as 申报关区代码,v_customname as 申报关区," \
                    "v_returninfo as 回执说明 " \
                    "FROM (SELECT ROW_NUMBER() OVER(PARTITION BY v_mailno ORDER BY d_returntime desc) rn," \
                    "t.*FROM (select t1.*,u.v_customname from tb_mail_result@gjpy72 t1,TB_PARA_CUSTOMZONE@gjpy72 u  " \
                    "where  t1.v_customscode=u.v_customcode(+) and t1.v_mailno in ( SELECT v_mailno "\
                    "FROM (SELECT ROW_NUMBER() OVER(PARTITION BY v_mailno ORDER BY d_returntime desc) rn," \
                    "t.* FROM (select t1.*,u.v_customname from tb_mail_result@gjpy72 t1,TB_PARA_CUSTOMZONE@gjpy72 u  " \
                    "WHERE t1.v_customscode = u.v_customcode(+) and t1.v_mailno in ("+ mail_numbers_str  + ") ) t)  " \
                    "WHERE rn = 1 and v_returnstatus <> '21') and t1.v_returnstatus = '62' ) t) WHERE  rn=1 "
        #df = pandas.read_sql(sql_query, connection)
        cnt = cursor.execute(sql_query)
        li = cnt.fetchall()

        print(li)
        results.append(li)
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results


 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def process_data_in_parallel(datalist):
    """
    并行处理数据列表中的每个元素，使用提供的数据库游标。

    参数:
    - datalist (list): 需要处理的数据项列表。
    - cursor: 数据库游标，用于在`getmailmg`中执行查询。

    注意: 确保`getmailmg`函数是线程安全的，尤其是当它操作共享资源（如`cursor`）时。
    """
    # 参数类型检查
    if not isinstance(datalist, list):
        print("datalist 必须是一个列表。")
        return

    try:
        max_workers = int(threads_combobox.get())
        if max_workers <= 0:
            raise ValueError("Max workers should be a positive integer.")
    except ValueError as e:
        print(f"Invalid value for max workers: {e}")
        return

    # 使用列表推导式收集结果（如果getmailmg有返回值）
    results = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 修正lambda表达式以确保线程安全操作（假设getmailmg需要cursor和额外的错误处理）
        future_to_mailno = {executor.submit(getmailmg, mailno): mailno for mailno in datalist}

        for future in concurrent.futures.as_completed(future_to_mailno):
            mailno = future_to_mailno[future]
            try:
                result = future.result()  # 获取结果，这会抛出异常如果在执行中发生错误
                results.append(result)
            except Exception as exc:
                print(f'生成邮件数据{mailno}时发生错误: {exc}')
                traceback.print_exc()


def getinfohead(datalist, cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]
    # for mailno in datalist:
    #     getmailmg(cursor, mailno)


    # # 调用并发处理函数
    process_data_in_parallel(datalist)

    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = "select ih.v_mailno as 邮件号, ih.v_maingname as 申报内件名, ih.v_dport as 关区代码, u.v_customname as 关区," \
                    " ih.v_sendoffice as 收寄局, ih.v_recvoffice as 寄达局, ih.d_apptime as 申报时间, " \
                    " ih.n_totalvalue as 申报价值, cu.v_name as 币制, ih.n_totalwt as 重量," \
                    " REPLACE(ih.v_sendname, CHR(9), ' ') AS 寄件人,REPLACE(ih.v_sendaddress, CHR(9), ' ') AS 寄件人地址, ih.v_sendtel as 寄件人电话, REPLACE(ih.v_recvname, CHR(9), ' ') AS 收件人," \
                    " REPLACE(ih.v_recvaddress, CHR(9), ' ') AS 收件人地址, ih.v_recvtel as 收件人电话, ih.v_recvcompany as 报关组备注, ih.v_note as 备注 " \
                    " from hgzs.tb_mail_info_head@gjpy72 ih, TB_PARA_CUSTOMZONE@gjpy72 u, hgzs.tb_para_currency@gjpy72 cu " \
                    " where ih.v_dport = u.v_customcode(+) and ih.v_currcode = cu.v_code(+) and ih.v_mailno in (" + mail_numbers_str + ")"

        # df = pandas.read_sql(sql_query, connection)
        cnt = cursor.execute(sql_query)
        li = cnt.fetchall()
        #ILLEGAL_CHARACTERS_RE = re.compile(r'[\000-\010]|[\013-\014]|[\016-\037]')
        for row in li:
            cleaned_row = [clean_text(cell) for cell in row]
            results.append(cleaned_row)
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results
# 定义非法字符的正则表达式
ILLEGAL_CHARACTERS_RE = re.compile(r'[\000-\010]|[\013-\014]|[\016-\037]')

def clean_text(text):
    if not isinstance(text, (str, bytes)):
        return text  # 如果不是字符串或字节类型，直接返回
    return ILLEGAL_CHARACTERS_RE.sub(r'', text)

def updateinfohead(datalist, cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]
    # for mailno in datalist:
    #     getmailmg(cursor, mailno)
    process_data_in_parallel(datalist)
    # def process_data_in_parallel(datalist, cursor):
    #     try:
    #         max_workers = int(threads_combobox.get())
    #         if max_workers <= 0:
    #             raise ValueError("Max workers should be a positive integer.")
    #     except ValueError as e:
    #         print(f"Invalid value for max workers: {e}")
    #         return
    #
    #     with ThreadPoolExecutor(max_workers=max_workers) as executor:
    #         executor.map(lambda mailno: getmailmg(cursor, mailno), datalist)
    #         executor.shutdown(wait=True)  # 等待所有任务完成
    # # 调用并发处理函数
    # process_data_in_parallel(datalist, cursor)

    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])
        # Step 1: 查询已存在的邮件号
        check_query = f"select v_mailno from hgzs.tb_mail_info_list@gjpy72 where v_mailno in ({mail_numbers_str})"
        cursor.execute(check_query)
        existing_mailnos = {row[0] for row in cursor.fetchall()}  # 存在的数据集

        # Step 2: 找出需要插入和删除的邮件号
        missing_mailnos = [n for n in group if n not in existing_mailnos]

        # Step 3: 如果存在需要插入的邮件号，则进行插入和删除操作
        if missing_mailnos:
            missing_mailnos_str = ",".join([f"'{n}'" for n in missing_mailnos])

            # 插入数据
            insert_query = f"""
                insert into hgzs.tb_mail_info_list@gjpy72
                select * from hgzs.tb_mail_info_list_jdpt_his where v_mailno in ({missing_mailnos_str})
                """
            cursor.execute(insert_query)

            # 删除数据
            # delete_query = f"delete from hgzs.tb_mail_info_list_1104@gjpy72 where v_mailno in ({missing_mailnos_str})"
            # cursor.execute(delete_query)

        # Step 4: 更新操作
        if input3_textbox.get() == "":
            sql_query = f"update hgzs.tb_mail_info_head@gjpy72 ih set ih.v_read_flag='N' where ih.v_mailno in ({mail_numbers_str})"
        else:
            sql_query = f"update hgzs.tb_mail_info_head@gjpy72 ih set ih.v_read_flag='N', ih.v_note='{input3_textbox.get()}' where ih.v_mailno in ({mail_numbers_str})"
        cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()

    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results


def recvinfo(datalist, cursor):
    results = []
    # 获取当前选择的邮件类型
    selected_value = selected2.get()
    if selected_value == "进口":
        suffix = 'I'
    elif selected_value == "出口":
        suffix = 'E'
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]

    for group in mail_number_groups:
        mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = """
        DECLARE
            cursor s1 IS SELECT d.v_mailno FROM tb_mail_RESULT@gjpy72 d 
            WHERE d.v_mailno IN ({});
            f_dt DATE;
            f_guid VARCHAR2(36);
            f_seq NUMBER;
            ln NUMBER;
        BEGIN
            FOR t1 IN s1 LOOP
                SELECT COUNT(1) INTO ln FROM tb_recv_info@gjpy72 i 
                WHERE v_mailno = t1.v_mailno;

                IF ln = 0 THEN
                    SELECT SYSDATE INTO f_dt FROM DUAL;
                    SELECT seq_mail_info.nextval INTO f_seq FROM DUAL;
                    f_guid := 'gzamc-pac103-' || TO_CHAR(f_dt, 'yyyymmddhh24miss') || '-' || LPAD(TO_CHAR(f_seq), 8, '0');

                    INSERT INTO tb_recv_info@gjpy72 (
                        v_guid, d_apptime, v_packno, v_mailno, v_ieflag, d_recvdate, v_recvport, v_recvoffice, v_sendport, v_sendoffice, v_tranmode, n_weight, v_spectag, v_customstransittag, v_Mailtype
                    ) VALUES (
                        f_guid, f_dt, TO_CHAR(f_dt, 'MMDD'), t1.v_mailno, '{}', SYSDATE, '5147', '广州航空邮件处理中心', '5147', '广州国际', '公路运输', '0.5', '无', '1', 'E'
                    );
                ELSE
                    UPDATE tb_recv_info@gjpy72 i SET v_read_flag = 'N' WHERE v_mailno = t1.v_mailno;
                END IF;

                COMMIT;
            END LOOP;
        END;
        """.format(mail_numbers_str, suffix)  # 关键修改：插入两个参数

        # df = pandas.read_sql(sql_query, connection)
        cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()

    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results


def rkqr(datalist, cursor):

    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]

    for group in mail_number_groups:
        # mail_numbers_str = ",".join([f"'{n}'" for n in group])

        sql_query = """
        DECLARE
    cursor s2 IS
        SELECT t.v_mailno,
               substr(to_char(t.d_returntime, 'yyyymmdd'), 1, 8) flow,
                'I' AS V_IEFLAG --进出口标志
      from hgzs.tb_mail_result@gjpy72 t
        WHERE t.v_mailno IN ({});

    f_guid        VARCHAR2(36);
    f_dt          DATE;
    ln            NUMBER(2);
    ln2           NUMBER(2);
    l_kcdh        VARCHAR2(20);
    l_store_place VARCHAR2(50);
BEGIN
    SELECT SYSDATE INTO f_dt FROM DUAL;

    FOR t2 IN s2 LOOP
        SELECT TO_CHAR(SYSDATE, 'YYYY') || SYS_GUID() INTO f_guid FROM DUAL;
        l_kcdh := 'I5147' || t2.flow || '0000';
        SELECT COUNT(*)
          INTO ln2
          FROM tb_mail_store@gjpy72
         WHERE v_mailno = t2.v_mailno
           AND v_storetype = 'I';

        IF t2.v_ieflag = 'E' THEN
            l_store_place := '出口货物报关监管仓';
        ELSE
            l_store_place := '进口货物报关监管仓';
        END IF;

        IF ln2 = 0 THEN
            INSERT INTO tb_mail_store@gjpy72 (
                v_guid,
                v_mailno,
                v_storetype,
                d_storetime,
                v_dport,
                n_packnum,
                v_storename,
                v_thread_flag,
                v_storeseqno
            )
            VALUES (
                f_guid,
                t2.v_mailno,
                'I', -- 出库标志
                SYSDATE,
                '5147',
                1,
                l_store_place,
                '1',
                l_kcdh
            );
        ELSE
            UPDATE tb_mail_store@gjpy72
               SET v_read_flag = 'N'
             WHERE v_storetype = 'I'
               AND v_mailno = t2.v_mailno;
        END IF;

        COMMIT;
    END LOOP;
END;
        """.format(','.join([f"'{n}'" for n in group]))

        # df = pandas.read_sql(sql_query, connection)
        cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results

def ckqr(datalist, cursor):

    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]

    for group in mail_number_groups:

        sql_query = """
        DECLARE
    cursor s2 IS
        SELECT t.v_mailno,
               substr(to_char(t.d_returntime, 'yyyymmdd'), 1, 8) flow,
                'E' AS V_IEFLAG --进出口标志
      from hgzs.tb_mail_result@gjpy72 t
        WHERE t.v_mailno IN ({});

    f_guid        VARCHAR2(36);
    f_dt          DATE;
    ln            NUMBER(2);
    ln2           NUMBER(2);
    l_kcdh        VARCHAR2(20);
    l_store_place VARCHAR2(50);
BEGIN
    SELECT SYSDATE INTO f_dt FROM DUAL;

    FOR t2 IN s2 LOOP
        SELECT TO_CHAR(SYSDATE, 'YYYY') || SYS_GUID() INTO f_guid FROM DUAL;
        l_kcdh := 'I5147' || t2.flow || '0000';
        SELECT COUNT(*)
          INTO ln2
          FROM tb_mail_store@gjpy72
         WHERE v_mailno = t2.v_mailno
           AND v_storetype = 'E';

        IF t2.v_ieflag = 'E' THEN
            l_store_place := '出口货物报关监管仓';
        ELSE
            l_store_place := '进口货物报关监管仓';
        END IF;

        IF ln2 = 0 THEN
            INSERT INTO tb_mail_store@gjpy72 (
                v_guid,
                v_mailno,
                v_storetype,
                d_storetime,
                v_dport,
                n_packnum,
                v_storename,
                v_thread_flag,
                v_storeseqno
            )
            VALUES (
                f_guid,
                t2.v_mailno,
                'E', -- 出库标志
                SYSDATE,
                '5147',
                1,
                l_store_place,
                '1',
                l_kcdh
            );
        ELSE
            UPDATE tb_mail_store@gjpy72
               SET v_read_flag = 'N'
             WHERE v_storetype = 'E'
               AND v_mailno = t2.v_mailno;
        END IF;

        COMMIT;
    END LOOP;
END;
        """.format(','.join([f"'{n}'" for n in group]))

        # df = pandas.read_sql(sql_query, connection)
        cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results

def tysq(datalist, cursor):

    results = []
    for mailno in datalist:
        # 获取当前日期
        f_dt = cursor.execute("SELECT sysdate FROM dual").fetchone()[0]

        # 生成GUID
        f_guid = cursor.execute("SELECT to_char(sysdate, 'yyyy') || sys_guid() FROM dual").fetchone()[0]

        # 获取动态审批日期数字部分
        #dynamic_approval_number = input2_textbox.get()
        # 获取固定审批日期文本
        #fixed_approval_text = "该批邮件均未在境外投递，按原状退回境内，邮件包面随附退运批条，均符合上述邮联有关规定。审批号"
        # 查询邮件号是否存在于tb_mail_apply
        check_query = "SELECT COUNT(*) FROM tb_mail_apply@gjpy72 WHERE v_mailno = :mailno"
        ln = cursor.execute(check_query, {"mailno": mailno}).fetchone()[0]
        print(ln)
        if ln ==0:  # 如果邮件号不存在
            insert_query = """
                INSERT INTO tb_mail_apply@gjpy72 
                (v_guid, v_apptype, d_apptime, v_mailno, v_dport, v_declsource, 
                 v_agentcode, v_agentname, v_decltype, v_note)
                VALUES (:f_guid, '1', :f_dt, :mailno, '5147', '2', 
                        '51003200', '广州航空邮件处理中心', '302', 
                        :approval_note)
            """
            #approval_note = fixed_approval_text + dynamic_approval_number
            approval_note = input2_textbox.get()
            cursor.execute(insert_query,
                         {"f_guid": f_guid, "f_dt": f_dt, "mailno": mailno, "approval_note": approval_note})
        else:  # 如果邮件号已存在
            update_query = """
                UPDATE tb_mail_apply@gjpy72
                SET v_read_flag = 'N',v_note=:approval_note
                WHERE v_mailno = :mailno
            """
            #approval_note = fixed_approval_text + dynamic_approval_number
            approval_note = input2_textbox.get()
            cursor.execute(update_query, {"mailno": mailno, "approval_note": approval_note})

        #cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        #print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()
    tool.process_input("共"+str(len(datalist))+"个邮件号已处理")
    return results


def cttysq(datalist, cursor):

    results = []
    for mailno in datalist:

        # 查询邮件号是否存在于tb_mail_apply
        check_query = "SELECT COUNT(*) FROM tb_mail_apply@gjpy72 WHERE v_mailno = :mailno"
        ln = cursor.execute(check_query, {"mailno": mailno}).fetchone()[0]
        print(ln)
        if ln ==0:  # 如果邮件号不存在
            tool.process_input(f"邮件号未做过退运申请: {mailno}")
        else:  # 如果邮件号已存在
            update_query = """
                UPDATE tb_mail_apply@gjpy72
                SET v_read_flag = 'N'
                WHERE v_mailno = :mailno
            """

            cursor.execute(update_query, {"mailno": mailno})


        affected_rows = cursor.rowcount  # 获取受影响的行数
        #print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()
    tool.process_input("共"+str(len(datalist))+"个邮件号已处理")
    return results


def chunk_list(lst, chunk_size=1000):
    """将列表分成不超过 chunk_size 的小块"""
    for i in range(0, len(lst), chunk_size):
        yield lst[i:i + chunk_size]


def dajjxx(datalist, cursor, cursor2):
    """查询邮件号，并获取邮件信息"""

    # 获取日期参数，确保是 YYYYMMDD 格式
    start_date = start_date_entry.get_date().strftime('%Y%m%d')
    end_date = end_date_entry.get_date().strftime('%Y%m%d')

    # SQL1 查询邮件号
    sql1 = """
        SELECT * FROM gdamc.tb_customs_image_check 
        WHERE scan_time >= TO_DATE(:start_date, 'YYYYMMDD') 
        AND scan_time < TO_DATE(:end_date, 'YYYYMMDD')
    """

    if datalist:
        # 处理Oracle的1000条IN限制
        chunks = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]
        conditions = []

        for chunk in chunks:
            # 使用三重双引号解决引号嵌套问题
            escaped_values = [f"""'{value.replace("'", "''")}'""" for value in chunk]
            conditions.append(f"mail_code IN ({', '.join(escaped_values)})")

        # 合并条件并添加到SQL
        mail_condition = " OR ".join(conditions)
        sql1 += f" AND ({mail_condition})"
    results1, headers1 = query_database(cursor2, sql1, {"start_date": start_date, "end_date": end_date})
    export_to_csv("1.csv", results1, headers1)

    # 提取邮件号
    mail_numbers = {row[0] for row in results1}  # 假设邮件号在第一列
    if not mail_numbers:
        print("没有找到邮件号，不执行第二个查询。")
        return

    # **清空临时表**
    cursor.execute("DELETE FROM tb_item@gjpy72")
    cursor.connection.commit()

    # **插入邮件号到临时表**
    insert_sql = "INSERT INTO tb_item@gjpy72 (V_MAILNO) VALUES (:mail_code)"
    cursor.executemany(insert_sql, [{"mail_code": mail} for mail in mail_numbers])
    cursor.connection.commit()

    # **使用 JOIN 查询邮件信息**
    sql2 = """
        SELECT ih.* 
        FROM hgzs.tb_mail_info_head@gjpy72 ih
        JOIN tb_item@gjpy72 ti ON ih.v_mailno = ti.V_MAILNO
    """
    results2, headers2 = query_database(cursor, sql2)
    export_to_csv("2.csv", results2, headers2)

    # **清空临时表**
    cursor.execute("DELETE FROM tb_item@gjpy72")
    cursor.connection.commit()


def query_database(cursor,sql, params=None):
    """执行 SQL 查询并返回结果"""

    cursor.execute(sql, params or {})
    results = cursor.fetchall()
    headers = [col[0] for col in cursor.description]  # 获取表头
    #cursor.close()
    #connection2.close()
    return results, headers

def export_to_csv(filename, data, headers):
    """将查询结果导出为 CSV 文件"""
    with open(filename, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)
        writer.writerow(headers)  # 写入表头
        writer.writerows(data)  # 写入数据
    print(f"数据已导出到 {filename}")
    tool.process_input("数据已导出到" + filename)


def scyj(datalist):
    wildcard_positions = [i for i, c in enumerate(datalist[0]) if c == '*']
    batch_size = 10000  # 每 10,000 条存一个 Excel
    file_index = 1  # 文件编号
    df_chunks = []  # 批量存储数据

    # 生成所有可能的组合
    for i, combo in enumerate(product("0123456789", repeat=len(wildcard_positions))):
        number = list(datalist[0])
        for idx, digit in zip(wildcard_positions, combo):
            number[idx] = digit
        df_chunks.append(["".join(number)])

        # 每 batch_size 条数据写入一个 Excel 文件
        if (i + 1) % batch_size == 0:
            df = pandas.DataFrame(df_chunks)
            filename = f"numbers_{file_index}.xlsx"
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"已生成 {filename}，包含 {batch_size} 条数据")
            tool.process_input("已生成" + filename + "，包含" + str(batch_size) + "条数据")
            # 清空数据，准备写入下一个文件
            df_chunks = []
            file_index += 1

    # 处理最后不足 batch_size 的部分
    if df_chunks:
        df = pandas.DataFrame(df_chunks)
        filename = f"numbers_{file_index}.xlsx"
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"已生成 {filename}，包含 {len(df_chunks)} 条数据")
        tool.process_input("已生成" + filename + "，包含" + str(len(df_chunks)) + "条数据")
    print("所有 Excel 文件生成完毕！")
    tool.process_input("所有 Excel 文件生成完毕")


def rcdj(datalist,cursor):
    results = []
    # 分割邮件号列表，每组最多一千个
    mail_number_groups = [datalist[i:i + 1000] for i in range(0, len(datalist), 1000)]

    for group in mail_number_groups:
        sql_query = """
            
  begin
   
    for t1 in (select substr(to_char(r.d_returntime, 'yyyymmdd'), 1, 8) flow,
                      t.v_mailno,
                      t.v_maingname,
                      t.n_totalwt,
                      t.d_apptime,
                      t.v_recvtel,
                      t.v_recvname,
                      t.v_ieflag, --进出口标志
                      t.v_sendname,
                      t.v_sendtel,
                      t.v_tzdno, --通知单号码
                      r.v_returninfo,
                      r.d_returntime
                 from hgzs.tb_mail_result@gjpy72 r, hgzs.tb_mail_info_head@gjpy72 t
                where r.v_mailno = t.v_mailno
                  and r.v_returnstatus = '40'
                  and t.v_declsource = '2'
                 
                  and r.v_customscode = '5147'
               
                 and r.v_mailno in ({}
)
) loop
               
    declare
     l_no           number;
    l_no1          number;
    l_note         varchar2(50);
    f_dt           date;
    l_kcdh         varchar2(20);
    l_gname        varchar2(100);
    l_store_place  varchar2(50);
    v_name         varchar2(100);
    v_tel          varchar2(30);
    tzdno          number;



  begin
        
        select sysdate into f_dt from dual;
        select count(1)
          into l_no
          from mis.mis_inter_tabstore_head@gjpy72 y
         where y.entry_no = t1.v_mailno;
        select count(1)
          into l_no1
          from ccgl.sms_mail@gjpy72
         where entry_no = t1.v_mailno;
        --入仓单号一天一个号，以海关回执入仓日期为准
        l_kcdh  := 'I5147' || t1.flow || '0000';
        l_gname := substr(t1.v_maingname, 1, 30);
        if t1.v_returninfo like '%货物报关%' then
          l_note := '货物报关';
        elsif t1.v_returninfo like '%补充申报%' then
          l_note := '补充申报';
          --   elsif t1.v_returninfo like '%其它%' then
          --       l_note := '其它';
        else
          l_note := substr(t1.v_returninfo, 1, 25);
        end if;
        if t1.v_ieflag = 'E' then
          l_store_place := '出口货物报关监管仓';
          v_name        := t1.v_sendname;
          v_tel         := t1.v_sendtel;
        else
          l_store_place := '进口货物报关监管仓';
          v_name        := t1.v_recvname;
          v_tel         := t1.v_recvtel;
        end if;
        v_tel := replace(substrb(v_tel, 1, 13), '-', '');
        v_tel := replace(substrb(v_tel, 1, 13), '+', '');
        v_tel := replace(substrb(v_tel, 1, 13), ' ', '');
        if l_no = 0 then
          if l_note = '补充申报' or l_note = '货物报关' then
            insert into mis.mis_inter_tabstore_head@gjpy72
              (entry_no,
               store_type,
               store_date,
               customs_id,
               trade_code,
               store_place,
               main_g_name,
               total_wt,
               note,
               flag,
               create_date,
               staff_no,
               org_id,
               dept_id,
               ccbz,
               kcdh)
            values
              (t1.v_mailno,
               '1',
               t1.d_returntime,
               '5147',
               '510000',
               l_store_place,
               l_gname,
               t1.n_totalwt,
               l_note,
               '0',
               f_dt,
               'GH006274',
               '1',
               '3',
               '0',
               l_kcdh);
            if l_no1 = 0 then
              insert into ccgl.sms_mail@gjpy72
                (entry_no,
                 total_wt,
                 recv_name,
                 recv_phone,
                 v_input_reason,
                 g_name,
                 t_sysdate,
                 v_ieflag)
              values
                (t1.v_mailno,
                 t1.n_totalwt,
                 v_name,
                 v_tel,
                 l_note,
                 l_gname,
                 f_dt,
                 t1.v_ieflag);
              if v_tel = '0' and t1.v_tzdno = '0' then
                select ccgl.seq_tzdno.nextval into tzdno from dual;
                update hgzs.tb_mail_info_head@gjpy72
                   set v_tzdno = tzdno
                 where v_mailno = t1.v_mailno;
              end if;
            end if;
          else
            insert into mis.mis_inter_tabstore_head@gjpy72
              (entry_no,
               store_type,
               store_date,
               customs_id,
               trade_code,
               store_place,
               main_g_name,
               total_wt,
               note,
               flag,
               create_date,
               staff_no,
               org_id,
               dept_id,
               ccbz,
               kcdh)
            values
              (t1.v_mailno,
               '1',
               t1.d_returntime,
               '5147',
               '510000',
               l_store_place,
               l_gname,
               t1.n_totalwt,
               l_note,
               '0',
               f_dt,
               'GH006274',
               '1',
               '3',
               '0',
               l_kcdh);
          end if;
        else
          if l_note = '补充申报' or l_note = '货物报关' then
            --重新入库后把原有信息归档
            insert into mis.mis_inter_tabstore_head_h@gjpy72
              select *
                from mis.mis_inter_tabstore_head@gjpy72
               where entry_no = t1.v_mailno;
            delete from mis.mis_inter_tabstore_head@gjpy72
             where entry_no = t1.v_mailno;
            insert into hgzs.tb_mail_result_h@gjpy72
              select *
                from hgzs.tb_mail_result@gjpy72 rr
               where rr.v_returnstatus in ('26', '21')
                 and v_mailno = t1.v_mailno;
            delete from hgzs.tb_mail_result@gjpy72 rr
             where rr.v_returnstatus in ('26', '21')
               and v_mailno = t1.v_mailno;
            insert into ccgl.tb_taxbill_list_h@gjpy72
              select *
                from ccgl.tb_taxbill_list@gjpy72 dd
               where dd.v_bill_no in
                     (select mm.v_bill_no
                        from ccgl.tb_taxbill_list_item@gjpy72 mm
                       where mm.v_item_no = t1.v_mailno);
            delete from ccgl.tb_taxbill_list@gjpy72 dd
             where dd.v_bill_no in
                   (select mm.v_bill_no
                      from ccgl.tb_taxbill_list_item@gjpy72 mm
                     where mm.v_item_no = t1.v_mailno);
            insert into ccgl.tb_taxbill_list_item_h@gjpy72
              select *
                from ccgl.tb_taxbill_list_item@gjpy72 mm
               where mm.v_item_no = t1.v_mailno;
            delete from ccgl.tb_taxbill_list_item@gjpy72 mm
             where mm.v_item_no = t1.v_mailno;
            insert into ccgl.sms_mail_h@gjpy72
              select * from ccgl.sms_mail@gjpy72 aa where aa.entry_no = t1.v_mailno;
            delete from ccgl.sms_mail@gjpy72 aa where aa.entry_no = t1.v_mailno;
            insert into mis.mis_inter_tabstore_head@gjpy72
              (entry_no,
               store_type,
               store_date,
               customs_id,
               trade_code,
               store_place,
               main_g_name,
               total_wt,
               note,
               flag,
               create_date,
               staff_no,
               org_id,
               dept_id,
               ccbz,
               kcdh)
            values
              (t1.v_mailno,
               '1',
               t1.d_returntime,
               '5147',
               '510000',
               l_store_place,
               l_gname,
               t1.n_totalwt,
               l_note,
               '0',
               f_dt,
               'GH006274',
               '1',
               '3',
               '0',
               l_kcdh);
            insert into ccgl.sms_mail@gjpy72
              (entry_no,
               total_wt,
               recv_name,
               recv_phone,
               v_input_reason,
               g_name,
               t_sysdate,
               v_ieflag)
            values
              (t1.v_mailno,
               t1.n_totalwt,
               v_name,
               v_tel,
               l_note,
               l_gname,
               f_dt,
               t1.v_ieflag);
            --没有电话的自动生成通知单号码，用于打印通知单
            if v_tel = '0' and t1.v_tzdno = '0' then
              select ccgl.seq_tzdno.nextval into tzdno from dual;
              update hgzs.tb_mail_info_head@gjpy72
                 set v_tzdno = tzdno
               where v_mailno = t1.v_mailno;
            end if;
          end if;
        end if;
      end;
      update hgzs.tb_mail_result@gjpy72
         set v_read_flag = 'Y'
       where v_returnstatus = '40'
         and v_mailno = t1.v_mailno;
    end loop;
    commit;
end;

            """.format(','.join([f"'{n}'" for n in group]))

        # df = pandas.read_sql(sql_query, connection)
        cursor.execute(sql_query)

        affected_rows = cursor.rowcount  # 获取受影响的行数
        print(f"受影响的行数: {affected_rows}")
        tool.process_input(f"受影响的行数: {affected_rows}")
        connection.commit()
    tool.process_input("共" + str(len(datalist)) + "个邮件号已处理")
    return results


def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data,connection,connection2


        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本转换为大写
        text = text.upper()
        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines


        tool.process_input('开始查询')

        # 设置 Oracle 数据库连接信息
        dsn = cx_Oracle.makedsn('***********', '1521', service_name='gjinter')

        # 使用代理连接 Oracle 数据库
        connection = cx_Oracle.connect(user='hgzs', password='gjyj_hgzs', dsn=dsn)

        cursor = connection.cursor()
        result =[]
        # 并发执行并获取结果
        if '导最后回执' in selected.get():

            result=getlastresult(datalist,cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = pandas.concat([pandas.DataFrame(data, columns=['邮件号码', '回执代码', '征税标识', '回执时间',
                                                                       '申报关区代码', '申报关区', '回执说明']) for data
                                       in
                                       result])

        elif '导全部回执' in selected.get():

            result = getallresult(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = pandas.concat([pandas.DataFrame(data, columns=['邮件号码', '回执代码', '征税标识', '回执时间',
                                                                       '申报关区代码', '申报关区', '回执说明']) for data
                                       in
                                       result])
        elif '导补录信息' in selected.get():
            tool.process_input('开始登录新一代')
            tool.process_input('第1次尝试登录')
            if '进口' in selected2.get():
                url = 'https://**********/intprep-web/a/intprep/impintcustmailbase/list'
            else:
                url = 'https://**********/intprep-web/a/intprep/exchangeReplace/list'
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i=2
            while (jdptid == '0'):
                # print ('第'+str(i)+'次尝试登录')
                tool.process_input('第' + str(i) + '次尝试登录')
                result = tool.getck(username, password, session, url)
                jdptid = result[0]
                userName = result[1]
                i += 1
                if i > 10:
                    tool.process_input('超10次登录失败,请检查账号密码正确后重试')
                    break
            tool.postlog(username, userName, title, ip_address)
            result = getinfohead(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = pandas.concat([pandas.DataFrame([data], columns=['邮件号码', '申报内件名', '关区代码', '关区', '收寄局', '寄达局', '申报时间', '申报价值', '币制', '重量', '寄件人','寄件人地址', '寄件人电话', '收件人', '收件人地址', '收件人电话', '报关组备注', '备注']) for data
                                       in
                                       result])
        elif "待征税" in selected.get():
            result = getdzsresult(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = pandas.concat([pandas.DataFrame(data, columns=['邮件号码', '回执代码', '征税标识', '回执时间',
                                                                       '申报关区代码', '申报关区', '回执说明']) for data
                                       in
                                       result])
        elif "推申报" in selected.get():
            tool.process_input('开始登录新一代')
            tool.process_input('第1次尝试登录')
            if '进口' in selected2.get():
                url = 'https://**********/intprep-web/a/intprep/impintcustmailbase/list'
            else:
                url = 'https://**********/intprep-web/a/intprep/exchangeReplace/list'
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i = 2
            while (jdptid == '0'):
                # print ('第'+str(i)+'次尝试登录')
                tool.process_input('第' + str(i) + '次尝试登录')
                result = tool.getck(username, password, session, url)
                jdptid = result[0]
                userName = result[1]
                i += 1
                if i > 10:
                    tool.process_input('超10次登录失败,请检查账号密码正确后重试')
                    break
            tool.postlog(username, userName, title, ip_address)
            result = updateinfohead(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
            # results = pool.map(getallmailtrace, datalist)
        elif "推开拆" in selected.get():
            result = recvinfo(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
        elif "入库确认" in selected.get():
            result = rkqr(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
        elif "出库确认" in selected.get():
            result = ckqr(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
        elif "退运申请" == selected.get():

            result = tysq(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
        elif "重推退运申请" == selected.get():

            result = cttysq(datalist, cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = ''
        elif '最早退运回执' in selected.get():

            result=getzztyresult(datalist,cursor)
            # 合并所有查询结果为一个 DataFrame
            merged_df = pandas.concat([pandas.DataFrame(data, columns=['邮件号码', '回执代码', '征税标识', '回执时间',
                                                                       '申报关区代码', '申报关区', '回执说明']) for data
                                       in
                                       result])
        elif '导安检信息' in selected.get():
            # 设置 Oracle 数据库连接信息
            dsn2 = cx_Oracle.makedsn('10.194.69.10', '1521', service_name='orcl')

            # 使用代理连接 Oracle 数据库
            connection2 = cx_Oracle.connect(user='qryda', password='ghzx_Qryda#2024', dsn=dsn2)

            cursor2 = connection2.cursor()
            dajjxx(datalist,cursor, cursor2)

        elif '生成邮件' in selected.get():
            scyj(datalist)
        elif '入仓登记' in selected.get():

            rcdj(datalist,cursor)


        tool.process_input('执行完毕')

        cursor.close()


        # 定义当前时间
        currentTime = datetime.datetime.now()
        # 将元组转换为列表
        #list_of_lists = [list(row) for row in result]
        #dataForm = pandas.DataFrame(result,columns=['邮件号码', '回执代码', '征税标识', '回执时间', '申报关区代码', '申报关区', '回执说明'])
        if len(result) > 0:
            tool.process_input('正在写入Excel')
            row = 1048570
            length = len(result)
            number = length // row
            for i in range(number + 1):
                merged_df[i * row:(i + 1) * row].to_excel("邮件"+selected.get()+"-"+
                                                         currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                         index=False)


            tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del result
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    if '插最新回执' in selected.get():
        def run_async_task():
            # 创建事件循环
            asyncio.run(batch_query_and_export(title, parent))

        # 启动线程运行异步任务
        threading.Thread(target=run_async_task, daemon=True).start()
    else:

        t = threading.Thread(target=run, args=(title, parent))
        t.start()

executable_path = "firefox/firefox.exe"    # 指定可执行文件路径，用相对路径

# 请求信息类
class RequestInfo:
    def __init__(self, url, response_data=None):
        self.url = url
        self.response_data = response_data

def saveOrgShopTeamSeat(cookies):
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    url = 'https://**********/portal/a/basic/saveOrgShopTeamSeat'
    # 获取当前时间戳
    #timestamp = int(time.time() * 1000)

    # 构建 URL
    #url = f'https://**********/portal/a/basic/saveOrgShopTeamSeat?t={timestamp}'
    data = {
        'rs': '{"workShopCode":"SD51040034","workShopName":"默认车间","workShopGroupCode":"GJCK","workShopGroupName":"国际出口班","seatCode":"30001","seatName":"测试01"}'
    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
        #'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    #tool.process_input(response.text)
    print(response.text)
    tool.process_input(response.text)


def joinShift(cookies):
    requests.packages.urllib3.disable_warnings()
    cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
    #url = 'https://**********/intproc-web/a/intproc/shift/view'
    url = 'https://**********/intproc-web/a/intproc/impcustomsquery/list'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Host': '**********',
        'DNT': '1',
        'Referer': 'https://**********/portal/a',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'iframe',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
    }
    response = session.get(url, headers=headers, verify=False)
    r = response.text
    soup = BeautifulSoup(r, 'lxml')
    #print(r)
    bc = soup.find('input', attrs={'name': 'ids'})['value']  # 班次
    # print(bc)
    url = 'https://**********/intproc-web/a/intproc/shift/joinShift'
    data = {
        'cryptId': bc

    }
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-Length': '79',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'DNT': '1',
        'Host': '**********',
        'Origin': 'https://**********',
        'Referer': 'https://**********/intproc-web/a/intproc/impcustomsquery/list',
        'sec-ch-ua': '"Not-A.Brand";v="24", "Chromium";v="14"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Cookie': "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])
    }
    response = session.post(url, headers=headers, data=data, verify=False)
    #tool.process_input(response.text)
    print(response.text)
async def batch_query_and_export(title,parent):
    global L
    # 程序已经在运行中，禁用按钮
    submit_button.configure(state="disabled")
    back_button.configure(state="disabled")
    start = time.perf_counter()
    # 获取本机主机名
    hostname = socket.gethostname()
    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}

        tool.process_input("代理功能已启用")
    L = 0

    # 保存账号和密码
    tool.save_data()
    username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
    password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
    try:
        # 使用 Playwright 启动浏览器
        async with async_playwright() as p:
            browser = await p.firefox.launch(headless=True,
                                             executable_path=executable_path,
                                             args=["--no-sandbox", "--disable-setuid-sandbox"])

            # 创建浏览器上下文
            context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
            page = await context.new_page()
            # 获取网络请求
            # page.on("request", lambda request: print( ">> ", request.method, request.url))
            # # 获取网络响应
            # page.on("response", lambda response: print( "<< ", response.status, response.text))

            # 设置 User-Agent模拟正常浏览器
            await context.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            })

            # 禁用Playwright的自动化标识
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)
            # 获取文本框中的文本
            text = input_textbox.get("1.0", "end-1c")
            # 将文本转换为大写
            text = text.upper()
            # 将文本按行分割并去除空行
            lines = text.splitlines()
            lines = [line for line in lines if line.strip()]
            mailnos = lines
            #print(mailnos)
            # 登录
            userName,orgName,orgcode=await tool.newlogin(page, username, password)
            tool.postlog(username, userName, title, ip_address)


            #进入班次查询页面
            #await select_latestbc(page)

            # 获得登录后 cookie
            cookies = await context.cookies()
            # 打印cookie
            # for cookie in cookies:
            #    print(cookie)
            # 将cookie添加到session中
            #session.cookies.update(cookies)

            # 获取登录后的 cookies
            #cookies = await page.context.cookies()
            # 将 Playwright 获取的 cookies 转换为 requests 需要的格式

            saveOrgShopTeamSeat(cookies)

            #await select_latestbc(page)
            await page.goto(url="https://**********/intproc-web/a/intproc/impcustomsquery/list")
            cookies = await page.context.cookies()

            joinShift(cookies)

            await page.close()  # 登录完成后关闭页面

            all_logistics_data = []

            # 限制并发的最大任务数
            semaphore = asyncio.Semaphore(int(threads_combobox.get()))  # 设置并发上限为 5（可调整）

            async def query_with_semaphore(mailno,cookies):
                async with semaphore:

                    try:
                        #tool.process_input(f"正在查询邮件号: {mailno}")
                        if '进口' in selected2.get():
                            logistics_data = await getinmailtrace( parent,mailno,cookies)
                        else:
                            logistics_data = await getoutmailtrace( parent,mailno,cookies)

                    finally:
                        pass

                    return logistics_data


            # 创建任务列表
            tasks = [query_with_semaphore(mailno,cookies) for mailno in mailnos]
            # 并发执行任务
            results = await asyncio.gather(*tasks)
            #收集所有结果
            # for logistics_data in results:
            #     if logistics_data:
            #         #print(logistics_data)
            #         # 使用 append 直接将字典添加到列表中
            #         all_logistics_data.append(logistics_data)
            #print(all_logistics_data)


            #print(merged_data)

            # 导出到Excel


            end = time.perf_counter()
            runTime = end - start
            # 计算时分秒
            hour = runTime // 3600
            minute = (runTime - 3600 * hour) // 60
            second = runTime - 3600 * hour - 60 * minute

            tool.process_input(f'共处理{L}件邮件')
            # 输出
            # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
            tool.process_input(f'\n运行时间：{hour}小时{minute}分钟{second}秒')
            del all_logistics_data
            # 程序未在运行中，启用按钮
            submit_button.configure(state="normal")
            back_button.configure(state="normal")

    except Exception as e:
        tool.process_input(f"查询失败，错误信息：{e}")
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    await browser.close()

def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)


def update_date(*args):  # 添加 *args 来接收传递的参数
    global options
    # 获取当前选择的邮件类型
    selected_value = selected2.get()

    # 根据选择更新日期后缀
    if selected_value == "进口":
        suffix = '1'
    elif selected_value == "出口":
        suffix = '2'
    else:
        suffix = ''
    options = [
        '该批邮件均未在境外投递，按原状退回境内，邮件包面随附退运批条，均符合上述邮联有关规定。审批号' + datetime.datetime.now().strftime(f'%Y%m%d{suffix}'),
        '接收预安检内件不及格，审批号' + datetime.datetime.now().strftime('%Y%m%d2'),
        '逾期未缴税，申请退运。'
    ]
    input2_textbox['values'] = options
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, selected2,submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,\
        back_button,organization_combobox,input3_textbox,input2_textbox,options

    # 构造Session
    session = requests.Session()

    today = datetime.datetime.today()
    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='导最后回执')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=0, column=0)
    radio_button1 = ttk.Radiobutton(input_label_container, text="导最后回执", value="导最后回执", variable=selected)
    radio_button1.grid(row=0, column=1)

    radio_button2 = ttk.Radiobutton(input_label_container, text="导补录信息", value="导补录信息", variable=selected)
    radio_button2.grid(row=0, column=2, padx=15)

    radio_button3 = ttk.Radiobutton(input_label_container, text="待征税时间", value="待征税时间", variable=selected)
    radio_button3.grid(row=0, column=3)

    radio_button4 = ttk.Radiobutton(input_label_container, text="推申报信息", value="推申报信息", variable=selected)
    radio_button4.grid(row=0, column=4)

    radio_button5 = ttk.Radiobutton(input_label_container, text="推开拆信息", value="推开拆信息", variable=selected)
    radio_button5.grid(row=0, column=5)

    radio_button6 = ttk.Radiobutton(input_label_container, text="入库确认", value="入库确认", variable=selected)
    radio_button6.grid(row=1, column=1)

    radio_button7 = ttk.Radiobutton(input_label_container, text="出库确认", value="出库确认", variable=selected)
    radio_button7.grid(row=1, column=2)

    radio_button8 = ttk.Radiobutton(input_label_container, text="退运申请", value="退运申请", variable=selected)
    radio_button8.grid(row=1, column=3)

    radio_button9 = ttk.Radiobutton(input_label_container, text="最早退运回执", value="最早退运回执", variable=selected)
    radio_button9.grid(row=1, column=4)

    radio_button10 = ttk.Radiobutton(input_label_container, text="重推退运申请", value="重推退运申请", variable=selected)
    radio_button10.grid(row=1, column=5)

    radio_button11 = ttk.Radiobutton(input_label_container, text="导全部回执", value="导全部回执",variable=selected)
    radio_button11.grid(row=2, column=1)

    radio_button12 = ttk.Radiobutton(input_label_container, text="插最新回执", value="插最新回执", variable=selected)
    radio_button12.grid(row=2, column=2)

    radio_button13 = ttk.Radiobutton(input_label_container, text="导安检信息", value="导安检信息", variable=selected)
    radio_button13.grid(row=2, column=3)

    radio_button14 = ttk.Radiobutton(input_label_container, text="生成邮件", value="生成邮件", variable=selected)
    radio_button14.grid(row=2, column=4)

    radio_button15 = ttk.Radiobutton(input_label_container, text="入仓登记", value="入仓登记", variable=selected)
    radio_button15.grid(row=2, column=5)

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=2, column=0)

    # 创建单选按钮
    selected2 = tk.StringVar(value='进口')
    radio_label = ttk.Label(date_container, text="邮件类型:")
    radio_label.grid(row=3, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(date_container, text="进口", value="进口", variable=selected2)
    radio_button1.grid(row=3, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(date_container, text="出口", value="出口", variable=selected2)
    radio_button2.grid(row=3, column=2, padx=5, pady=10)

    # 监听选项变化
    #selected2.trace("w", update_date)

    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=4, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(date_container, maxdate=today.date())
    start_date_entry.grid(row=4, column=1, padx=10, pady=10)

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=4, column=2, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(date_container)
    end_date_entry.grid(row=4, column=3, padx=10, pady=10)

    # 添加指定处理动作
    input3_label = ttk.Label(date_container, text="申报备注:")
    input3_label.grid(row=5, column=0, padx=10, pady=10)

    # input_label.pack()
    # input3_textbox = tk.Entry(date_container, width=30)
    # input3_textbox.grid(row=5, column=1, padx=10, pady=10, columnspan=1)
    # input3_textbox.insert(0, '长期未申报，申请入库转退运')  # 插入默认值
    # 下拉选择框的选项列表
    options3 = [

        '长期未申报，申请入库转退运',


    ]
    # 创建Combobox
    input3_textbox = ttk.Combobox(date_container, values=options3, width=30)
    input3_textbox.set(options3[0])  # 设置默认值为第一个选项
    input3_textbox.grid(row=5, column=1, padx=10, pady=10, columnspan=1)


    input2_label = ttk.Label(date_container, text="审批号:")
    input2_label.grid(row=5, column=2, padx=10, pady=10)

    # input_label.pack()
    # input2_textbox = tk.Entry(date_container, width=30)
    # input2_textbox.grid(row=5, column=3, padx=10, pady=10, columnspan=1)
    # # 设置默认值
    # input2_textbox.insert(0, '该批邮件均未在境外投递，按原状退回境内，邮件包面随附退运批条，均符合上述邮联有关规定。审批号'+datetime.datetime.now().strftime('%Y%m%d1'))  # 插入默认值

    # 下拉选择框的选项列表
    options = [
        '该批邮件均未在境外投递，按原状退回境内，邮件包面随附退运批条，均符合上述邮联有关规定。',#审批号' + datetime.datetime.now().strftime('%Y%m%d1'),
        '接收预安检内件不及格',#审批号' + datetime.datetime.now().strftime('%Y%m%d2'),
        '逾期未缴税，申请退运。'
    ]

    # 创建Combobox
    input2_textbox = ttk.Combobox(date_container, values=options, width=30)
    input2_textbox.set(options[0])  # 设置默认值为第一个选项
    input2_textbox.grid(row=5, column=3, padx=10, pady=10, columnspan=1)

    # 添加多行输入框
    input_label = ttk.Label(date_container, text="邮件号:")
    input_label.grid(row=6, column=1, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(date_container, height=5, width=30)
    input_textbox.grid(row=6, column=2, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(date_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=7, column=1, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(date_container, text="清空文本", command=clear_text)
    button_clear.grid(row=7, column=2, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=6, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()