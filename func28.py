import datetime
import io
import json
import os
import socket
import sys
import threading
import tkinter as tk
import traceback

from datetime import timedelta
from tkinter import *
from tkinter import scrolledtext
from tkinter import ttk

from os import path


import pandas
import re
import requests
import time

from bs4 import BeautifulSoup
from tkcalendar import DateEntry
from concurrent.futures import ThreadPoolExecutor, as_completed
from tool import Tool

# 以下为定义一个字典，以及需要获取的字段名称

dataOutput = {
    "序号": [],
    "创建时间": [],
    "单号": [],
    "税单号": [],
    "汇总税单号": [],
    "缴款人姓名": [],
    "回执时间": [],
    "缴款人地址": [],
    "行邮税总额(元)": [],
    "税单状态": []
}

# 邮件号查询结果数据结构
mailDetailOutput = {
    "序号": [],
    "物品名称": [],
    "归类编码": [],
    "完税价格（元）": [],
    "行邮税率": [],
    "行邮税金额（元）": [],
    "邮件id": [],
    "邮件重量(kg)": []
}


def getzb(parent):
    # 获取邮件号输入
    mail_numbers_text = mail_input_textbox.get("1.0", "end-1c").strip()

    if mail_numbers_text:
        # 如果有邮件号输入，按邮件号查询
        tool.process_input('检测到邮件号输入，开始按邮件号查询')
        mail_numbers = [line.strip() for line in mail_numbers_text.split('\n') if line.strip()]
        tool.process_input(f'共输入{len(mail_numbers)}个邮件号')

        # 获取日期范围并转换格式
        start_date = str(start_date_entry.get_date()).replace('-', '')
        end_date = str(end_date_entry.get_date()).replace('-', '')

        # 使用 ThreadPoolExecutor 来并发查询邮件详情
        with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            futures = [
                executor.submit(get_mail_detail, mail_number, start_date, end_date, parent)
                for mail_number in mail_numbers
            ]
    else:
        # 如果没有邮件号输入，按日期范围查询
        tool.process_input('未输入邮件号，开始按日期范围查询')

        # 获取其他查询条件
        bill_item_text = bill_item_entry.get().strip()
        user_name_text = user_name_entry.get().strip()

        # 获取日期范围
        start_date = str(start_date_entry.get_date())
        end_date = str(end_date_entry.get_date())

        tool.process_input('开始日期:' + start_date)
        tool.process_input('结束日期:' + end_date)

        if bill_item_text:
            tool.process_input('税单号:' + bill_item_text)
        if user_name_text:
            tool.process_input('缴款人姓名:' + user_name_text)

        # 将字符串日期转换为 datetime 对象
        start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')

        # 判断日期差，如果超过七天，则分批请求
        date_ranges = []
        while start_date_obj <= end_date_obj:
            next_date = min(start_date_obj + timedelta(days=7), end_date_obj)
            date_ranges.append((start_date_obj.strftime('%Y%m%d'), next_date.strftime('%Y%m%d')))
            start_date_obj = next_date + timedelta(days=1)  # 更新开始日期为下一个日期
            # 确保最后一个日期范围包含结束日期
        if date_ranges and date_ranges[-1][1] < end_date_obj.strftime('%Y%m%d'):
            date_ranges.append((date_ranges[-1][1], end_date_obj.strftime('%Y%m%d')))
        # 使用 ThreadPoolExecutor 来并发请求数据
        with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            futures = [
                executor.submit(get_data_for_date_range_with_params, date_range[0], date_range[1], '', bill_item_text, user_name_text, parent)
                for date_range in date_ranges
            ]

def get_data_for_date_range(start_date, end_date, parent):
    # 获取数据的函数，传入日期范围进行请求

    parent.after(0, tool.process_input(f'开始请求数据: {start_date} - {end_date}'))
    # 根据日期范围进行数据请求和处理
    process_data(start_date, end_date)

def get_data_for_date_range_with_params(start_date, end_date, itemId, billItem, userName, parent):
    # 获取数据的函数，传入日期范围和查询参数进行请求
    parent.after(0, tool.process_input(f'开始请求数据: {start_date} - {end_date}, 参数: itemId={itemId}, billItem={billItem}, userName={userName}'))
    # 根据日期范围和参数进行数据请求和处理
    process_data_with_params(start_date, end_date, itemId, billItem, userName)

def get_mail_detail(mail_number, start_date, end_date, parent):
    # 获取邮件详情的函数
    parent.after(0, tool.process_input(f'开始查询邮件: {mail_number}'))
    # 根据邮件号进行详情查询
    process_mail_detail(mail_number, start_date, end_date)

def fetch_data(start_date, end_date, itemId='', billItem='', userName=''):
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/report-intl-web/a/reportIntl/customsTaxReceipt/result'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {
        'userOrgCode': 51040075,
        'beginDate': start_date,
        'endDate': end_date,
        'itemId': itemId,
        'start': 0,
        'billItem': billItem,
        'userName': userName
    }
    print(f"查询参数: itemId={itemId}, billItem={billItem}, userName={userName}, 日期范围: {start_date}-{end_date}")
    response = session.post(url, headers=headers, params=data, verify=False)
    return response.text

def parse_data(response_text):
    # 检查 response_text 是否有效
    if not response_text:
        return
    html = BeautifulSoup(response_text, 'lxml')
    target_tbody = html.find('tbody')
    if target_tbody:
        trs = target_tbody.find_all('tr')
        for tr in trs:
            tds = tr.find_all('td')
            # 确保 tds 长度满足需求
            if len(tds) >= 10:
                dataOutput["序号"].append(tds[0].get_text().strip())
                dataOutput["创建时间"].append(tds[1].get_text().strip())
                # 提取单号，去除链接标签
                dan_hao_element = tds[2].find('a')
                if dan_hao_element:
                    dataOutput["单号"].append(dan_hao_element.get_text().strip())
                else:
                    dataOutput["单号"].append(tds[2].get_text().strip())
                dataOutput["税单号"].append(tds[3].get_text().strip())
                dataOutput["汇总税单号"].append(tds[4].get_text().strip())
                dataOutput["缴款人姓名"].append(tds[5].get_text().strip())
                dataOutput["回执时间"].append(tds[6].get_text().strip())
                dataOutput["缴款人地址"].append(tds[7].get_text().strip())
                dataOutput["行邮税总额(元)"].append(tds[8].get_text().strip())
                dataOutput["税单状态"].append(tds[9].get_text().strip())
            else:
                print(f"行数据不足，跳过该行: {tds}")

def fetch_mail_detail(mail_number, start_date, end_date):
    requests.packages.urllib3.disable_warnings()

    # 直接在URL中组装参数
    url = f'https://**********/report-intl-web/a/reportIntl/customsTaxReceipt/detail?itemId={mail_number}&beginDate={start_date}&endDate={end_date}&start=0&userName=&billItem=&userOrgCode=51040075'

    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Connection': 'keep-alive',
        'Host': '**********',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }

    print(f"请求URL: {url}")
    response = session.get(url, headers=headers, verify=False)
    return response.text

def parse_mail_detail(response_text, mail_number):
    # 检查 response_text 是否有效
    if not response_text:
        print(f"邮件 {mail_number} 返回内容为空")
        return

    # 打印返回的网页内容用于调试
    print(f"邮件 {mail_number} 返回内容长度: {len(response_text)}")
    print(f"邮件 {mail_number} 返回完整内容:")
    print("=" * 80)
    print(response_text)
    print("=" * 80)

    html = BeautifulSoup(response_text, 'lxml')

    # 查找表格
    content_table = html.find('table', {'id': 'contentTable'})
    if not content_table:
        print(f"邮件 {mail_number} 未找到contentTable表格")
        # 尝试查找任何表格
        all_tables = html.find_all('table')
        print(f"邮件 {mail_number} 找到 {len(all_tables)} 个表格")
        if all_tables:
            content_table = all_tables[0]  # 使用第一个表格
        else:
            return

    target_tbody = content_table.find('tbody')
    if target_tbody:
        trs = target_tbody.find_all('tr')
        print(f"邮件 {mail_number} 找到 {len(trs)} 行数据")

        for i, tr in enumerate(trs):
            tds = tr.find_all('td')
            print(f"邮件 {mail_number} 第{i+1}行有 {len(tds)} 列数据")

            # 确保 tds 长度满足需求
            if len(tds) >= 8:
                mailDetailOutput["序号"].append(tds[0].get_text().strip())
                mailDetailOutput["物品名称"].append(tds[1].get_text().strip())
                mailDetailOutput["归类编码"].append(tds[2].get_text().strip())
                mailDetailOutput["完税价格（元）"].append(tds[3].get_text().strip())
                mailDetailOutput["行邮税率"].append(tds[4].get_text().strip())
                mailDetailOutput["行邮税金额（元）"].append(tds[5].get_text().strip())
                mailDetailOutput["邮件id"].append(tds[6].get_text().strip())
                mailDetailOutput["邮件重量(kg)"].append(tds[7].get_text().strip())
                print(f"邮件 {mail_number} 第{i+1}行数据解析成功")
            else:
                print(f"邮件 {mail_number} 第{i+1}行数据不足，只有 {len(tds)} 列: {[td.get_text().strip() for td in tds]}")
    else:
        print(f"邮件 {mail_number} 未找到tbody")
        # 尝试直接从table中查找tr
        trs = content_table.find_all('tr')
        print(f"邮件 {mail_number} 直接从table找到 {len(trs)} 行")
        if len(trs) > 1:  # 跳过表头
            for i, tr in enumerate(trs[1:], 1):
                tds = tr.find_all('td')
                print(f"邮件 {mail_number} 第{i}行有 {len(tds)} 列数据")

                if len(tds) >= 8:
                    mailDetailOutput["序号"].append(tds[0].get_text().strip())
                    mailDetailOutput["物品名称"].append(tds[1].get_text().strip())
                    mailDetailOutput["归类编码"].append(tds[2].get_text().strip())
                    mailDetailOutput["完税价格（元）"].append(tds[3].get_text().strip())
                    mailDetailOutput["行邮税率"].append(tds[4].get_text().strip())
                    mailDetailOutput["行邮税金额（元）"].append(tds[5].get_text().strip())
                    mailDetailOutput["邮件id"].append(tds[6].get_text().strip())
                    mailDetailOutput["邮件重量(kg)"].append(tds[7].get_text().strip())
                    print(f"邮件 {mail_number} 第{i}行数据解析成功")
                else:
                    print(f"邮件 {mail_number} 第{i}行数据不足，只有 {len(tds)} 列")

def process_mail_detail(mail_number, start_date, end_date):
    """
    处理邮件详情查询的主流程
    """
    print(f"开始处理邮件详情: {mail_number}, 日期范围: {start_date} - {end_date}")

    # 获取HTML数据
    response_text = fetch_mail_detail(mail_number, start_date, end_date)
    if response_text:
        print(f"邮件 {mail_number} 获取到响应，长度: {len(response_text)}")
        # 解析HTML数据
        parse_mail_detail(response_text, mail_number)
        print(f"邮件详情已成功获取并解析: {mail_number}")

        # 打印当前mailDetailOutput的数据量
        total_records = len(mailDetailOutput["序号"]) if mailDetailOutput["序号"] else 0
        print(f"当前邮件详情总记录数: {total_records}")
    else:
        print(f"无法获取邮件详情: {mail_number}")

def process_data(start_date, end_date):
    """
    处理数据的主流程，直接获取HTML并解析
    """
    # 获取HTML数据
    response_text = fetch_data(start_date, end_date)
    if response_text:
        # 解析HTML数据
        parse_data(response_text)
        print(f"数据已成功获取并解析: {start_date} - {end_date}")
    else:
        print("无法获取数据")

def process_data_with_params(start_date, end_date, itemId, billItem, userName):
    """
    处理带参数的数据查询主流程
    """
    # 如果itemId有多个邮件号，需要分别查询
    if itemId and ',' in itemId:
        # 多个邮件号，分别查询
        mail_numbers = [mail.strip() for mail in itemId.split(',') if mail.strip()]
        for mail_number in mail_numbers:
            print(f"查询邮件号: {mail_number}")
            response_text = fetch_data(start_date, end_date, mail_number, billItem, userName)
            if response_text:
                parse_data(response_text)
                print(f"邮件号 {mail_number} 数据已成功获取并解析")
            else:
                print(f"无法获取邮件号 {mail_number} 的数据")
    elif itemId and '\n' in itemId:
        # 换行分隔的多个邮件号
        mail_numbers = [mail.strip() for mail in itemId.split('\n') if mail.strip()]
        for mail_number in mail_numbers:
            print(f"查询邮件号: {mail_number}")
            response_text = fetch_data(start_date, end_date, mail_number, billItem, userName)
            if response_text:
                parse_data(response_text)
                print(f"邮件号 {mail_number} 数据已成功获取并解析")
            else:
                print(f"无法获取邮件号 {mail_number} 的数据")
    else:
        # 单个查询
        response_text = fetch_data(start_date, end_date, itemId, billItem, userName)
        if response_text:
            parse_data(response_text)
            print(f"数据已成功获取并解析: {start_date} - {end_date}")
        else:
            print("无法获取数据")


def run(title, parent):
    try:
        global username, password, session, jdptid, L, dataOutput, mailDetailOutput
        L = 1

        # 清空数据
        for key in dataOutput.keys():
            dataOutput[key] = []
        for key in mailDetailOutput.keys():
            mailDetailOutput[key] = []

        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")

        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")

        # 保存账号和密码
        tool.save_data()

        username = account_entry.get()
        password = password_entry.get()
        password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')

        start = time.perf_counter()

        i = 2
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/report-intl-web/a/reportIntl/customsTaxReceipt/list'
        result = tool.getck(username, password, session, url,'报关行')
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url,'报关行')
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)

        getzb(parent)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()

        # 检查是否有邮件详情数据
        mail_numbers_text = mail_input_textbox.get("1.0", "end-1c").strip()

        # 统计邮件详情数据量
        mail_detail_count = sum(len(mailDetailOutput[key]) for key in mailDetailOutput.keys())
        regular_data_count = sum(len(dataOutput[key]) for key in dataOutput.keys())

        tool.process_input(f"邮件详情数据总量: {mail_detail_count}")
        tool.process_input(f"常规数据总量: {regular_data_count}")

        if mail_numbers_text and mail_detail_count > 0:
            # 导出邮件详情数据
            tool.process_input("开始导出邮件详情数据...")
            mailDetailForm = pandas.DataFrame(mailDetailOutput)
            tool.process_input(f"邮件详情DataFrame行数: {len(mailDetailForm)}")

            if len(mailDetailForm) > 0:
                row = 1048570
                length = len(mailDetailForm)
                number = length // row
                for i in range(number + 1):
                    filename = "海关税收回执邮件详情" + "-" + currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx"
                    mailDetailForm[i * row:(i + 1) * row].to_excel(filename, index=False)
                    tool.process_input(f"邮件详情文件已生成: {filename}")
                tool.process_input("邮件详情写入完成共" + str(number + 1) + "个文件")
            else:
                tool.process_input("邮件详情DataFrame为空，无法导出")

        if regular_data_count > 0:
            # 导出常规数据
            tool.process_input("开始导出常规数据...")
            dataForm = pandas.DataFrame(dataOutput)
            tool.process_input(f"常规数据DataFrame行数: {len(dataForm)}")

            if len(dataForm) > 0:
                row = 1048570
                length = len(dataForm)
                number = length // row
                for i in range(number + 1):
                    filename = "海关税收回执" + "-" + currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx"
                    dataForm[i * row:(i + 1) * row].to_excel(filename, index=False)
                    tool.process_input(f"常规数据文件已生成: {filename}")
                tool.process_input("常规数据写入完成共" + str(number + 1) + "个文件")
            else:
                tool.process_input("常规数据DataFrame为空，无法导出")

        if mail_detail_count == 0 and regular_data_count == 0:
            tool.process_input("没有找到任何数据，请检查查询条件或网络连接")

        tool.process_input("写入完成")

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")


def handle_input(title, parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    output_textbox.delete("1.0", "end")
    # 清空邮件号输入框
    mail_input_textbox.delete("1.0", "end")
    # 清空税单号输入框
    bill_item_entry.delete(0, "end")
    # 清空缴款人姓名输入框
    user_name_entry.delete(0, "end")


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)


def open_func_window(parent, title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title + " Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox, root, submit_button, button_clear, \
        start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, interFlag_options, interFlag_combobox, \
        routeLevel_options, routeLevel_combobox, statusJ_options, statusJ_combobox, dataOutput, starthour_var, startminute_var, endhour_var, endminute_var, \
        starthour_combobox, startminute_combobox, endhour_combobox, endminute_combobox, tool, back_button, organization_combobox, \
        startsecond_var, startsecond_combobox, endsecond_var, endsecond_combobox, mail_input_textbox, mailDetailOutput, \
        bill_item_entry, user_name_entry

    # 构造Session
    session = requests.Session()

    today = datetime.datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行"])
    organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=1, column=0)

    # 当前日期前一天
    max_date = datetime.datetime.now() - datetime.timedelta(days=1)
    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=0, column=0, padx=10, pady=10)
    start_date_entry = DateEntry(date_container, maxdate=max_date)
    start_date_entry.grid(row=0, column=1, padx=10, pady=10)

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=0, column=2, padx=10, pady=10)
    end_date_entry = DateEntry(date_container, maxdate=max_date)
    end_date_entry.grid(row=0, column=3, padx=10, pady=10)

    # 添加税单号输入框
    bill_item_label = ttk.Label(date_container, text="税单号:")
    bill_item_label.grid(row=1, column=0, padx=10, pady=10)
    bill_item_entry = Entry(date_container, width=20)
    bill_item_entry.grid(row=1, column=1, padx=10, pady=10)

    # 添加缴款人姓名输入框
    user_name_label = ttk.Label(date_container, text="缴款人姓名:")
    user_name_label.grid(row=1, column=2, padx=10, pady=10)
    user_name_entry = Entry(date_container, width=20)
    user_name_entry.grid(row=1, column=3, padx=10, pady=10)

    # 创建邮件输入容器
    mail_container = ttk.Frame(frame)
    mail_container.grid(row=2, column=0, padx=10, pady=10)
    mail_container.grid_columnconfigure(0, weight=1)  # 让容器可以扩展

    # 添加邮件号输入框
    mail_label = ttk.Label(mail_container, text="邮件号输入（一行一个，留空则按日期查询）:")
    mail_label.grid(row=0, column=0, padx=10, pady=5)

    mail_input_textbox = scrolledtext.ScrolledText(mail_container, height=5, width=60)
    mail_input_textbox.grid(row=1, column=0, padx=10, pady=5)

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=3, column=0)

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=1, column=0, padx=10, pady=10)

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=1, padx=10, pady=10)

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=4, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
