import os
import shutil
import subprocess
import site
import PyInstaller.__main__


def run_command(command):
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    output, error = process.communicate()
    if process.returncode != 0:
        print(f"Error executing command: {command}")
        print(error.decode())
        raise RuntimeError(f"Command failed: {command}")
    return output.decode()


def modify_spec_file(spec_file):
    with open(spec_file, 'r', encoding='utf-8') as file:
        content = file.read()

    # 修改 datas 行
    content = content.replace("datas=[],",
                              "datas=[('chinapost.ico', '.'),('./common_old.onnx', 'ddddocr')],")

    with open(spec_file, 'w', encoding='utf-8') as file:
        file.write(content)


def copy_onnx_file():
    # 获取 Python 安装目录
    site_packages = site.getsitepackages()[1]
    #print(site.getsitepackages())
    # ddddocr 目录路径
    ddddocr_path = os.path.join(site_packages, 'ddddocr')

    # 源文件路径
    source_file = os.path.join(ddddocr_path, 'common_old.onnx')

    # 目标文件路径（当前目录）
    target_file = './common_old.onnx'

    # 复制文件
    shutil.copy2(source_file, target_file)
    print(f"Copied common_old.onnx to {target_file}")

def generate_requirements():
    print("Generating requirements.txt...")
    try:
        run_command("pip install pip-chill")
        with open("requirements.txt", "w") as f:
            f.write(run_command("pip-chill"))
    except Exception as e:
        print(f"Error generating requirements.txt: {e}")
        print("Falling back to pip freeze...")
        with open("requirements.txt", "w") as f:
            f.write(run_command("pip freeze"))

if __name__ == "__main__":
    app_name = "新一代神器"
    spec_file = f"{app_name}.spec"

    # 设置环境变量，告诉 Playwright 使用打包内的浏览器
    #os.environ['PLAYWRIGHT_BROWSERS_PATH'] = '0'
    # 生成 requirements.txt
    generate_requirements()

    # 第一次执行 PyInstaller 命令
    PyInstaller.__main__.run([
        'main.py',  # 替换成你的主脚本文件名
        '--onefile',
        '--icon=chinapost.ico',
        '--noconsole',  # 如果你不想显示命令行窗口
        '--distpath=dist',  # 替换成你想要输出的目录
        f'--name={app_name}',  # 使用变量设置应用程序名称
        '--hidden-import', 'babel.numbers' # 添加依赖项

    ])

    # 修改生成的 .spec 文件
    print(f"Modifying {spec_file}...")
    modify_spec_file(spec_file)

    # 复制 common.onnx 文件到打包目录
    # print("Copying common.onnx file...")
    # copy_onnx_file()

    # 重新使用 .spec 文件打包
    print("Repackaging with modified spec file...")
    run_command(f"pyinstaller {spec_file}")

    print("Packaging complete!")