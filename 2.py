import cx_Oracle
import pandas as pd


# 连接数据库
def get_db_connection():
    dsn_tns = cx_Oracle.makedsn('10.194.69.26', '1521', service_name='gkxt')
    print("正在连接数据库...")
    return cx_Oracle.connect(user='c##zxjtotal', password='Gzyqzxj2019.', dsn=dsn_tns)


# 查询 DEPTID
def get_deptid(cursor, ygxm, pbrq):
    query = f"SELECT DEPTID FROM TB_SYGL_CBGK_PBXX WHERE ygxm='{ygxm}' AND pbrq='{pbrq}'"
    print(f"查询 DEPTID: ygxm='{ygxm}', pbrq='{pbrq}'")
    cursor.execute(query)
    result = cursor.fetchone()
    if result:
        print(f"找到 DEPTID: {result[0]}")
        return result[0]
    else:
        print("没有找到 DEPTID")
        return None


# 查询 bcid 是否存在
def check_bcid_exists(cursor, bcmc, depid):
    query = f"SELECT bcid FROM TB_SYGL_CBGK_JCSJ_BCXX WHERE bcmc='{bcmc}' AND deptid='{depid}'"
    print(f"检查 bcid 是否存在: bcmc='{bcmc}', depid='{depid}'")
    cursor.execute(query)
    exists = cursor.fetchone() is not None
    if exists:
        print("找到匹配的 bcid")
    else:
        print("没有找到匹配的 bcid")
    return exists


# 执行更新操作
def update_bcid(cursor, ygxm, pbrq, bcmc, bcid):
    update_query = f"""
    UPDATE TB_SYGL_CBGK_PBXX 
    SET bcid='{bcid}', bcmc='{bcmc}' 
    WHERE ygxm='{ygxm}' AND pbrq='{pbrq}'
    """
    print(f"执行更新操作: ygxm='{ygxm}', pbrq='{pbrq}', bcmc='{bcmc}', bcid='{bcid}'")
    cursor.execute(update_query)


# 执行插入操作
def insert_bcid(cursor, ygxm, pbrq, bcmc):
    insert_query = f"""
    UPDATE TB_SYGL_CBGK_PBXX 
    SET bcmc='{bcmc}' 
    WHERE ygxm='{ygxm}' AND pbrq='{pbrq}'
    """
    print(f"执行插入操作: ygxm='{ygxm}', pbrq='{pbrq}', bcmc='{bcmc}'")
    cursor.execute(insert_query)


# 读取Excel文件
df = pd.read_excel('2.xlsx')  # 替换为你的文件路径


# 批量更新
def batch_update():
    print("开始批量更新...")
    conn = get_db_connection()
    cursor = conn.cursor()

    for index, row in df.iterrows():
        ygxm = row['姓名']
        pbrq = row['日期']
        bcmc = row['班次']

        print(f"处理第 {index + 1} 行数据: 姓名='{ygxm}', 日期='{pbrq}', 班次='{bcmc}'")

        # 查询 DEPTID
        deptid = get_deptid(cursor, ygxm, pbrq)

        if deptid:
            # 查询 bcid 是否存在
            if check_bcid_exists(cursor, bcmc, deptid):
                # 执行更新
                update_bcid(cursor, ygxm, pbrq, bcmc, deptid)
            else:
                # 执行插入
                insert_bcid(cursor, ygxm, pbrq, bcmc)

    # 提交事务并关闭连接
    conn.commit()
    print("批量更新完成，已提交事务。")
    cursor.close()
    conn.close()


# 调用批量更新函数
batch_update()
