import os
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows

try:
    # 用户输入文件路径
    excel_file_path = input("请输入Excel文件名（包括扩展名，例如input.xlsx）：")

    # 检查文件是否存在
    if not os.path.isfile(excel_file_path):
        raise FileNotFoundError("文件不存在，请检查路径是否正确。")

    # 读取Excel文件
    df = pd.read_excel(excel_file_path)

    # 用户输入保留的行数
    desired_rows = int(input("请输入要保留的行数："))

    # 检查用户输入是否合法
    if desired_rows <= 0 or desired_rows > len(df):
        raise ValueError("输入的行数不合法。")

    # 随机选择并保留指定数量的行
    selected_df = df.sample(n=desired_rows, random_state=42)

    # 创建一个Workbook对象
    wb = Workbook()
    ws = wb.active

    # 将DataFrame的数据写入到Workbook中
    for r in dataframe_to_rows(selected_df, index=False, header=True):
        ws.append(r)

    # 提取文件名和扩展名
    file_name, file_extension = os.path.splitext(excel_file_path)

    # 构造输出文件路径
    output_file_path = f'随机保留_{file_name}{file_extension}'

    # 保存Workbook到指定文件
    wb.save(output_file_path)

    print(f"已将随机选择的 {desired_rows} 行数据保存到 {output_file_path}")

except FileNotFoundError as e:
    print(f"错误：{e}")

except ValueError as e:
    print(f"错误：{e}")

except Exception as e:
    print(f"发生了一个错误：{e}")
