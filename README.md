# 邮政系统加密分析工具

这个项目包含一系列工具，用于分析邮政查询系统中的加密机制，特别是`MG9Olv7Z`参数的生成过程。通过跟踪请求、分析JavaScript代码和监控Cookie变化，帮助理解和实现模拟请求。

## 项目结构

- `encryption_analysis_tool.py`: 主要分析工具，追踪请求和监控JavaScript执行
- `request_analyzer.py`: 用于分析请求日志，提取关键信息和模式
- `extraction_script.py`: 用于提取和分析JavaScript文件中的关键函数

## 使用方法

### 1. 运行加密分析工具

```bash
python encryption_analysis_tool.py
```

此工具会启动一个Firefox浏览器，访问邮政系统的登录页面，并记录以下数据：
- 所有网络请求和响应
- JavaScript执行过程（特别是eval调用）
- Cookie变化
- 加载的JavaScript文件

运行过程中需要手动输入：
- 用户名
- 密码
- 要查询的邮件号

### 2. 分析请求日志

```bash
python request_analyzer.py
```

此工具会分析之前生成的请求日志，找出：
- `MG9Olv7Z`参数的特征和规律
- Cookie变化与参数生成之间的关系
- 可能的加密模式

### 3. 分析JavaScript文件

```bash
python extraction_script.py [目标文件或目录]
```

此工具会分析提取到的JavaScript文件，寻找：
- 关键函数（如`_$jX`和`bmF0aXZlRmlVyUHJ`）
- eval调用
- Cookie操作
- 加密/解密函数

## 分析目标

主要分析以下内容：

1. `/cas/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js` 核心加密文件
2. `enable_3bg4b5fckQas` 和 `3bg4b5fckQasP` Cookie
3. `MG9Olv7Z` 请求参数的生成过程

## 输出文件

工具会生成以下输出：

- `request_logs_*.txt` - 网络请求日志
- `eval_logs.json` - JavaScript eval调用日志
- `cookie_logs.json` - Cookie变化日志
- `extracted_js/` - 提取的JavaScript文件
- `js_analysis/` - JavaScript分析结果
- `analysis_output/` - 请求分析结果

## 依赖项

- Python 3.7+
- Playwright
- ddddocr
- 其他依赖见requirements.txt

## 安装

```bash
# 安装依赖
pip install -r requirements.txt

# 安装Playwright浏览器
python -m playwright install firefox
```

## 注意事项

- 此工具仅用于学习和研究目的
- 运行工具需要有效的邮政系统账号
- 请勿用于任何非法用途

## 加密机制分析概述

经过初步分析，加密过程可能涉及以下步骤：

1. 页面加载时，核心混淆加密文件通过eval执行一系列代码
2. 设置`enable_3bg4b5fckQas=true` Cookie激活加密机制
3. 生成复杂的`3bg4b5fckQasP` Cookie值，可能包含时间戳、随机数等信息
4. 查询API时，使用Cookie中的信息生成`MG9Olv7Z`参数

关键函数可能为`_$jX`和`bmF0aXZlRmlVyUHJ`（可能是Base64编码的函数名）。 