import asyncio
import io
import multiprocessing
import os
import socket
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
import requests,json, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading

# from requests_html import HTMLSession

from tool import Tool

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称
def getzdmailtrace(parent,mailno):
    global L
    # print ('正在处理:'+mailno)
    try:
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        data = {

            'trace_no': mailno,
            'numType': 15,
            'limit': 20

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)

        dataOutput = {
            "邮件号": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        # 提取键
        extracted_keys = extract_keys_by_delimiters(input3_textbox.get())

        # 添加新字段
        for key in extracted_keys:
            if key not in dataOutput:
                dataOutput[key] = []


        # 获取input2_textbox的值
        input2_value = re.sub(r'\s', '', input2_textbox.get())

        # 用#分割每个值
        values_list = input2_value.split('#')
        print(values_list)
        if '否' in selected2.get():
            # 通过循环来获取JSON中的数据，并添加到字典中
            for line in jsonObj:
                #print(line['opName'])
                if line['opName'] in values_list:
                    dataOutput["邮件号"].append(line.get('traceNo', ''))  # 获取JSON中的id，并存入字典中的id内
                    dataOutput["时间"].append(line.get('opTime', ''))
                    dataOutput["处理机构"].append(line.get('opOrgSimpleName', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('internalDesc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
                    groups = input3_textbox.get().split('#')
                    for group in groups:
                        if '@' in group:
                            start_char, end_char = group.split('@')
                            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                            #print("开始字符"+start_char,"结束字符"+ end_char)
                            if end_char:  # 有结束字符
                                pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                            else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                            matches = re.findall(pattern, line.get('internalDesc', ''))
                            if matches:
                                dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                            else:
                                dataOutput[start_char.rstrip(':')].append('')
        else:
            for line in jsonObj:
                if line['opName'] in values_list and ('51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]):
                    dataOutput["邮件号"].append(line.get('traceNo', ''))  # 获取JSON中的id，并存入字典中的id内
                    dataOutput["时间"].append(line.get('opTime', ''))
                    dataOutput["处理机构"].append(line.get('opOrgSimpleName', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('internalDesc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
                    groups = input3_textbox.get().split('#')
                    for group in groups:
                        if '@' in group:
                            start_char, end_char = group.split('@')
                            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                            if end_char:  # 有结束字符
                                pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                            else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                            matches = re.findall(pattern, line.get('internalDesc', ''))

                            if matches:
                                dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                            else:
                                dataOutput[start_char.rstrip(':')].append('')
    except json.JSONDecodeError:
        dataOutput = {
            "邮件号": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        dataOutput["邮件号"].append(mailno)
        dataOutput["时间"].append('')
        dataOutput["处理机构"].append('')
        dataOutput["处理机构代码"].append('')
        dataOutput["处理动作"].append('')
        dataOutput["详细说明"].append('')
        dataOutput["操作员"].append('')
        dataOutput["操作员代码"].append('')
        dataOutput["来源"].append('')
        dataOutput["备注"].append('')
    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

            # 模拟一些耗时操作
            #time.sleep(1)
            # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
        #tool.process_input('已爬' + str(L) + '件')
        # process_input('防封号,暂停1秒')
        #time.sleep(1)
    # 循环结束后释放session资源
    #session.close()
    return dataOutput

def getallmailtrace(parent,mailno):
    global L
    #print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_no': mailno,
        'numType': 15,
        'limit': 20

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    #print(r)
    dataOutput = {
        "邮件号": [],
        "时间": [],
        "处理机构": [],
        "处理机构代码": [],
        "处理动作": [],
        "详细说明": [],
        "操作员": [],
        "操作员代码": [],
        "来源": [],
        "备注": []
    }
    try:
        jsonObj = json.loads(r)


        if '否' in selected2.get():
            # 通过循环来获取JSON中的数据，并添加到字典中
            for line in jsonObj:

                dataOutput["邮件号"].append(line.get('traceNo', ''))  # 获取JSON中的id，并存入字典中的id内
                dataOutput["时间"].append(line.get('opTime', ''))
                dataOutput["处理机构"].append(line.get('opOrgSimpleName', ''))
                dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                dataOutput["处理动作"].append(line.get('opName', ''))
                dataOutput["详细说明"].append(line.get('internalDesc', ''))
                dataOutput["操作员"].append(line.get('operatorName', ''))
                dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                dataOutput["来源"].append(line.get('source', ''))
                dataOutput["备注"].append(line.get('notes', ''))
        else:
            for line in jsonObj:
                if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]:
                    dataOutput["邮件号"].append(line.get('traceNo', ''))  # 获取JSON中的id，并存入字典中的id内
                    dataOutput["时间"].append(line.get('opTime', ''))
                    dataOutput["处理机构"].append(line.get('opOrgSimpleName', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('internalDesc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
    except json.JSONDecodeError:
        dataOutput["邮件号"].append(mailno)
        dataOutput["时间"].append('')
        dataOutput["处理机构"].append('')
        dataOutput["处理机构代码"].append('')
        dataOutput["处理动作"].append('')
        dataOutput["详细说明"].append('')
        dataOutput["操作员"].append('')
        dataOutput["操作员代码"].append('')
        dataOutput["来源"].append('')
        dataOutput["备注"].append('')
    L += 1
    #time.sleep(1)
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock
            # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
        #tool.process_input('已爬' + str(L) + '件')
        # process_input('防封号,暂停1秒')
        #time.sleep(1)
    # 循环结束后释放session资源

    return dataOutput

def getlastmailtrace(parent,mailno):
    global L
    print ('正在处理:'+mailno)
    try:
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        # data = {
        #
        #     'trace_no': mailno,
        #     'numType': 15,
        #     'limit': 20
        #
        # }
        data = {

            'trace_no': mailno,
            'numType': 15

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)
        print(r)
        yjh=''
        sjsj=''
        sjs=''
        sjds=''
        sjj=''
        jds=''
        mdd=''
        yjzl=''
        zl=''
        bzzf=''
        sjr=''
        sj=''
        cljg=''
        cljgdm=''
        cldz=''
        xxsm=''
        czy=''
        czydm=''
        ly=''
        bz=''

        if '否' in selected2.get():
        # 通过循环来获取JSON中的数据，并添加到字典中
            for index, line in enumerate(jsonObj):
                if index == len(jsonObj) - 1:
                    sj=line["opTime"]
                    if "opOrgSimpleName" in line:
                        cljg=line["opOrgSimpleName"]

                    if "opOrgCode" in line:
                        cljgdm=line["opOrgCode"] # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间

                    if "opName" in line:
                        cldz=line["opName"]

                    if "internalDesc" in line:
                        xxsm=(line["internalDesc"])

                    if "operatorName" in line:
                        czy=(line["operatorName"])

                    if "operatorNo" in line:
                        czydm=(line["operatorNo"])

                    ly=(line["source"])
                    if "notes" in line:
                        bz=(line["notes"])

                else:
                    if "opName" in line:
                        if '收寄计费信息' in line["opName"]:
                            yjh = line["traceNo"]


                            if "receivePlace" in line:
                                jdss = re.findall(r'(?:上海市|北京市|天津市|重庆市|[\u4e00-\u9fa5]+(?:省|自治区))',
                                                  str(line["receivePlace"]))
                                # mdd=re.findall(r'([\u4e00-\u9fa5]+市)', str(line["receivePlace"]))
                                mdds = re.findall(r'(?:上海市|北京市|天津市|重庆市)', str(line["receivePlace"]))
                                if jdss:
                                    jds = jdss[0]

                                if mdds:
                                    mdd = mdds[0]
                                else:
                                    mdd = line["receivePlace"].replace(jds, '')
                            yjzl=line.get('bizProductName', '')
                            sjsj = line["opTime"]
                            zls = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                            if zls:
                                zl = zls[0]

                            bzzfs = re.findall(r'标准资费:(.*),', str(line["internalDesc"]))
                            if bzzfs:
                                bzzf = bzzfs[0]


                            sjrs = re.findall(r'收件人:(.*)', str(line["internalDesc"]))
                            if sjrs:
                                sjr = sjrs[0]
                            sjs=line.get('opOrgProvName', '')
                            sjds=line.get('opOrgCity', '')
                            sjj=line.get('opOrgSimpleName', '')
        else:
            for line in jsonObj:
                if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]:
                    sj=line["opTime"]
                    if "opOrgSimpleName" in line:
                        cljg=line["opOrgSimpleName"]

                    if "opOrgCode" in line:
                        cljgdm=line["opOrgCode"] # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间

                    if "opName" in line:
                        cldz=line["opName"]

                    if "internalDesc" in line:
                        xxsm=(line["internalDesc"])

                    if "operatorName" in line:
                        czy=(line["operatorName"])

                    if "operatorNo" in line:
                        czydm=(line["operatorNo"])

                    ly=(line["source"])
                    if "notes" in line:
                        bz=(line["notes"])

                else:
                    if "opName" in line:
                        if '收寄计费信息' in line["opName"]:
                            yjh = line["traceNo"]
                            if "receivePlace" in line:
                                jdss = re.findall(r'(?:上海市|北京市|天津市|重庆市|[\u4e00-\u9fa5]+(?:省|自治区))',
                                                  str(line["receivePlace"]))
                                # mdd=re.findall(r'([\u4e00-\u9fa5]+市)', str(line["receivePlace"]))
                                mdds = re.findall(r'(?:上海市|北京市|天津市|重庆市)', str(line["receivePlace"]))
                                if jdss:
                                    jds = jdss[0]

                                if mdds:
                                    mdd = mdds[0]
                                else:
                                    mdd = line["receivePlace"].replace(jds, '')

                            yjzl = line.get('bizProductName', '')
                            sjsj = line["opTime"]
                            zls = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                            if zls:
                                zl = zls[0]

                            bzzfs = re.findall(r'标准资费:(.*),', str(line["internalDesc"]))
                            if bzzfs:
                                bzzf = bzzfs[0]


                            sjrs = re.findall(r'收件人:(.*)', str(line["internalDesc"]))
                            if sjrs:
                                sjr = sjrs[0]
                            sjs = line.get('opOrgProvName', '')
                            sjds = line.get('opOrgCity', '')
                            sjj = line.get('opOrgSimpleName', '')

        dataOutput = {
            "邮件号码": [],
            "邮件种类": [],
            "收寄时间": [],
            "收寄省": [],
            "收寄地市": [],
            "收寄局": [],
            "寄达省":[],
            "寄达地市":[],
            "重量": [],
            "标准资费": [],
            "收件人": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        dataOutput["邮件号码"].append(mailno)
        dataOutput["邮件种类"].append(yjzl)
        dataOutput["收寄时间"].append(sjsj)
        dataOutput["收寄省"].append(sjs)
        dataOutput["收寄地市"].append(sjds)
        dataOutput["收寄局"].append(sjj)
        dataOutput["寄达省"].append(jds)
        dataOutput["寄达地市"].append(mdd)
        dataOutput["重量"].append(zl)
        dataOutput["标准资费"].append(bzzf)
        dataOutput["收件人"].append(sjr)
        dataOutput["时间"].append(sj)
        dataOutput["处理机构"].append(cljg)
        dataOutput["处理机构代码"].append(cljgdm)
        dataOutput["处理动作"].append(cldz)
        dataOutput["详细说明"].append(xxsm)
        dataOutput["操作员"].append(czy)
        dataOutput["操作员代码"].append(czydm)
        dataOutput["来源"].append(ly)
        dataOutput["备注"].append(bz)
    except json.JSONDecodeError:
        dataOutput = {
            "邮件号码": [],
            "邮件种类": [],
            "收寄时间": [],
            "收寄省": [],
            "收寄地市": [],
            "收寄局": [],
            "寄达省": [],
            "寄达地市": [],
            "重量": [],
            "标准资费": [],
            "收件人": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        dataOutput["邮件号码"].append(mailno)
        dataOutput["邮件种类"].append('')
        dataOutput["收寄时间"].append('')
        dataOutput["收寄省"].append('')
        dataOutput["收寄地市"].append('')
        dataOutput["收寄局"].append('')
        dataOutput["寄达省"].append('')
        dataOutput["寄达地市"].append('')
        dataOutput["重量"].append('')
        dataOutput["标准资费"].append('')
        dataOutput["收件人"].append('')
        dataOutput["时间"].append('')
        dataOutput["处理机构"].append('')
        dataOutput["处理机构代码"].append('')
        dataOutput["处理动作"].append('')
        dataOutput["详细说明"].append('')
        dataOutput["操作员"].append('')
        dataOutput["操作员代码"].append('')
        dataOutput["来源"].append('')
        dataOutput["备注"].append('')

    # finally:
    #     # 释放线程锁
    #     lock.release()

    L += 1
    if L % 1000 == 0:

        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
        # 模拟一些耗时操作
        #time.sleep(1)
        #tool.process_input('已爬'+str(L)+'件')
        #time.sleep(1)
    # 循环结束后释放session资源
    #session.close()
    return dataOutput

 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data


        # 构造Session
        session = requests.Session()
        #session = HTMLSession()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address :
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')
        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        #url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        url= 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        # # 创建事件循环并设置为当前事件循环
        # loop = asyncio.new_event_loop()
        # asyncio.set_event_loop(loop)
        #
        # # 在子线程中运行异步代码
        # result = loop.run_until_complete(tool.getck(username, password, session, url))
        # # 处理异步函数的结果
        # jdptid, userName = result  # 解包元组
        # print("Value 1:", jdptid)
        # print("Value 2:", userName)
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #pool = multiprocessing.Pool(processes=int(threads_combobox.get()))
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果

        # 并发执行并获取结果
        if '全程' in selected.get():
            merged_data = {
                "邮件号": [],
                "时间": [],
                "处理机构": [],
                "处理机构代码": [],
                "处理动作": [],
                "详细说明": [],
                "操作员": [],
                "操作员代码": [],
                "来源": [],
                "备注": []
            }
            #results = pool.map(getallmailtrace, datalist)
            #results = pool.map(lambda mailno: getallmailtrace(mailno, parent), datalist)
            getmailtrace_bound = partial(getallmailtrace, parent)

        elif '最后' in selected.get():
            merged_data =  {
                "邮件号码": [],
                "邮件种类": [],
                "收寄时间": [],
                "收寄省": [],
                "收寄地市": [],
                "收寄局": [],
                "寄达省":[],
                "寄达地市":[],
                "重量": [],
                "标准资费": [],
                "收件人": [],
                "时间": [],
                "处理机构": [],
                "处理机构代码": [],
                "处理动作": [],
                "详细说明": [],
                "操作员": [],
                "操作员代码": [],
                "来源": [],
                "备注": []
    }
            #results = pool.map(getlastmailtrace, datalist)
            #results = pool.map(lambda mailno: getlastmailtrace(mailno, parent), datalist)
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getlastmailtrace, parent)

        else:
            merged_data = {
                "邮件号": [],
                "时间": [],
                "处理机构": [],
                "处理机构代码": [],
                "处理动作": [],
                "详细说明": [],
                "操作员": [],
                "操作员代码": [],
                "来源": [],
                "备注": []
            }
            #results = pool.map(getallmailtrace, datalist)
            # 提取键
            extracted_keys = extract_keys_by_delimiters(input3_textbox.get())
            # 添加新字段
            for key in extracted_keys:
                if key not in merged_data:
                    merged_data[key] = []
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getzdmailtrace, parent)

        results = pool.map(getmailtrace_bound, datalist)
        merged_data = reduce(merge_dicts, results, merged_data)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()
        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("邮件"+selected.get()+"-"+
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')

def extract_keys_by_delimiters(input_str):
    results = {}
    groups = input_str.split('#')
    for group in groups:
        if '@' in group:
            start_char, _ = group.split('@')# 使用_来丢弃分割后的第二个元素（如果有的话）
            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
            key = start_char.rstrip(':')  # 去掉冒号
            results[key] = []  # 初始化为空列表
            #print("新增键："+str(results))
    return results


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()

def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, selected2,submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,\
        back_button,organization_combobox,input2_textbox,input3_textbox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='全程轨迹')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=0, column=0)
    radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    radio_button1.grid(row=0, column=1)

    radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    radio_button2.grid(row=0, column=2, padx=15)

    radio_button3 = ttk.Radiobutton(input_label_container, text="指定轨迹", value="指定轨迹", variable=selected)
    radio_button3.grid(row=0, column=3)

    # 创建单选按钮
    selected2 = tk.StringVar(value='否')
    radio2_label = ttk.Label(input_label_container, text="是否只筛广航轨迹:")
    radio2_label.grid(row=1, column=0, padx=10, pady=10)
    radio2_button1 = ttk.Radiobutton(input_label_container, text="是", value='是', variable=selected2)
    radio2_button1.grid(row=1, column=1, padx=5, pady=10)

    radio2_button2 = ttk.Radiobutton(input_label_container, text="否", value='否', variable=selected2)
    radio2_button2.grid(row=1, column=2, padx=5, pady=10)


    # 添加指定处理动作
    input2_label = ttk.Label(input_label_container, text="筛处理动作(多个处理动作用#分隔):")
    input2_label.grid(row=2, column=0, padx=10, pady=10)
    # input_label.pack()
    input2_textbox = tk.Entry(input_label_container, width=30)
    input2_textbox.grid(row=2, column=1, padx=10, pady=10, columnspan=1)

    input3_label = ttk.Label(input_label_container, text="提取详细明细(开始和结束字符，用@隔开，多组内容用#分隔):")
    input3_label.grid(row=3, column=0, padx=10, pady=10)
    # input_label.pack()
    input3_textbox = tk.Entry(input_label_container, width=30)
    input3_textbox.grid(row=3, column=1, padx=10, pady=10, columnspan=1)
    input3_label2 = ttk.Label(input_label_container, text="例如AAA:@,#BBB:@,#CCC:@,#DDD:@")
    input3_label2.grid(row=3, column=2, padx=10, pady=10)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=4, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=4, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=5, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=5, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()