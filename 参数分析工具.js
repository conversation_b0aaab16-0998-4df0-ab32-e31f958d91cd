// ==UserScript==
// @name         邮政查询参数分析工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  完整捕获和分析邮政系统加密参数生成逻辑
// <AUTHOR>
// @match        https://**********/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 全局存储对象
    const capturedData = {
        cookies: {},
        requests: [],
        mg9olv7zValues: [],
        evalCode: [],
        functions: {},
        successfulRequests: []
    };

    // 自定义日志函数
    function logToConsole(message, type = 'info') {
        console.log(`[参数分析工具] ${type.toUpperCase()}: ${message}`);
    }

    // 保存捕获的数据
    function saveData() {
        localStorage.setItem('capturedApiData', JSON.stringify(capturedData));
        logToConsole('数据已保存到localStorage');
    }

    // 导出数据到文件
    function exportToFile(data, filename) {
        const dataStr = JSON.stringify(data, null, 2);
        const blob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        logToConsole(`已导出到文件: ${filename}`);
    }

    // 1. 监控eval执行 - 在脚本最早执行
    logToConsole('安装eval监控...');
    const originalEval = window.eval;
    window.eval = function(code) {
        try {
            // 记录eval执行的代码
            const codePreview = code.substring(0, 100) + (code.length > 100 ? '...' : '');
            logToConsole(`执行eval: ${codePreview}`);

            // 检查是否包含关键函数
            const isKeyCode = code.includes('_$j$') ||
                             code.includes('_$al') ||
                             code.includes('3bg4b5fckQas') ||
                             code.includes('MG9Olv7Z');

            if (isKeyCode) {
                logToConsole('捕获到关键eval代码');

                // 记录时间戳和代码
                capturedData.evalCode.push({
                    timestamp: new Date().toISOString(),
                    code: code,
                    length: code.length
                });

                // 保存数据
                saveData();

                // 导出较大的代码块
                if (code.length > 1000) {
                    const blob = new Blob([code], {type: 'text/javascript'});
                    const url = URL.createObjectURL(blob);
                    const filename = `eval_code_${Date.now()}.js`;

                    capturedData.evalCode[capturedData.evalCode.length - 1].filename = filename;

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.style.display = 'none';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }
            }

            // 执行原始代码
            return originalEval.call(this, code);
        } catch (e) {
            logToConsole(`eval执行错误: ${e.message}`, 'error');
            throw e;
        }
    };

    // 2. 监控XMLHttpRequest
    logToConsole('安装XHR监控...');
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;

        xhr.open = function(...args) {
            const method = args[0];
            const url = args[1];

            this._url = url;
            this._method = method;

            if (url && typeof url === 'string') {
                try {
                    const urlObj = new URL(url, window.location.origin);

                    // 检查是否包含目标参数
                    if (urlObj.searchParams.has('MG9Olv7Z')) {
                        const mg9olv7zValue = urlObj.searchParams.get('MG9Olv7Z');
                        logToConsole(`捕获到MG9Olv7Z参数: ${mg9olv7zValue}`);

                        // 记录完整URL和所有参数
                        const params = {};
                        for (let [key, value] of urlObj.searchParams) {
                            params[key] = value;
                        }

                        // 获取当前所有Cookie
                        const cookies = {};
                        document.cookie.split(';').forEach(cookie => {
                            const parts = cookie.trim().split('=');
                            if (parts.length >= 2) {
                                const name = parts[0];
                                const value = parts.slice(1).join('=');
                                cookies[name] = value;
                            }
                        });

                        capturedData.requests.push({
                            timestamp: new Date().toISOString(),
                            url: url,
                            method: method,
                            params: params,
                            cookies: cookies
                        });

                        capturedData.mg9olv7zValues.push({
                            timestamp: new Date().toISOString(),
                            value: mg9olv7zValue,
                            url: url
                        });

                        // 保存数据
                        saveData();

                        // 记录调用栈
                        logToConsole('MG9Olv7Z参数生成调用栈:');
                        console.trace();
                    }
                } catch (e) {
                    logToConsole(`URL解析错误: ${e.message}`, 'error');
                }
            }

            return originalOpen.apply(this, args);
        };

        xhr.send = function(...args) {
            if (this._url && this._url.includes('MG9Olv7Z')) {
                logToConsole(`发送请求: ${this._method} ${this._url}`);
                if (args[0]) {
                    logToConsole(`请求体: ${args[0]}`);
                }
            }
            return originalSend.apply(this, args);
        };

        return xhr;
    };

    // 3. 监控Fetch API
    logToConsole('安装Fetch监控...');
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && url.includes('MG9Olv7Z')) {
            try {
                const urlObj = new URL(url, window.location.origin);
                const mg9olv7zValue = urlObj.searchParams.get('MG9Olv7Z');

                logToConsole(`Fetch捕获到MG9Olv7Z参数: ${mg9olv7zValue}`);

                // 获取当前所有Cookie
                const cookies = {};
                document.cookie.split(';').forEach(cookie => {
                    const parts = cookie.trim().split('=');
                    if (parts.length >= 2) {
                        const name = parts[0];
                        const value = parts.slice(1).join('=');
                        cookies[name] = value;
                    }
                });

                // 导出请求信息
                const params = {};
                for (let [key, value] of urlObj.searchParams) {
                    params[key] = value;
                }

                capturedData.requests.push({
                    timestamp: new Date().toISOString(),
                    api: 'fetch',
                    url: url,
                    params: params,
                    cookies: cookies
                });

                capturedData.mg9olv7zValues.push({
                    timestamp: new Date().toISOString(),
                    value: mg9olv7zValue,
                    url: url
                });

                // 保存数据
                saveData();

                // 记录调用栈
                logToConsole('MG9Olv7Z参数生成调用栈:');
                console.trace();
            } catch (e) {
                logToConsole(`Fetch URL解析错误: ${e.message}`, 'error');
            }
        }
        return originalFetch.apply(this, args);
    };

    // 4. 监控URL构造函数
    logToConsole('安装URL监控...');
    const originalURL = window.URL;
    window.URL = function(url, base) {
        const urlInstance = new originalURL(url, base);

        // 检查是否包含目标参数
        if (typeof url === 'string' && url.includes('MG9Olv7Z')) {
            logToConsole(`创建包含MG9Olv7Z的URL: ${url}`);
            console.trace('URL创建调用栈');
        }

        return urlInstance;
    };
    window.URL.prototype = originalURL.prototype;
    window.URL.createObjectURL = originalURL.createObjectURL;
    window.URL.revokeObjectURL = originalURL.revokeObjectURL;

    // 5. 监控Cookie变化
    logToConsole('安装Cookie监控...');
    const originalDocumentCookie = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie');

    Object.defineProperty(document, 'cookie', {
        set: function(val) {
            try {
                // 记录所有Cookie设置
                logToConsole(`设置Cookie: ${val}`);

                // 特别记录目标Cookie
                if (val.includes('3bg4b5fckQasP=') || val.includes('enable_3bg4b5fckQas=')) {
                    const cookieName = val.split('=')[0];
                    const cookieValue = val.substring(val.indexOf('=') + 1, val.includes(';') ? val.indexOf(';') : val.length);

                    logToConsole(`捕获关键Cookie: ${cookieName}=${cookieValue.substring(0, 20)}...`);

                    // 保存Cookie内容
                    capturedData.cookies[cookieName] = {
                        value: cookieValue,
                        timestamp: new Date().toISOString(),
                        fullCookie: val
                    };

                    // 记录调用栈
                    logToConsole('Cookie设置调用栈:');
                    console.trace();

                    // 保存数据
                    saveData();
                }
            } catch (e) {
                logToConsole(`Cookie解析错误: ${e.message}`, 'error');
            }

            return originalDocumentCookie.set.call(this, val);
        },
        get: function() {
            return originalDocumentCookie.get.call(this);
        }
    });

    // 6. 监控关键函数
    // 在页面加载后尝试获取关键函数定义
    window.addEventListener('load', function() {
        logToConsole('页面加载完成，开始提取关键函数...');

        // 延迟执行，确保所有脚本已加载
        setTimeout(function() {
            // 提取可能的关键函数列表
            const functionNames = [
                '_$j$', '_$al', '_$jX', '_$_P', '_$ag', '_$hE', '_$kz', '_$_i',
                'Z8XHJJY'
            ];

            functionNames.forEach(funcName => {
                try {
                    // 使用Function构造器尝试访问全局作用域中的函数
                    const funcStr = `
                        try {
                            return typeof ${funcName} === "function" ? ${funcName}.toString() : null;
                        } catch(e) {
                            return null;
                        }
                    `;
                    const funcDef = new Function(funcStr)();

                    if (funcDef) {
                        logToConsole(`成功提取函数 ${funcName}`);
                        capturedData.functions[funcName] = {
                            definition: funcDef,
                            timestamp: new Date().toISOString()
                        };
                        saveData();
                    }
                } catch (e) {
                    logToConsole(`提取函数 ${funcName} 失败: ${e.message}`, 'error');
                }
            });

            // 查找window中的可疑对象
            Object.keys(window).forEach(key => {
                if (key.startsWith('_$') || key.includes('MG9Olv7Z') || key.includes('3bg4b5fckQas')) {
                    try {
                        const value = window[key];
                        const type = typeof value;
                        logToConsole(`发现可疑全局对象: ${key} (${type})`);

                        if (type === 'function') {
                            capturedData.functions[key] = {
                                definition: value.toString(),
                                timestamp: new Date().toISOString()
                            };
                        } else if (type === 'object' && value !== null) {
                            capturedData.functions[key] = {
                                properties: Object.keys(value),
                                timestamp: new Date().toISOString()
                            };
                        }
                        saveData();
                    } catch (e) {
                        logToConsole(`访问对象 ${key} 失败: ${e.message}`, 'error');
                    }
                }
            });

            logToConsole('关键函数提取完成');
        }, 5000); // 等待5秒确保页面完全加载
    });

    // 7. 创建UI界面
    window.addEventListener('load', function() {
        setTimeout(function() {
            try {
                // 创建日志区域
                const logDiv = document.createElement('div');
                logDiv.style.position = 'fixed';
                logDiv.style.bottom = '10px';
                logDiv.style.right = '10px';
                logDiv.style.width = '400px';
                logDiv.style.height = '300px';
                logDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                logDiv.style.color = '#fff';
                logDiv.style.padding = '10px';
                logDiv.style.overflow = 'auto';
                logDiv.style.zIndex = '10000';
                logDiv.style.fontSize = '12px';
                logDiv.style.fontFamily = 'monospace';
                logDiv.style.borderRadius = '5px';
                logDiv.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';

                // 添加标题和控制栏
                const titleBar = document.createElement('div');
                titleBar.style.display = 'flex';
                titleBar.style.justifyContent = 'space-between';
                titleBar.style.marginBottom = '10px';
                titleBar.style.borderBottom = '1px solid #555';
                titleBar.style.paddingBottom = '5px';

                const title = document.createElement('div');
                title.textContent = '邮政查询参数分析工具';
                title.style.fontWeight = 'bold';

                const controlButtons = document.createElement('div');

                titleBar.appendChild(title);
                titleBar.appendChild(controlButtons);
                logDiv.appendChild(titleBar);

                // 创建日志内容区
                const logContent = document.createElement('div');
                logContent.style.height = 'calc(100% - 70px)';
                logContent.style.overflowY = 'auto';
                logDiv.appendChild(logContent);

                // 创建状态栏
                const statusBar = document.createElement('div');
                statusBar.style.marginTop = '10px';
                statusBar.style.fontSize = '10px';
                statusBar.style.color = '#aaa';
                statusBar.textContent = '等待数据捕获...';
                logDiv.appendChild(statusBar);

                // 添加功能按钮
                const buttonBar = document.createElement('div');
                buttonBar.style.display = 'flex';
                buttonBar.style.justifyContent = 'space-between';
                buttonBar.style.marginTop = '10px';

                // 导出数据按钮
                const exportBtn = document.createElement('button');
                exportBtn.textContent = '导出数据';
                exportBtn.style.backgroundColor = '#2196F3';
                exportBtn.style.color = '#fff';
                exportBtn.style.border = 'none';
                exportBtn.style.padding = '5px 10px';
                exportBtn.style.borderRadius = '3px';
                exportBtn.style.cursor = 'pointer';
                exportBtn.addEventListener('click', function() {
                    exportToFile(capturedData, `api_params_${Date.now()}.json`);

                    // 更新状态栏
                    statusBar.textContent = `数据已导出 - ${new Date().toLocaleTimeString()}`;

                    // 添加日志
                    addLogEntry('导出全部捕获数据');
                });

                // 清除日志按钮
                const clearLogBtn = document.createElement('button');
                clearLogBtn.textContent = '清除日志';
                clearLogBtn.style.backgroundColor = '#F44336';
                clearLogBtn.style.color = '#fff';
                clearLogBtn.style.border = 'none';
                clearLogBtn.style.padding = '5px 10px';
                clearLogBtn.style.borderRadius = '3px';
                clearLogBtn.style.cursor = 'pointer';
                clearLogBtn.addEventListener('click', function() {
                    logContent.innerHTML = '';

                    // 更新状态栏
                    statusBar.textContent = `日志已清除 - ${new Date().toLocaleTimeString()}`;
                });

                // 隐藏/显示按钮
                const toggleBtn = document.createElement('button');
                toggleBtn.textContent = '隐藏';
                toggleBtn.style.backgroundColor = '#4CAF50';
                toggleBtn.style.color = '#fff';
                toggleBtn.style.border = 'none';
                toggleBtn.style.padding = '5px 10px';
                toggleBtn.style.borderRadius = '3px';
                toggleBtn.style.cursor = 'pointer';

                let isVisible = true;
                toggleBtn.addEventListener('click', function() {
                    if (isVisible) {
                        logContent.style.display = 'none';
                        statusBar.style.display = 'none';
                        buttonBar.style.display = 'none';
                        logDiv.style.height = 'auto';
                        toggleBtn.textContent = '显示';
                    } else {
                        logContent.style.display = 'block';
                        statusBar.style.display = 'block';
                        buttonBar.style.display = 'flex';
                        logDiv.style.height = '300px';
                        toggleBtn.textContent = '隐藏';
                    }
                    isVisible = !isVisible;
                });

                controlButtons.appendChild(toggleBtn);

                buttonBar.appendChild(exportBtn);
                buttonBar.appendChild(clearLogBtn);
                logDiv.appendChild(buttonBar);

                // 添加到文档
                document.body.appendChild(logDiv);

                // 自定义日志函数
                function addLogEntry(message, type = 'info') {
                    const entry = document.createElement('div');
                    entry.style.marginBottom = '5px';
                    entry.style.borderLeft = '3px solid ' + (type === 'error' ? '#F44336' : '#4CAF50');
                    entry.style.paddingLeft = '5px';
                    entry.style.fontSize = '11px';

                    const timestamp = document.createElement('span');
                    timestamp.textContent = `[${new Date().toLocaleTimeString()}] `;
                    timestamp.style.color = '#aaa';

                    const messageText = document.createElement('span');
                    messageText.textContent = message;

                    entry.appendChild(timestamp);
                    entry.appendChild(messageText);

                    logContent.appendChild(entry);
                    logContent.scrollTop = logContent.scrollHeight;

                    // 更新状态栏
                    statusBar.textContent = `最近活动: ${message} - ${new Date().toLocaleTimeString()}`;
                }

                // 覆盖控制台日志函数
                const originalConsoleLog = logToConsole;
                logToConsole = function(message, type = 'info') {
                    // 仍然调用原始控制台日志
                    originalConsoleLog(message, type);

                    // 添加到UI
                    if (logContent) {
                        addLogEntry(message, type);
                    }
                };

                // 初始化日志
                addLogEntry('参数分析工具已启动');

                // 显示已捕获的数据统计
                if (capturedData.mg9olv7zValues.length > 0) {
                    addLogEntry(`已捕获 ${capturedData.mg9olv7zValues.length} 个MG9Olv7Z参数`);
                }

                if (capturedData.evalCode.length > 0) {
                    addLogEntry(`已捕获 ${capturedData.evalCode.length} 段eval代码`);
                }

                if (Object.keys(capturedData.cookies).length > 0) {
                    addLogEntry(`已捕获 ${Object.keys(capturedData.cookies).length} 个关键Cookie`);
                }
            } catch (e) {
                console.error('创建UI界面失败:', e);
            }
        }, 1000);
    });

    // 精确监控成功的查询请求
    function installRequestMonitor() {
        // 记录原始的请求发送
        const originalSend = XMLHttpRequest.prototype.send;
        XMLHttpRequest.prototype.send = function(body) {
            // 保存请求URL
            const url = this._url || "";
            
            if (url.includes('queryCurrentTraceByTrace_nos')) {
                logToConsole(`发送查询请求: ${url}`);
                logToConsole(`请求体: ${body}`);
                
                // 捕获完整的请求信息
                const requestInfo = {
                    timestamp: new Date().toISOString(),
                    url: url,
                    body: body,
                    headers: {}
                };
                
                // 保存当前所有cookie
                const cookies = {};
                document.cookie.split(';').forEach(cookie => {
                    const parts = cookie.trim().split('=');
                    if (parts.length >= 2) {
                        cookies[parts[0]] = parts.slice(1).join('=');
                    }
                });
                requestInfo.cookies = cookies;
                
                // 监听响应
                this.addEventListener('load', function() {
                    requestInfo.status = this.status;
                    requestInfo.statusText = this.statusText;
                    requestInfo.response = this.responseText;
                    
                    if (this.status >= 200 && this.status < 300) {
                        logToConsole(`查询请求成功: ${this.status}`);
                        requestInfo.success = true;
                    } else {
                        logToConsole(`查询请求失败: ${this.status} ${this.statusText}`, 'error');
                        requestInfo.success = false;
                    }
                    
                    capturedData.successfulRequests = capturedData.successfulRequests || [];
                    capturedData.successfulRequests.push(requestInfo);
                    saveData();
                    
                    // 导出成功的请求
                    if (this.status >= 200 && this.status < 300) {
                        exportToFile(requestInfo, `successful_request_${Date.now()}.json`);
                    }
                });
            }
            
            return originalSend.apply(this, arguments);
        };
        
        logToConsole('已安装详细请求监控');
    }

    // 安装更详细的header监控
    function installHeaderMonitor() {
        const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
        XMLHttpRequest.prototype.setRequestHeader = function(header, value) {
            // 记录header设置
            if (this._url && this._url.includes('queryCurrentTraceByTrace_nos')) {
                logToConsole(`设置请求头: ${header} = ${value}`);
                
                // 保存header信息
                if (!this._headers) this._headers = {};
                this._headers[header] = value;
            }
            
            return originalSetRequestHeader.apply(this, arguments);
        };
        
        logToConsole('已安装请求头监控');
    }

    // 在页面加载后安装这些监控
    window.addEventListener('load', function() {
        setTimeout(function() {
            installRequestMonitor();
            installHeaderMonitor();
            logToConsole('详细请求监控已启用');
        }, 1000);
    });

    logToConsole('参数分析工具初始化完成');
})();