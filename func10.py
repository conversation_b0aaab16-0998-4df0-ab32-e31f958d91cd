import io
import os
import re
import socket
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
import requests,json, time

from datetime import datetime
import pandas
from multiprocessing.dummy import Pool
import threading
from tool import Tool

# 修改全局变量 dataOutput 为线程安全的队列

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称
def getzb(mailno):
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpspacket/queryCurrentPacketByContainNos'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'contain_nos': mailno,
        'containOrVessel': 2
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    #print(r)
    try:
        jsonObj = json.loads(r)
    except json.JSONDecodeError:
        # 处理错误情况，例如设置默认值或记录日志
        jsonObj = None

    if jsonObj:
        traceNo = jsonObj[0]['traceNo']
    else:
        traceNo = ''
    print(traceNo)
    return traceNo
def getnextOrgName(mailno):

    #print ('正在处理:'+str(mailno))
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpspacket/queryCurrentPacketByContainNos'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'contain_nos': mailno,
        'containOrVessel': 1
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    #print(r)
    try:
        jsonObj = json.loads(r)
    except json.JSONDecodeError:
        # 处理错误情况，例如设置默认值或记录日志
        jsonObj = None

    if jsonObj:
        nextOrgName=jsonObj[0]['nextOrgName']
        nextOrgCode= jsonObj[0]['nextOrgCode']
        containerNo=jsonObj[0]['containerNo']
        lastOrgName=jsonObj[0]['lastOrgName']
        lastOrgCode=jsonObj[0]['lastOrgCode']
        deliveryUserName=jsonObj[0]['deliveryUserName']
        if 'weight' in jsonObj[0]:
            weight=jsonObj[0]['weight']
        else:
            weight=''
        #dispatchDate=jsonObj[0]['dispatchDate']
        if 'dispatchDate' in jsonObj[0]:
            # Convert the Unix timestamp to a datetime object
            datetime_object = datetime.fromtimestamp(int(jsonObj[0]['dispatchDate']) / 1000)

            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
            dispatchDate = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
        else:
            dispatchDate=''
    else:
        nextOrgName=''
        nextOrgCode=''
        containerNo=''
        lastOrgName=''
        lastOrgCode=''
        deliveryUserName=''
        weight=0
        dispatchDate=''
    return nextOrgName,nextOrgCode,containerNo,lastOrgName,lastOrgCode,deliveryUserName,weight,dispatchDate
def getjianshu(mailno):
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpspacket/queryNumberOfInternals'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'traceNo': mailno,
        'dispatchDate': '2024-07-10 02:39:21'
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text
    print(r)

    return r

def getlastmailtrace(parent,mailno):
    global L
    #print ('正在处理:'+mailno)
    try:
        r=getnextOrgName(mailno)
        nextOrgName=r[0]
        nextOrgCode=r[1]
        containerNo=r[2]
        lastOrgName=r[3]
        lastOrgCode=r[4]
        deliveryUserName=r[5]
        weight=r[6]
        dispatchDate=r[7]
        jianshu=getjianshu(mailno)

        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/querypush-web/a/qps/qpspacket/queryPacketsByContainNo'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        data = {

            'contain_no': mailno

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)
        #print(r)
        dataOutput = {
            "总包号码": [],
            "总包条码": [],
            "原寄局": [],
            "原寄局代码": [],
            "总包种类": [],
            "封发时间": [],
            '寄达局':[],
            '寄达局代码': [],
            "重量": [],
            "件数": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        if jsonObj:
            # 通过循环来获取JSON中的数据，并添加到字典中
            if '否' in selected2.get():
                line = jsonObj[-1]


                #dataOutput["时间"].append(line.get('opTime', ''))
                op_time_value = line.get('opTime', '')
                #print(op_time_value)
                if op_time_value:
                    # Convert the timestamp to a datetime object
                    datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)
                    # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                    formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')
                    # Append the formatted datetime to the '时间' key in dataOutput
                    dataOutput["时间"].append(formatted_datetime)
                else:
                    # Handle the case when 'opTime' is empty
                    dataOutput["时间"].append("")
                dataOutput["总包号码"].append(containerNo)
                dataOutput["总包条码"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                dataOutput["原寄局"].append(lastOrgName)
                dataOutput["原寄局代码"].append(lastOrgCode)
                dataOutput["总包种类"].append(deliveryUserName)
                dataOutput["封发时间"].append(dispatchDate)

                dataOutput["寄达局"].append(nextOrgName)
                dataOutput["寄达局代码"].append(nextOrgCode)
                dataOutput["重量"].append(weight)
                dataOutput["件数"].append(jianshu)
                dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                dataOutput["处理动作"].append(line.get('opName', ''))
                dataOutput["详细说明"].append(line.get('desc', ''))
                dataOutput["操作员"].append(line.get('operatorName', ''))
                dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                dataOutput["来源"].append(line.get('source', ''))
                dataOutput["备注"].append(line.get('notes', ''))
            else:
                sj = ''
                cljg = ''
                cljgdm = ''
                cldz = ''
                xxsm = ''
                czy = ''
                czydm = ''
                ly = ''
                bz = ''
                for line in jsonObj:

                    if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]:
                        # 通过循环来获取JSON中的数据，并添加到字典中
                        op_time_value = line.get('opTime', '')
                        if op_time_value:
                            # Convert the timestamp to a datetime object
                            datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                            # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                            sj = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        #sj = line["opTime"]
                        if "opOrgSimplename" in line:
                            cljg = line["opOrgSimplename"]

                        if "opOrgCode" in line:
                            cljgdm = line["opOrgCode"]  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间

                        if "opName" in line:
                            cldz = line["opName"]

                        if "desc" in line:
                            xxsm = line["desc"]

                        if "operatorName" in line:
                            czy = line["operatorName"]

                        if "operatorNo" in line:
                            czydm = line["operatorNo"]

                        ly = line["source"]
                        if "notes" in line:
                            bz = line["notes"]

                dataOutput["总包号码"].append(containerNo)
                dataOutput["总包条码"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                dataOutput["原寄局"].append(lastOrgName)
                dataOutput["原寄局代码"].append(lastOrgCode)
                dataOutput["总包种类"].append(deliveryUserName)
                dataOutput["封发时间"].append(dispatchDate)

                dataOutput["寄达局"].append(nextOrgName)
                dataOutput["寄达局代码"].append(nextOrgCode)
                dataOutput["重量"].append(weight)
                dataOutput["件数"].append(jianshu)
                dataOutput["时间"].append(sj)
                dataOutput["处理机构"].append(cljg)
                dataOutput["处理机构代码"].append(cljgdm)
                dataOutput["处理动作"].append(cldz)
                dataOutput["详细说明"].append(xxsm)
                dataOutput["操作员"].append(czy)
                dataOutput["操作员代码"].append(czydm)
                dataOutput["来源"].append(ly)
                dataOutput["备注"].append(bz)
    except json.JSONDecodeError:
        dataOutput = {
            "总包号码": [],
            "总包条码": [],
            "原寄局": [],
            "原寄局代码": [],
            "总包种类": [],
            "封发时间": [],
            '寄达局': [],
            '寄达局代码': [],
            "重量": [],
            "件数": [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        dataOutput["总包号码"].append('')
        dataOutput["总包条码"].append(mailno)
        dataOutput["原寄局"].append('')
        dataOutput["原寄局代码"].append('')
        dataOutput["总包种类"].append('')
        dataOutput["封发时间"].append('')
        dataOutput["寄达局"].append(nextOrgName)
        dataOutput["寄达局代码"].append(nextOrgCode)
        dataOutput["重量"].append('')
        dataOutput["件数"].append('')
        dataOutput["时间"].append('')
        dataOutput["处理机构"].append('')
        dataOutput["处理机构代码"].append('')
        dataOutput["处理动作"].append('')
        dataOutput["详细说明"].append('')
        dataOutput["操作员"].append('')
        dataOutput["操作员代码"].append('')
        dataOutput["来源"].append('')
        dataOutput["备注"].append('')


    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

        # 模拟一些耗时操作
        #time.sleep(1)
        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    print(dataOutput)
    return dataOutput
    #print(dataOutput.get())


def getallmailtrace(parent,mailno):
    global L

    try:
    #print ('正在处理:'+mailno)
        r = getnextOrgName(mailno)
        nextOrgName = r[0]
        nextOrgCode = r[1]

        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/querypush-web/a/qps/qpspacket/queryPacketsByContainNo'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        data = {

            'contain_no': mailno

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)
        dataOutput = {

            "总包号": [],
            '寄达局': [],
            '寄达局代码': [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []

        }
        if '否' in selected2.get():
            for line in jsonObj:
                # 通过循环来获取JSON中的数据，并添加到字典中
                dataOutput["总包号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                # dataOutput["时间"].append(line.get('opTime', ''))
                op_time_value = line.get('opTime', '')

                if op_time_value:
                    # Convert the timestamp to a datetime object
                    datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                    # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                    formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                    # Append the formatted datetime to the '时间' key in dataOutput
                    dataOutput["时间"].append(formatted_datetime)
                else:
                    # Handle the case when 'opTime' is empty
                    dataOutput["时间"].append("")

                dataOutput["寄达局"].append(nextOrgName)
                dataOutput["寄达局代码"].append(nextOrgCode)
                dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                dataOutput["处理动作"].append(line.get('opName', ''))
                dataOutput["详细说明"].append(line.get('desc', ''))
                dataOutput["操作员"].append(line.get('operatorName', ''))
                dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                dataOutput["来源"].append(line.get('source', ''))
                dataOutput["备注"].append(line.get('notes', ''))
        else:
            for line in jsonObj:
                if '51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]:
                    # 通过循环来获取JSON中的数据，并添加到字典中
                    dataOutput["总包号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                    # dataOutput["时间"].append(line.get('opTime', ''))
                    op_time_value = line.get('opTime', '')

                    if op_time_value:
                        # Convert the timestamp to a datetime object
                        datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                        # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                        formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        # Append the formatted datetime to the '时间' key in dataOutput
                        dataOutput["时间"].append(formatted_datetime)
                    else:
                        # Handle the case when 'opTime' is empty
                        dataOutput["时间"].append("")
                    dataOutput["寄达局"].append(nextOrgName)
                    dataOutput["寄达局代码"].append(nextOrgCode)
                    dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('desc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
    except json.JSONDecodeError:
            print(r)
            dataOutput = {

               "总包号": [],
               '寄达局': [],
               '寄达局代码': [],
               "时间": [],
               "处理机构": [],
               "处理机构代码": [],
               "处理动作": [],
               "详细说明": [],
               "操作员": [],
               "操作员代码": [],
               "来源": [],
               "备注": []

           }
            dataOutput["总包号"].append(mailno)
            dataOutput["寄达局"].append('')
            dataOutput["寄达局代码"].append('')
            dataOutput["时间"].append('')
            dataOutput["处理机构"].append('')
            dataOutput["处理机构代码"].append('')
            dataOutput["处理动作"].append('')
            dataOutput["详细说明"].append('')
            dataOutput["操作员"].append('')
            dataOutput["操作员代码"].append('')
            dataOutput["来源"].append('')
            dataOutput["备注"].append('')


    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

        # 模拟一些耗时操作
        #time.sleep(1)
        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput

def getzdmailtrace(parent,mailno):
    global L
    #print ('正在处理:'+mailno)
    try:
        r = getnextOrgName(mailno)
        nextOrgName = r[0]
        nextOrgCode = r[1]

        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/querypush-web/a/qps/qpspacket/queryPacketsByContainNo'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',

            'Connection': 'keep-alive',
            'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Host': '**********',
            'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
        }
        data = {

            'contain_no': mailno

        }
        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
        response = session.post(url, headers=headers, params=data, verify=False)
        r = response.text

        jsonObj = json.loads(r)
        #print(r)
        dataOutput = {

            "总包号": [],
            '寄达局': [],
            '寄达局代码': [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []

        }
        # 提取键
        extracted_keys = extract_keys_by_delimiters(input3_textbox.get())

        # 添加新字段
        for key in extracted_keys:
            if key not in dataOutput:
                dataOutput[key] = []
        # 获取input2_textbox的值
        input2_value = input2_textbox.get()

        # 用#分割每个值
        values_list = input2_value.split('#')
        if '否' in selected2.get():
            for line in jsonObj:
                # 通过循环来获取JSON中的数据，并添加到字典中
                if line['opName'] in values_list:
                    dataOutput["总包号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                    #dataOutput["时间"].append(line.get('opTime', ''))
                    op_time_value = line.get('opTime', '')

                    if op_time_value:
                        # Convert the timestamp to a datetime object
                        datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                        # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                        formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        # Append the formatted datetime to the '时间' key in dataOutput
                        dataOutput["时间"].append(formatted_datetime)
                    else:
                        # Handle the case when 'opTime' is empty
                        dataOutput["时间"].append("")

                    dataOutput["寄达局"].append(nextOrgName)
                    dataOutput["寄达局代码"].append(nextOrgCode)
                    dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('desc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
                    groups = input3_textbox.get().split('#')
                    for group in groups:
                        if '@' in group:
                            start_char, end_char = group.split('@')
                            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                            # print("开始字符"+start_char,"结束字符"+ end_char)
                            if end_char:  # 有结束字符
                                pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                            else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                            matches = re.findall(pattern, line.get('internalDesc', ''))
                            if matches:
                                dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                            else:
                                dataOutput[start_char.rstrip(':')].append('')
        else:
            for line in jsonObj:
                if line['opName'] in values_list and ('51000061' in line["opOrgCode"] or '51040034' in line["opOrgCode"] or '51040033' in line["opOrgCode"]):
                    # 通过循环来获取JSON中的数据，并添加到字典中
                    dataOutput["总包号"].append(mailno)  # 获取JSON中的id，并存入字典中的id内
                    # dataOutput["时间"].append(line.get('opTime', ''))
                    op_time_value = line.get('opTime', '')

                    if op_time_value:
                        # Convert the timestamp to a datetime object
                        datetime_object = datetime.fromtimestamp(int(op_time_value) / 1000)

                        # Format the datetime object as a string in "YYYY-MM-DD HH:MM:SS" format
                        formatted_datetime = datetime_object.strftime('%Y-%m-%d %H:%M:%S')

                        # Append the formatted datetime to the '时间' key in dataOutput
                        dataOutput["时间"].append(formatted_datetime)
                    else:
                        # Handle the case when 'opTime' is empty
                        dataOutput["时间"].append("")
                    dataOutput["寄达局"].append(nextOrgName)
                    dataOutput["寄达局代码"].append(nextOrgCode)
                    dataOutput["处理机构"].append(line.get('opOrgSimplename', ''))
                    dataOutput["处理机构代码"].append(line.get('opOrgCode', ''))  # 对于JSON中的时间戳数据，可以通过该方法转换为具体的时间
                    dataOutput["处理动作"].append(line.get('opName', ''))
                    dataOutput["详细说明"].append(line.get('desc', ''))
                    dataOutput["操作员"].append(line.get('operatorName', ''))
                    dataOutput["操作员代码"].append(line.get('operatorNo', ''))
                    dataOutput["来源"].append(line.get('source', ''))
                    dataOutput["备注"].append(line.get('notes', ''))
                    groups = input3_textbox.get().split('#')
                    for group in groups:
                        if '@' in group:
                            start_char, end_char = group.split('@')
                            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
                            # print("开始字符"+start_char,"结束字符"+ end_char)
                            if end_char:  # 有结束字符
                                pattern = re.escape(start_char) + '(.*?)' + re.escape(end_char)
                            else:  # 没有结束字符，匹配到字符串末尾或者下一个已知的起始字符
                                pattern = re.escape(start_char) + '(.*?)(?:,|$)'
                            matches = re.findall(pattern, line.get('internalDesc', ''))
                            if matches:
                                dataOutput[start_char.rstrip(':')].append(matches[0].rstrip())
                            else:
                                dataOutput[start_char.rstrip(':')].append('')
    except json.JSONDecodeError:
        dataOutput = {

            "总包号": [],
            '寄达局': [],
            '寄达局代码': [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []

        }
        dataOutput["总包号"].append(mailno)
        dataOutput["寄达局"].append(nextOrgName)
        dataOutput["寄达局代码"].append(nextOrgCode)
        dataOutput["时间"].append("")
        dataOutput["处理机构"].append("")
        dataOutput["处理机构代码"].append("")
        dataOutput["处理动作"].append("")
        dataOutput["详细说明"].append("")
        dataOutput["操作员"].append("")
        dataOutput["操作员代码"].append("")
        dataOutput["来源"].append("")
        dataOutput["备注"].append("")
    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock

        # 模拟一些耗时操作
        #time.sleep(1)
        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    #session.close()
    return dataOutput

# 合并字典数据
def merge_dicts(dict1, dict2):
            for key in dict1:
                if key in dict2:
                    dict1[key].extend(dict2[key])
            return dict1

def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data
        merged_data = {
            "总包号": [],
            '寄达局': [],
            '寄达局代码': [],
            "时间": [],
            "处理机构": [],
            "处理机构代码": [],
            "处理动作": [],
            "详细说明": [],
            "操作员": [],
            "操作员代码": [],
            "来源": [],
            "备注": []
        }
        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/querypush-web/a/qps/qpspacket/list'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果


        # 修改封装函数，确保正确添加容器码字段
        def get_mailtrace_with_container(func, mailno, container_code):
            # 调用原始 getmailtrace_bound 函数，仅传递 mailno
            result = func(mailno)
            if container_code:
                # 为每条记录添加容器码信息，确保长度与其他字段一致
                if result:
                    any_key = next(iter(result.keys()), None)
                    if any_key and isinstance(result[any_key], list):
                        result["容器码"] = [container_code] * len(result[any_key])
            return result

        # 如果按容器查询，先用容器号获取对应的总包号
        if selected3.get() == '按容器':
            # 生成一个容器号到总包号的映射字典
            container_to_total_package = {container_no: getzb(container_no) for container_no in datalist}
            # 创建一个用于并发处理的列表，包含 (总包号, 容器号) 元组
            total_package_and_container_list = [(total_package, container_no)
                                                for container_no, total_package in container_to_total_package.items()]
        else:
            # 否则，直接按总包号进行处理，每个元素是 (总包号, None)
            total_package_and_container_list = [(mailno, None) for mailno in datalist]

        if '全程' in selected.get():
            # 创建一个偏函数，其中root参数预先设定
            if selected3.get() == '按容器':
                merged_data["容器码"] = []  # 确保包含容器码字段
            getmailtrace_bound = partial(getallmailtrace, parent)

            #results = pool.map(lambda mailno: getallmailtrace(mailno, parent), datalist)
        elif '最后' in selected.get():
            # 创建一个偏函数，其中root参数预先设定
            getmailtrace_bound = partial(getlastmailtrace, parent)
            if selected3.get() == '按容器':
                merged_data = {
                    "容器码": [],  # 确保包含容器码字段
                    "总包号码": [],
                    "总包条码": [],
                    "原寄局": [],
                    "原寄局代码": [],
                    "总包种类": [],
                    "封发时间": [],
                    '寄达局': [],
                    '寄达局代码': [],
                    "重量": [],
                    "件数": [],
                    "时间": [],
                    "处理机构": [],
                    "处理机构代码": [],
                    "处理动作": [],
                    "详细说明": [],
                    "操作员": [],
                    "操作员代码": [],
                    "来源": [],
                    "备注": []
                }
            else:
            # 创建一个偏函数，其中root参数预先设定

                merged_data = {
                        "总包号码": [],
                        "总包条码": [],
                        "原寄局": [],
                        "原寄局代码": [],
                        "总包种类": [],
                        "封发时间": [],
                        '寄达局':[],
                        '寄达局代码': [],
                        "重量": [],
                        "件数": [],
                        "时间": [],
                        "处理机构": [],
                        "处理机构代码": [],
                        "处理动作": [],
                        "详细说明": [],
                        "操作员": [],
                        "操作员代码": [],
                        "来源": [],
                        "备注": []
                }
            #results = pool.map(lambda mailno: getlastmailtrace(mailno, parent), datalist)
        else:
            if selected3.get() == '按容器':
                merged_data["容器码"] = []  # 确保包含容器码字段
            # 创建一个偏函数，其中root参数预先设定
            # 提取键
            extracted_keys = extract_keys_by_delimiters(input3_textbox.get())
            # 添加新字段
            for key in extracted_keys:
                if key not in merged_data:
                    merged_data[key] = []
            getmailtrace_bound = partial(getzdmailtrace, parent)
            #results = pool.map(lambda mailno: getzdmailtrace(mailno, parent), datalist)
        # results = pool.map(getmailtrace_bound, datalist)
        # merged_data = reduce(merge_dicts, results, merged_data)

        # 3. 使用线程池并发执行，传递 (总包号, 容器码)
        # results = pool.starmap(getmailtrace_bound, total_package_and_container_list)
        #
        # # 4. 修改 merge_dicts 函数，将容器码合并
        # def merge_dicts(dict1, dict2):
        #     for key in dict1:
        #         if key in dict2:
        #             dict1[key].extend(dict2[key])
        #     return dict1
        #
        # # 将结果合并到 merged_data 中，同时确保每个记录包含正确的容器码
        # for result, (_, container_code) in zip(results, total_package_and_container_list):
        #     if container_code:
        #         # 给结果增加容器码字段，每条记录的容器码都设置为当前的 container_code
        #         result["容器码"] = [container_code] * len(result["总包号码"])
        #     merged_data = merge_dicts(merged_data, result)


        # 使用 starmap 并发调用封装函数
        # 使用 starmap 并发调用封装函数，以 (总包号, 容器号) 作为参数
        results = pool.starmap(partial(get_mailtrace_with_container, getmailtrace_bound),
                               [(total_package, container_no) for total_package, container_no in
                                total_package_and_container_list])

        # 合并结果
        for result in results:
            merged_data = merge_dicts(merged_data, result)


        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.now()
        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("总包"+selected.get()+"-" +
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')
def extract_keys_by_delimiters(input_str):
    results = {}
    groups = input_str.split('#')
    for group in groups:
        if '@' in group:
            start_char, _ = group.split('@')# 使用_来丢弃分割后的第二个元素（如果有的话）
            start_char = start_char.replace("：", ":")  # 将中文冒号替换为英文冒号
            key = start_char.rstrip(':')  # 去掉冒号
            results[key] = []  # 初始化为空列表
            #print("新增键："+str(results))
    return results


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected,selected2,selected3, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,\
        back_button,organization_combobox,input2_textbox,input3_textbox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='全程轨迹')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=0, column=0, padx=10, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    radio_button1.grid(row=0, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    radio_button2.grid(row=0, column=2, padx=5, pady=10)

    radio_button3 = ttk.Radiobutton(input_label_container, text="指定轨迹", value="指定轨迹", variable=selected)
    radio_button3.grid(row=0, column=3)

    # 创建单选按钮
    selected2 = tk.StringVar(value='否')
    radio2_label = ttk.Label(input_label_container, text="是否只筛广航轨迹:")
    radio2_label.grid(row=1, column=0, padx=10, pady=10)
    radio2_button1 = ttk.Radiobutton(input_label_container, text="是", value='是', variable=selected2)
    radio2_button1.grid(row=1, column=1, padx=5, pady=10)

    radio2_button2 = ttk.Radiobutton(input_label_container, text="否", value='否', variable=selected2)
    radio2_button2.grid(row=1, column=2, padx=5, pady=10)

    selected3 = tk.StringVar(value='按总包')
    radio3_label = ttk.Label(input_label_container, text="查询方式:")
    radio3_label.grid(row=2, column=0, padx=10, pady=10)
    radio3_button1 = ttk.Radiobutton(input_label_container, text="按总包", value='按总包', variable=selected3)
    radio3_button1.grid(row=2, column=1, padx=5, pady=10)

    radio3_button2 = ttk.Radiobutton(input_label_container, text="按容器", value='按容器', variable=selected3)
    radio3_button2.grid(row=2, column=2, padx=5, pady=10)

    # 添加指定处理动作
    input2_label = ttk.Label(input_label_container, text="筛处理动作(多个处理动作用#分隔):")
    input2_label.grid(row=3, column=0, padx=10, pady=10)
    # input_label.pack()
    input2_textbox = tk.Entry(input_label_container, width=30)
    input2_textbox.grid(row=3, column=1, padx=10, pady=10, columnspan=1)

    input3_label = ttk.Label(input_label_container, text="提取详细明细(开始和结束字符，用@隔开，多组内容用#分隔):")
    input3_label.grid(row=4, column=0, padx=10, pady=10)
    # input_label.pack()
    input3_textbox = tk.Entry(input_label_container, width=30)
    input3_textbox.grid(row=4, column=1, padx=10, pady=10, columnspan=1)
    input3_label2 = ttk.Label(input_label_container, text="例如AAA:@,#BBB:@,#CCC:@,#DDD:@")
    input3_label2.grid(row=4, column=2, padx=10, pady=10)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="总包号:")
    input_label.grid(row=5, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=5, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=6, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=6, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()