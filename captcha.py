import ddddocr
import requests


class CaptchaHandler:
    def getcaptcha(self, session):
        requests.packages.urllib3.disable_warnings()
        url = 'https://10.4.188.1/cas/captcha.jpg'
        headers = {
            'Content-type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
        }

        response = session.get(url, headers=headers, verify=False)

        # 把获取的二进制写成图片
        image_path = 'captcha.jpg'
        with open(image_path, 'wb') as f:
            f.write(response.content)

        # 使用ddddocr识别验证码，设置识别范围为数字
        ocr = ddddocr.DdddOcr()
        ocr.set_ranges("0123456789")  # 设置识别范围为数字
        with open(image_path, 'rb') as f:
            img_bytes = f.read()

        captcha = ocr.classification(img_bytes).strip()

        self.process_input('原始：' + captcha)
        self.process_input('修正：' + captcha)

        # 删除验证码图片
        #os.remove(image_path)

        return captcha

    def process_input(self, input_text):
        # 在这里添加你的处理逻辑
        print(input_text)


# 使用示例
session = requests.Session()
session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
captcha_handler = CaptchaHandler()
captcha = captcha_handler.getcaptcha(session)
print(f"Captcha: {captcha}")