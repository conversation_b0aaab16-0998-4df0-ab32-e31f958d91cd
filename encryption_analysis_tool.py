import asyncio
import json
import os
import re
import time
import base64
from datetime import datetime
from urllib.parse import parse_qs, urlparse, unquote

import ddddocr
from playwright.async_api import async_playwright, TimeoutError, Error

# 基本配置
LOG_FILE = f"request_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
TARGET_PARAM = "MG9Olv7Z"
TARGET_COOKIES = ["enable_3bg4b5fckQas", "3bg4b5fckQasP"]
CAPTURE_DIR = "captured_data"
os.makedirs(CAPTURE_DIR, exist_ok=True)
capture_count = 0  # 添加这行，定义为全局变量

# 登录配置 - 替换为你的账号
USERNAME = "03621935"  # 这里填入你的用户名
PASSWORD = "Gzamc#381801"    # 这里填入你的密码
TEST_MAILNOS = ["1220848805009", "1050177580537"]  # 测试邮件号

def log_message(message, file_path=LOG_FILE):
    """记录消息到日志文件"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    with open(file_path, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(f"[{timestamp}] {message}")

async def network_monitor(context, page):
    """监控所有网络活动，专注于请求参数和Cookie"""
    global capture_count  # 添加这行，声明使用全局变量
    
    async def handle_request(request):
        url = request.url
        method = request.method
        
        # 记录所有请求
        if "queryTraceByTrace_noHidden" in url or TARGET_PARAM in url:
            global capture_count  # 在嵌套函数中也需要声明
            capture_count += 1
            
            # 分析URL参数
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            
            # 记录请求详情
            request_data = {
                "timestamp": datetime.now().isoformat(),
                "method": method,
                "url": url,
                "headers": dict(request.headers),
                "params": {k: v[0] if len(v) == 1 else v for k, v in query_params.items()},
            }
            
            # 记录关键参数
            if TARGET_PARAM in query_params:
                request_data["target_param"] = query_params[TARGET_PARAM][0]
                log_message(f"发现目标参数: {TARGET_PARAM}={request_data['target_param']}")
            
            # 保存请求详情
            capture_file = os.path.join(CAPTURE_DIR, f"request_{capture_count}_{datetime.now().strftime('%H%M%S')}.json")
            with open(capture_file, "w", encoding="utf-8") as f:
                json.dump(request_data, f, indent=2)
                
            # 记录Cookie
            current_cookies = await context.cookies()
            cookies_file = os.path.join(CAPTURE_DIR, f"cookies_{capture_count}_{datetime.now().strftime('%H%M%S')}.json")
            with open(cookies_file, "w", encoding="utf-8") as f:
                json.dump(current_cookies, f, indent=2)
                
            # 记录关键Cookie
            target_cookies = {}
            for cookie in current_cookies:
                if cookie["name"] in TARGET_COOKIES:
                    target_cookies[cookie["name"]] = cookie["value"]
                    log_message(f"目标Cookie: {cookie['name']}={cookie['value'][:30]}...")
                    
            # 添加简单分析
            log_message(f"捕获请求 #{capture_count}: {method} {parsed_url.path}")
    
    # 设置请求监听器
    page.on("request", handle_request)
    
    # 我们不需要响应监听器，因为我们只关心请求参数

async def auto_login(page, ocr):
    """自动登录，使用OCR识别验证码"""
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            # 填写用户名和密码
            await page.fill('#username', USERNAME)
            await page.fill('#password', PASSWORD)
            
            # 处理验证码
            captcha_img = await page.query_selector('div#sec_code > img')
            if not captcha_img:
                log_message("未找到验证码元素")
                return False
                
            # 截取验证码图片
            image_path = os.path.join(CAPTURE_DIR, f'captcha_{attempt}.jpg')
            await captcha_img.screenshot(path=image_path)
            
            # OCR识别验证码
            with open(image_path, "rb") as f:
                img_bytes = f.read()
            captcha = ocr.classification(img_bytes).strip()
            log_message(f"验证码识别结果: {captcha}")
            
            # 填写验证码
            await page.fill('#security', captcha)
            
            # 点击登录按钮
            await page.click('input#login[type="submit"]')
            
            # 检查是否登录成功 - 使用多个选择器并增加超时时间
            try:
                await page.wait_for_selector("li[name='首页'], #mainMenu, .index-menu, #indexPage", 
                                           timeout=10000)
                log_message(f"登录成功! (尝试 {attempt+1}/{max_retries})")
                return True
            except TimeoutError:
                log_message(f"等待首页元素超时 (尝试 {attempt+1}/{max_retries})")
                
                # 检查是否有错误消息
                error_text = await page.evaluate("""
                    () => {
                        const errors = document.querySelectorAll('.error-message, .alert, #error');
                        return errors.length > 0 ? errors[0].textContent : '';
                    }
                """)
                
                if error_text:
                    log_message(f"登录错误: {error_text}")
                
                # 保存登录失败截图
                await page.screenshot(path=os.path.join(CAPTURE_DIR, f"login_fail_{attempt}.png"))
                
                # 刷新页面重试
                await page.reload()
                continue
                
        except Exception as e:
            log_message(f"登录过程出错: {e}")
            await page.screenshot(path=os.path.join(CAPTURE_DIR, f"login_error_{attempt}.png"))
            await page.reload()
    
    log_message(f"登录失败，已达到最大重试次数 ({max_retries})")
    return False

async def monitor_with_cdp(page):
    """使用CDP监控JavaScript执行"""
    # 启用JavaScript分析器
    await page.context.session.send("Profiler.enable")
    # 启用Runtime分析
    await page.context.session.send("Runtime.enable")
    
    # 设置断点在关键函数上（如果知道函数名称）
    await page.context.session.send("Debugger.enable")
    
    # 监听Console消息
    async def on_console_message(event):
        log_message(f"控制台消息: {event['params']['message']['text']}")
    
    # 监听JavaScript异常
    async def on_exception_thrown(event):
        log_message(f"JavaScript异常: {json.dumps(event['params'], indent=2)}")
    
    # 注册事件监听器
    await page.context.session.on("Runtime.consoleAPICalled", on_console_message)
    await page.context.session.on("Runtime.exceptionThrown", on_exception_thrown)
    
    # 注入辅助脚本 - 监控指定函数调用
    await page.evaluate("""
    (function() {
        // 创建一个可视化的监控面板
        let monitorPanel = document.createElement('div');
        monitorPanel.style.position = 'fixed';
        monitorPanel.style.bottom = '0';
        monitorPanel.style.right = '0';
        monitorPanel.style.width = '400px';
        monitorPanel.style.height = '300px';
        monitorPanel.style.background = 'rgba(0,0,0,0.7)';
        monitorPanel.style.color = 'lime';
        monitorPanel.style.padding = '10px';
        monitorPanel.style.fontSize = '12px';
        monitorPanel.style.fontFamily = 'monospace';
        monitorPanel.style.overflow = 'auto';
        monitorPanel.style.zIndex = '9999';
        
        // 添加标题
        let title = document.createElement('div');
        title.textContent = 'JavaScript Monitoring';
        title.style.fontWeight = 'bold';
        title.style.marginBottom = '5px';
        title.style.borderBottom = '1px solid lime';
        monitorPanel.appendChild(title);
        
        // 创建日志区域
        let logArea = document.createElement('div');
        logArea.id = 'js-monitor-log';
        monitorPanel.appendChild(logArea);
        
        // 在文档准备好后添加到DOM
        function addPanel() {
            if (document.body) {
                document.body.appendChild(monitorPanel);
            } else {
                setTimeout(addPanel, 100);
            }
        }
        addPanel();
        
        // 日志函数
        window._monitorLog = function(message) {
            const log = document.getElementById('js-monitor-log');
            if (log) {
                const entry = document.createElement('div');
                entry.textContent = new Date().toISOString().substr(11, 8) + ': ' + message;
                log.appendChild(entry);
                // 保持滚动到底部
                log.scrollTop = log.scrollHeight;
            }
            // 同时发送到控制台
            console.debug('[Monitor] ' + message);
        };
        
        // 存储原始函数
        window._originalFunctions = {};
        
        // 监控函数调用
        window.monitorFunction = function(objPath, funcName) {
            try {
                let obj = window;
                const parts = objPath ? objPath.split('.') : [];
                
                // 遍历对象路径
                for (const part of parts) {
                    if (obj[part] === undefined) {
                        window._monitorLog(`对象路径不存在: ${objPath}.${funcName}`);
                        return false;
                    }
                    obj = obj[part];
                }
                
                // 保存原始函数
                const fullPath = objPath ? `${objPath}.${funcName}` : funcName;
                window._originalFunctions[fullPath] = obj[funcName];
                
                // 替换函数
                obj[funcName] = function() {
                    const args = Array.from(arguments);
                    window._monitorLog(`调用 ${fullPath}(${args.map(a => typeof a).join(', ')})`);
                    
                    // 调用原始函数
                    return window._originalFunctions[fullPath].apply(this, arguments);
                };
                
                window._monitorLog(`成功监控函数: ${fullPath}`);
                return true;
            } catch (e) {
                window._monitorLog(`监控函数失败: ${e.toString()}`);
                return false;
            }
        };
        
        // 全局对象检测
        window.detectGlobalObjects = function() {
            const globals = [];
            for (let key in window) {
                if (key.startsWith('_$') || key.startsWith('Z8') || key.includes('bmF0aXZlRmlVyUHJ')) {
                    globals.push(key);
                    window._monitorLog(`发现可疑全局对象: ${key}`);
                }
            }
            return globals;
        };
        
        // 在DOMContentLoaded后执行检测
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                window.detectGlobalObjects();
                
                // 尝试监控已知的关键函数
                try {
                    if (typeof _$jX === 'function') {
                        window.monitorFunction('', '_$jX');
                    }
                } catch(e) {}
            }, 1000);
        });
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url) {
            this._monitorUrl = url;
            this._monitorMethod = method;
            return originalXHROpen.apply(this, arguments);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            if (this._monitorUrl && (
                this._monitorUrl.includes('queryTraceByTrace_noHidden') ||
                this._monitorUrl.includes('MG9Olv7Z')
            )) {
                window._monitorLog(`XHR ${this._monitorMethod} ${this._monitorUrl}`);
                if (data) {
                    window._monitorLog(`发送数据: ${data.substr ? data.substr(0, 50) : typeof data}`);
                }
                
                // 记录关键Cookie
                const cookies = document.cookie.split(';');
                for (const cookie of cookies) {
                    if (cookie.includes('3bg4b5fckQasP') || cookie.includes('enable_3bg4b5fckQas')) {
                        window._monitorLog(`Cookie: ${cookie.trim()}`);
                    }
                }
            }
            return originalXHRSend.apply(this, arguments);
        };
        
        window._monitorLog('监控脚本已加载');
    })();
    """)
    
    log_message("已启用CDP监控和注入JavaScript监控器")

async def main():
    """主函数: 完全自动化的请求捕获流程"""
    global capture_count
    capture_count = 0  # 重置计数器
    log_message("开始自动化请求捕获过程...")
    
    # 实例化OCR对象
    ocr = ddddocr.DdddOcr()
    
    async with async_playwright() as p:
        # 启动Firefox浏览器 - 无头模式以提高性能
        browser = await p.firefox.launch(headless=False)
        
        # 创建浏览器上下文
        context = await browser.new_context(
            bypass_csp=True,
            ignore_https_errors=True,
            viewport={'width': 1280, 'height': 800}
        )
        
        # 设置User-Agent
        await context.set_extra_http_headers({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        })
        
        # 最小化浏览器指纹修改
        await context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
        """)
        
        # 创建页面
        page = await context.new_page()
        
        # 设置网络监控
        await network_monitor(context, page)
        
        try:
            # 步骤1: 访问登录页面
            log_message("访问登录页面...")
            await page.goto("https://**********/cas/login", timeout=30000)
            
            # 步骤2: 自动登录
            login_success = await auto_login(page, ocr)
            if not login_success:
                log_message("登录失败，终止流程")
                await browser.close()
                return
            
            # 保存登录后所有Cookie
            cookies = await context.cookies()
            with open(os.path.join(CAPTURE_DIR, "all_cookies_after_login.json"), "w") as f:
                json.dump(cookies, f, indent=2)
            
            # 步骤3: 访问查询页面
            log_message("访问邮件查询页面...")
            await page.goto('https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList', timeout=30000)
            
            # 等待页面加载
            await page.wait_for_selector('#traceNo', timeout=10000)
            
            # 步骤4: 自动执行多次查询以捕获更多数据
            for i, mailno in enumerate(TEST_MAILNOS):
                log_message(f"测试查询邮件号 ({i+1}/{len(TEST_MAILNOS)}): {mailno}")
                
                # 清除输入框
                await page.fill('#traceNo', '')
                await asyncio.sleep(0.5)
                
                # 输入邮件号
                await page.fill('#traceNo', mailno)
                await asyncio.sleep(0.5)
                
                # 保存查询前状态
                await page.screenshot(path=os.path.join(CAPTURE_DIR, f"before_query_{i+1}.png"))
                
                # 点击查询按钮
                await page.click('#btnSubmit')
                
                # 等待结果或超时
                try:
                    await page.wait_for_selector('.mailQuery_container', timeout=15000)
                    log_message(f"查询结果加载完成: {mailno}")
                    await page.screenshot(path=os.path.join(CAPTURE_DIR, f"query_result_{i+1}.png"))
                except TimeoutError:
                    log_message(f"查询结果超时: {mailno}")
                    await page.screenshot(path=os.path.join(CAPTURE_DIR, f"query_timeout_{i+1}.png"))
                
                # 暂停一下避免请求过快
                await asyncio.sleep(2)
            
            # 步骤5: 分析捕获的数据
            log_message("开始分析捕获的数据...")
            
            # 查找所有参数文件
            param_files = [f for f in os.listdir(CAPTURE_DIR) if f.startswith("request_") and f.endswith(".json")]
            param_values = []
            
            for file in param_files:
                with open(os.path.join(CAPTURE_DIR, file), "r") as f:
                    data = json.load(f)
                    if "target_param" in data:
                        param_values.append(data["target_param"])
            
            if param_values:
                log_message(f"捕获到 {len(param_values)} 个 {TARGET_PARAM} 参数值")
                
                # 简单分析参数特征
                param_lengths = [len(p) for p in param_values]
                log_message(f"参数长度: min={min(param_lengths)}, max={max(param_lengths)}, avg={sum(param_lengths)/len(param_lengths)}")
                
                # 尝试Base64解码
                for i, param in enumerate(param_values):
                    try:
                        # 尝试解码
                        decoded = base64.b64decode(param + "=" * (4 - len(param) % 4))
                        log_message(f"参数 #{i+1} 可以Base64解码: {decoded[:30]}...")
                    except:
                        try:
                            # 尝试URL解码后再Base64解码
                            decoded = base64.b64decode(unquote(param) + "=" * (4 - len(unquote(param)) % 4))
                            log_message(f"参数 #{i+1} 需要URL解码后再Base64解码: {decoded[:30]}...")
                        except:
                            log_message(f"参数 #{i+1} 无法解码: {param[:30]}...")
            
            log_message("数据捕获和分析完成！")
            
            # 记录捕获结果摘要
            with open(os.path.join(CAPTURE_DIR, "capture_summary.txt"), "w") as f:
                f.write(f"总共捕获请求数: {capture_count}\n")
                f.write(f"参数值数量: {len(param_values)}\n")
                if param_values:
                    f.write(f"参数长度: min={min(param_lengths)}, max={max(param_lengths)}, avg={sum(param_lengths)/len(param_lengths)}\n")
                    f.write("\n参数样本:\n")
                    for i, param in enumerate(param_values[:5]):  # 只展示前5个
                        f.write(f"{i+1}. {param}\n")
            
        except Exception as e:
            log_message(f"执行过程中出错: {e}")
            await page.screenshot(path=os.path.join(CAPTURE_DIR, "error.png"))
        
        finally:
            # 等待几秒以确保所有数据都被保存
            await asyncio.sleep(3)
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main()) 