('E:\\test\\build\\ghhj\\PYZ-00.pyz',
 [('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ast.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\contextlib.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\copy.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\datetime.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dis.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\hmac.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\mimetypes.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\nturl2path.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\optparse.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\py_compile.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\socket.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\ssl.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\.virtualenvs\\test\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\uu.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\zipfile.py',
   'PYMODULE')])
