import io
import json
import random
import re
import socket
import tkinter as tk
import traceback
from datetime import datetime
from multiprocessing import Pool
from tkinter import ttk, scrolledtext
import threading
import time
from tkinter.ttk import Label, Entry
import datetime
import requests
from bs4 import BeautifulSoup

from tool import Tool


class MyApp:
    def __init__(self, root):
        self.root = root
        self.running = False
        self.setup_window()

        self.data = {
            'pageNo': 1,
            'pageSize': 10000,  # 调整为适当的页面大小
            'searchCodes': '',
            'itemStoreStatus': '1',
            'storehouseCode': '02',
            'storageAreaCode': '02-1',
            'shelfNo': '',
            'notifyStatus': '',
            'recvstartTime': '',
            'recvendTime': '',
            'inputstartTime': '',
            'inputendTime': '',
            'firstNoticeDateSel': '',
            'workSect': '',
            'sepaBaggage': '',
            'isForeign': '',
            'isVn': '',
            'anomalyType': '',
            'custReceiptCode': '',
            'noticeType': '1',
            'ifPhoneValid': '',
            'addresseePhone': '',
            'outWarehoseStartTime': '',
            'outWarehoseEndTime': '',
            'outApplyStartTime': '',
            'outApplyEndTime': '',
            'custReceiptInfo': '',
            'itemClientStatus': '',
            'sendSms': '',
            'itemNo': ''
        }


    def setup_window(self):
        global username, password, session, jdptid, L, input_textbox, output_textbox, root, \
            submit_button, button_clear, start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, \
            business_label, business_combobox, business_options, selected, jlyy_textbox, tool, back_button, organization_combobox, access_token, all_posi_nos,\
        button_run,button_stop,posi_combobox,selected
        self.root.title("货架亮灯   Power by LHX")
        self.root.overrideredirect(False)  # 允许用户移动窗口
        # 设置图标
        icon_path = 'chinapost.ico'
        self.root.iconbitmap(icon_path)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        window_width = int(screen_width / 2)
        window_height = int(screen_height / 2)
        window_geometry = f"{window_width}x{window_height}"
        self.root.geometry(window_geometry)

        self.root.token_var = tk.StringVar()
        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

        # 创建子容器
        self.frame = tk.Frame(self.root)
        self.frame.pack(padx=0, pady=0)

        # 创建代理功能勾选框的变量
        # proxy_enabled = tk.BooleanVar()

        # 创建子容器
        self.account_container = ttk.Frame(self.frame)
        self.account_container.grid(row=0, column=0)

        # 创建下拉框
        self.organization_label = Label(self.account_container, text="机构:")
        self.organization_label.grid(row=0, column=0, padx=5)
        self.organization_combobox = ttk.Combobox(self.account_container, values=["报关行"])
        self.organization_combobox.set("报关行")  # 默认选择第一个机构

        self.organization_combobox.grid(row=0, column=1, padx=10, pady=10)

        # 创建账号输入框
        self.account_label = Label(self.account_container, text="新一代账号:")
        self.account_label.grid(row=0, column=2, padx=5)

        self.account_entry = Entry(self.account_container)
        self.account_entry.grid(row=0, column=3, padx=5)

        # 创建密码输入框
        self.password_label = Label(self.account_container, text="密码:")
        self.password_label.grid(row=0, column=4, padx=5)

        self.password_entry = Entry(self.account_container, show="*")
        self.password_entry.grid(row=0, column=5, padx=5)

        # 创建下拉框
        self.posi_label = Label(self.account_container, text="货架号:")
        self.posi_label.grid(row=1, column=1, padx=5)
        all_posi_nos = self.getshelf()
        self.posi_combobox = ttk.Combobox(self.account_container, values=all_posi_nos)
        # organization_combobox.set("报关行")  # 默认选择第一个机构

        self.posi_combobox.grid(row=1, column=2, padx=5)

        self.thread_label = tk.Label(self.account_container, text="存储位置状态:")
        self.thread_label.grid(row=1, column=3, padx=5)

        business_options = {
            "不亮灯": "0",
            "黄灯": "1",
            "绿灯": "2",
            "红灯": "3"
        }
        # 创建Combobox组件
        self.threads_combobox = ttk.Combobox(self.account_container, values=list(business_options.keys()))
        self.threads_combobox.grid(row=1, column=4, padx=5)
        self.threads_combobox.current(0)  # 设置默认选择的值为第一个选项

        # 创建子容器
        self.input_label_container = ttk.Frame(self.frame)
        self.input_label_container.grid(row=1, column=0)

        # 创建单选按钮
        self.selected = tk.StringVar(value='定时')
        self.radio_label = ttk.Label(self.input_label_container, text="功能选择:")
        self.radio_label.grid(row=1, column=0, padx=5, pady=10)
        self.radio_button1 = ttk.Radiobutton(self.input_label_container, text="指定", value="指定", variable=self.selected)
        self.radio_button1.grid(row=1, column=1, padx=5, pady=10)

        self.radio_button2 = ttk.Radiobutton(self.input_label_container, text="随机", value="随机", variable=self.selected)
        self.radio_button2.grid(row=1, column=2, padx=5, pady=10)

        self.radio_button3 = ttk.Radiobutton(self.input_label_container, text="全部", value="全部", variable=self.selected)
        self.radio_button3.grid(row=1, column=3, padx=5, pady=10)

        self.radio_button4 = ttk.Radiobutton(self.input_label_container, text="定时", value="定时",variable=self.selected)
        self.radio_button4.grid(row=1, column=4, padx=5, pady=10)
        # # 添加多行输入框
        # input_label = ttk.Label(input_label_container, text="邮件号:")
        # input_label.grid(row=2, column=1, padx=10, pady=10)
        # # input_label.pack()
        # input_textbox = tk.Text(input_label_container, height=5, width=30)
        # input_textbox.grid(row=2, column=2, padx=10, pady=10, columnspan=1)
        # input_textbox.pack()

        # 创建登录按钮
        # self.button_clear = ttk.Button(self.input_label_container, text="登录", command=self.login)
        # self.button_clear.grid(row=3, column=1, padx=10, pady=10)

        # 添加运行按钮
        self.button_run = ttk.Button(self.input_label_container, text="运行", command=self.start_running)
        self.button_run.grid(row=3, column=2, padx=10, pady=10)

        self.button_stop = ttk.Button(self.input_label_container, text="停止", command=self.stop_running, state="disabled")
        self.button_stop.grid(row=3, column=3, padx=10, pady=10)


        # 创建子容器
        self.output_container = ttk.Frame(self.frame)
        self.output_container.grid(row=5, column=0, columnspan=5)

        # 添加滚动文本框
        self.output_label = ttk.Label(self.output_container, text="运行情况:")
        self.output_label.grid(row=0, column=0, padx=10, pady=10)

        self.output_textbox = scrolledtext.ScrolledText(self.output_container, height=10, width=50)
        self.output_textbox.configure(state="disabled")  # 设置文本框为只读状态
        self.output_textbox.grid(row=0, column=1, padx=10, pady=10)

        self.root.protocol("WM_DELETE_WINDOW", root.quit)
        self.label = ttk.Label(root, text="状态: 未运行")
        self.label.pack(pady=10)

        # 创建工具类实例
        tool = Tool(self.output_textbox, self.account_entry, self.password_entry, self.root, self.organization_combobox)
        # 读取上次保存的账号和密码
        tool.read_data()
        # self.button_run = ttk.Button(root, text="运行", command=self.start_running)
        # self.button_run.pack(pady=5)
        #
        # self.button_stop = ttk.Button(root, text="停止", command=self.stop_running, state="disabled")
        # self.button_stop.pack(pady=5)


    def getshelf(self):
        url = 'http://************/shelfs/?skip=0&limit=50'
        headers = {
            'accept': 'application/json'
        }

        response = session.get(url, headers=headers)
        # 将 JSON 数据解析为 Python 对象
        data = json.loads(response.text)

        # 获取所有 "posi_no" 的值
        all_posi_nos = [posi["posi_no"] for item in data for posi in item["store_position"]]

        print(response.status_code)
        print(response.json())
        return all_posi_nos

    def login(self):
        url = 'http://************/auth/jwt/login'
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
        }

        data = {
            'username': '<EMAIL>',
            'password': 'admin123',
            'scope': '',
            'client_id': '',
            'client_secret': '',
        }
        try:
            response = session.post(url, headers=headers, data=data)

            # 处理响应
            if response.status_code == 200:
                print(response.json())
                self.process_input('亮灯控制系统登录成功')

                # return f"Request for {posi_no} successful."

            # 将 JSON 数据解析为 Python 对象
            data = json.loads(response.text)
            # print(response.status_code)
            # print(response.json())
            # print(data['access_token'])
            root.token_var.set(data['access_token'])
        except requests.exceptions.RequestException as e:
            # 记录错误信息
            print(f"Error sending request: {e}")
            self.process_input(f"Error sending request: {e}")
            self.login()
            return None
        except requests.exceptions.JSONDecodeError as e:
            # 记录 JSON 解析错误
            print(f"Error decoding JSON: {e}")
            self.process_input(f"Error decoding JSON: {e}")
            self.login()
            return None
    def process_input(self,text):
        # 在输出文本框中显示结果

        self.output_textbox.configure(state="normal")  # 设置文本框为可编辑状态
        self.output_textbox.insert(tk.END, text + '\n')  # 在文本框末尾插入新内容
        self.output_textbox.see(tk.END)  # 滚动到文本框末尾
        self.output_textbox.configure(state="disabled")  # 设置文本框为只读状态
        self.root.update()  # 更新界面，确保文本框能及时显示新内容


    def start_running(self):
        self.running = True
        self.label.config(text="运行状态: 正在运行")
        self.button_run.config(state="disabled")
        self.button_stop.config(state="normal")

        # 启动一个新的线程执行查询功能
        self.thread = threading.Thread(target=self.run_query)
        self.thread.start()

    def stop_running(self):
        self.running = False
        self.label.config(text="运行状态: 未运行")
        self.button_run.config(state="normal")
        self.button_stop.config(state="disabled")

    # 定义请求函数
    def send_request(self,posi_no, state):
        url = f'http://************/storepositions/iowrite/{posi_no}/{state}'
        headers = {
            'accept': 'application/json',
            'Authorization': 'Bearer ' + root.token_var.get()
        }

        # if state == '0':
        #     state = '不亮灯'
        # elif state == '1':
        #     state = '黄灯'
        # elif state == '2':
        #     state = '绿灯'
        # elif state == '3':
        #     state = '红灯'
        try:
            response = session.post(url, headers=headers)
            response.raise_for_status()  # Raise an exception for HTTP errors
            # 处理响应
            if response.status_code == 200:
                print(response.json())
                self.process_input(posi_no + '储位状态设置为' + state)
                # return f"Request for {posi_no} successful."
            elif response.headers.get('Content-Type') == 'application/json':
                print(response.json())
                self.process_input(str(response.json()))
            else:
                print("Response is not JSON:", response.text)
                self.process_input("Response is not JSON:"+ str(response.text))
                self.login()
                self.send_request(posi_no, state)
        except requests.exceptions.HTTPError as err:
            if err.response.status_code == 401:  # 如果是 401 错误，重新获取令牌并重试
                self.process_input("HTTP error:" + str(err))
                self.login()
                self.send_request(posi_no, state)
            else:
                print("HTTP error:", err)
                self.process_input("HTTP error:"+ str(err))
                self.login()
                self.send_request(posi_no, state)
        except requests.exceptions.RequestException as e:
            print("Error:", e)
            self.process_input("Error:"+ str(e))
            self.login()
            self.send_request(posi_no, state)
        # # 处理响应
        # if response.status_code == 200:
        #     print(response.json())
        #     self.process_input(posi_no + '设置为' + state)
        #     # return f"Request for {posi_no} successful."
        # else:
        #     print(response.json())
        #     self.process_input(str(response.json()))
        #     self.login()
            # return f"Request for {posi_no} failed with status code {response.status_code}."

    def appoint(self):
        # global access_token

        posi_no = organization_combobox.get()
        # 获取选择的值
        state = business_options[threads_combobox.get()]
        if posi_no:
            self.send_request(posi_no, state)
        else:
            self.process_input('请选择货架!!!')

    def randoms(self):
        # global access_token

        # 进行十次随机选择
        for _ in range(10):
            # 随机选择 organization_combobox 的值
            selected_organization = random.choice(all_posi_nos)

            # 随机选择 threads_combobox 的值，并获取对应的状态
            selected_business_option = random.choice(list(business_options.keys()))
            selected_state = business_options[selected_business_option]
            self.send_request(selected_organization, selected_state)

    def all(self):
        # global access_token

        # 获取选择的值
        state = business_options[threads_combobox.get()]
        # 遍历 all_posi_nos 列表，对每个 posi_no 发送 HTTP 请求
        for posi_no in all_posi_nos:
            self.send_request(posi_no, state)

    # def run_query(self):
    #     while self.running:
    #         # 这里执行你的查询功能
    #         print("正在执行查询...")
    #         time.sleep(1)  # 模拟查询操作
    def getyqmail(self):
        global L
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Length': '336',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'DNT': '1',
            'Host': '**********',
            'Origin': 'https://**********',
            'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
        }
        with session.post(url, headers=headers, data=self.data, verify=False) as response:
            r = response.text
            html = BeautifulSoup(r, 'lxml')

            match = re.search(r'共\s*(\d+)页', str(html))
            if match:
                page_number = int(match.group(1))
                print("提取的数字为:", page_number)
            else:
                print(html)
                print("未找到匹配的数字")
                return


            for i in range(1, page_number + 1):
                print('第' + str(i) + '页')
                self.data['pageNo'] = i
                with session.post(url,  headers=headers, data=self.data, verify=False) as response:
                    r = response.text
                    html = BeautifulSoup(r, 'lxml')
                    input_element = html.find('tbody')

                    if input_element:
                        trs = input_element.find_all('tr')
                        for tr in trs:
                            tds = tr.find_all('td')
                            new_posi = tds[12].get_text()[4:10]
                            if  new_posi in all_posi_nos:
                                print(tds[2].get_text(), tds[12].get_text(), new_posi)
                                tool.process_input(tds[2].get_text() + ',' + tds[12].get_text())
                                self.send_request(new_posi, '1')


    def getfxmail(self):
        global L
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Length': '336',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'DNT': '1',
            'Host': '**********',
            'Origin': 'https://**********',
            'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
        }
        data = {
            'pageNo': 1,
            'pageSize': 1000,
            'searchCodes': '',
            'itemStoreStatus': '1',
            'storehouseCode': '02',
            'storageAreaCode': '02-1',
            'shelfNo': '',
            'notifyStatus': '',
            'recvstartTime': '',
            'recvendTime': '',
            'inputstartTime': '',
            'inputendTime': '',
            'firstNoticeDateSel': '',
            'workSect': '',
            'sepaBaggage': '',
            'isForeign': '',
            'isVn': '',
            'anomalyType': '',
            'custReceiptCode': '26',
            'noticeType': '',
            'ifPhoneValid': '',
            'addresseePhone': '',
            'outWarehoseStartTime': '',
            'outWarehoseEndTime': '',
            'outApplyStartTime': '',
            'outApplyEndTime': '',
            'custReceiptInfo': '',
            'itemClientStatus': '',
            'sendSms': '',
            'itemNo': ''
        }

        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

        response = session.post(url, headers=headers, data=data, verify=False)
        r = response.text
        # print(r)
        # html=BeautifulSoup(r,'html.parser')
        html = BeautifulSoup(r, 'lxml')
        # print(r)
        input_element = html.find('tbody')
        posi = []
        if input_element:
            trs = input_element.find_all('tr')
            for tr in trs:
                tds = tr.find_all('td')

                # 获取要添加的元素
                new_posi = tds[12].get_text()[4:10]

                # 检查是否已存在于列表中
                if new_posi not in posi and new_posi in all_posi_nos:
                    print(tds[2].get_text(), tds[12].get_text(), new_posi)
                    tool.process_input(tds[2].get_text()+','+ tds[12].get_text())
                    posi.append(new_posi)
                    # self.send_request(new_posi, '1')
        for r in posi:
            self.send_request(r, '2')
        # 循环结束后释放session资源
        session.close()

    def gettymail(self):
        global L
        requests.packages.urllib3.disable_warnings()
        url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/list'
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Length': '336',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'DNT': '1',
            'Host': '**********',
            'Origin': 'https://**********',
            'Referer': 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.5359.125 Safari/537.36',
            'Cookie': 'jdpt.session.id=' + jdptid + ';CORAL_GRAYLEVEL=0'
        }
        data = {
            'pageNo': 1,
            'pageSize': 1000,
            'searchCodes': '',
            'itemStoreStatus': '1',
            'storehouseCode': '02',
            'storageAreaCode': '02-1',
            'shelfNo': '',
            'notifyStatus': '',
            'recvstartTime': '',
            'recvendTime': '',
            'inputstartTime': '',
            'inputendTime': '',
            'firstNoticeDateSel': '',
            'workSect': '',
            'sepaBaggage': '',
            'isForeign': '',
            'isVn': '',
            'anomalyType': '',
            'custReceiptCode': '21',
            'noticeType': '',
            'ifPhoneValid': '',
            'addresseePhone': '',
            'outWarehoseStartTime': '',
            'outWarehoseEndTime': '',
            'outApplyStartTime': '',
            'outApplyEndTime': '',
            'custReceiptInfo': '',
            'itemClientStatus': '',
            'sendSms': '',
            'itemNo': ''
        }

        # response = requests.post(url, headers=headers, verify=False,proxies=proxies)

        response = session.post(url, headers=headers, data=data, verify=False)
        r = response.text
        # print(r)
        # html=BeautifulSoup(r,'html.parser')
        html = BeautifulSoup(r, 'lxml')
        # print(r)
        input_element = html.find('tbody')
        posi = []
        if input_element:
            trs = input_element.find_all('tr')
            for tr in trs:
                tds = tr.find_all('td')

                # 获取要添加的元素
                new_posi = tds[12].get_text()[4:10]

                # 检查是否已存在于列表中
                if new_posi not in posi and new_posi in all_posi_nos:
                    print(tds[2].get_text(), tds[12].get_text(), new_posi)
                    tool.process_input(tds[2].get_text()+','+ tds[12].get_text())
                    posi.append(new_posi)
                    # self.send_request(new_posi, '1')
        for r in posi:
            self.send_request(r, '3')
        # 循环结束后释放session资源
        session.close()

    def loginxyd(self):
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        i = 2
        url = 'https://**********/intcubr-web/a/intcubr/storehousemanage/tolist'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]

            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        return jdptid

    def run_query(self):

        self.login()
        # 并发执行并获取结果
        if '指定' in self.selected.get():
            self.appoint()
        elif '随机' in self.selected.get():
            self.randoms()
        elif '全部' in self.selected.get():
            self.all()
        else:

            try:
                global username, password, session, jdptid, L, merged_data
                # 构造Session
                session = requests.Session()
                # 获取本机主机名
                hostname = socket.gethostname()

                # 获取本机 IP 地址
                ip_address = socket.gethostbyname(hostname)

                print("本机主机名:", hostname)
                print("本机 IP 地址:", ip_address)
                if '************' == ip_address:
                    session.proxies = {'http': "http://************:9999",
                                       'https': "http://************:9999"}
                    tool.process_input("代理功能已启用")
                L = 1

                # 保存账号和密码
                tool.save_data()
                username = self.account_entry.get()
                password = self.password_entry.get()

                password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')



                jdptid=self.loginxyd()
                while self.running:
                    start = time.perf_counter()
                    # 逾期
                    self.getyqmail()

                    #退运指令
                    self.gettymail()

                    #放行指令
                    self.getfxmail()

                    end = time.perf_counter()
                    runTime = end - start
                    # 计算时分秒
                    hour = runTime // 3600
                    minute = (runTime - 3600 * hour) // 60
                    second = runTime - 3600 * hour - 60 * minute
                    # 输出
                    # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    tool.process_input('当前时间：' + now)
                    tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                    time.sleep(10)
            except Exception as e:

                buffer = io.StringIO()
                traceback.print_exc(file=buffer)
                error_message = buffer.getvalue()
                tool.process_input(error_message)


root = tk.Tk()

app = MyApp(root)

root.mainloop()
