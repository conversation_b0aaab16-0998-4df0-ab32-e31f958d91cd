import pandas as pd


def custom_excel_summary(input_file, group_column, summary_columns, output_file):
    try:
        # 使用 pd.ExcelFile 读取所有Sheet
        xls = pd.ExcelFile(input_file)

        # 初始化一个空的 DataFrame 用于保存汇总数据
        total_df = pd.DataFrame()

        # 遍历每个 Sheet
        for sheet_name in xls.sheet_names:
            # 忽略 "Total" Sheet
            if sheet_name.lower() == 'total':
                continue

            # 读取当前 Sheet 的数据
            df = pd.read_excel(input_file, sheet_name=sheet_name, engine='openpyxl')

            # 根据指定列进行汇总
            grouped_df = df.groupby(group_column).agg({col: 'sum' for col in summary_columns}).reset_index()

            # 合并到总的 DataFrame 中
            total_df = pd.concat([total_df, grouped_df])

        # 根据指定列再次进行汇总，将相同值的行合并
        final_total_df = total_df.groupby(group_column).agg({col: 'sum' for col in summary_columns}).reset_index()

        # 将汇总数据写入一个新的 Excel 文件
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            final_total_df.to_excel(writer, sheet_name='Total', index=False)
    except KeyError as e:
            print(f"发生错误：{e} 列名未在 {sheet_name} sheet 中找到。请检查输入的列名是否正确。")

# 用户自定义输入
input_file_path = input("请输入需要统计的Excel文件名：")
group_column_name = input("请输入分组的列名：")
summary_column_names = input("请输入需要统计的列名，用逗号(,)分隔：").split(',')
#output_file_path = input("请输入输出的Excel文件路径：")
output_file_path='汇总'+input_file_path
# 执行汇总
custom_excel_summary(input_file_path, group_column_name, summary_column_names, output_file_path)

print("汇总完成，结果保存在", output_file_path)
