import asyncio

import os
import socket
import sys
import tkinter as tk

from functools import reduce

from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *

import requests,json, time, re

from datetime import datetime
from playwright.async_api import async_playwright

import pandas as pd
import threading
from os import path
from tool import Tool

executable_path = "firefox/firefox.exe"    # 指定可执行文件路径，用相对路径
#executable_path ="chrome/chrome.exe" 
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1
# 需要匹配的 URL 固定部分（正则或字符串）
TARGET_URL_PARTS = [
    'queryTraceByTrace_noHidden',  # 只匹配包含这部分的 URL
]
# 请求信息类
class RequestInfo:
    def __init__(self, url, response_data=None):
        self.url = url
        self.response_data = response_data

# 用于批量处理邮件号查询并导出数据
async def batch_query_and_export(title,parent):
    global L
    # 程序已经在运行中，禁用按钮
    submit_button.configure(state="disabled")
    back_button.configure(state="disabled")
    start = time.perf_counter()
    # 获取本机主机名
    hostname = socket.gethostname()
    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}

        tool.process_input("代理功能已启用")
    L = 0

    # 保存账号和密码
    tool.save_data()
    username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
    password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
    
    browser = None  # 初始化browser变量
    
    try:
        # 使用 Playwright 启动浏览器
        async with async_playwright() as p:
            # 修改为使用Firefox
            browser = await p.firefox.launch(headless=True,
                                           executable_path=executable_path)
            # Firefox不需要这些Chrome专用参数
            # 删除 args=["--no-sandbox", "--disable-setuid-sandbox", 
            #           "--disable-dev-shm-usage", 
            #           "--disable-extensions",
            #           "--disable-gpu"]

            # 创建浏览器上下文
            context = await browser.new_context(bypass_csp=True, 
                                               ignore_https_errors=True)
            page = await context.new_page()
            # 获取网络请求
            # page.on("request", lambda request: print( ">> ", request.method, request.url))
            # # 获取网络响应
            # page.on("response", lambda response: print( "<< ", response.status, response.text))

            # 设置 User-Agent模拟正常浏览器
            await context.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            })

            # 禁用Playwright的自动化标识
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)

            # 在context创建后添加拦截规则
            await context.route('**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2,ttf}', lambda route: route.abort())

            # 获取文本框中的文本
            text = input_textbox.get("1.0", "end-1c")
            # 将文本按行分割并去除空行
            lines = text.splitlines()
            lines = [line for line in lines if line.strip()]
            mailnos = lines

            # 登录
            userName,orgName,orgcode=await tool.newlogin(page, username, password)
            tool.postlog(username, userName, title, ip_address)
            await page.close()  # 登录完成后关闭页面

            all_logistics_data = []
            # for i, mailno in enumerate(mailnos):
            #     tool.process_input(f"正在查询第 {i+1} 个邮件号: {mailno}")
            #     logistics_data = await query_mailno(page, mailno)
            #     if logistics_data:
            #         all_logistics_data.extend(logistics_data)
            # 限制并发的最大任务数
            semaphore = asyncio.Semaphore(int(threads_combobox.get()))  # 设置并发上限为 5（可调整）

            # 登录后初始化页面池
            pool = PagePool(context, size=min(10, int(threads_combobox.get())))
            await pool.initialize()

            # 创建响应管理器
            response_manager = ResponseManager()
            
            # 预先将所有邮件号添加到顺序列表中
            for mailno in mailnos:
                response_manager.mailno_order.append(mailno)

            async def query_with_semaphore(mailno):
                async with semaphore:
                    page = None
                    try:
                        page = await pool.get_page()
                        # 检查页面状态
                        if page.is_closed():
                            # 如果页面已关闭，创建新页面
                            page = await context.new_page()
                            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpswaybilltraceinternal/traceList')
                            
                        await query_mailno(page, mailno, parent, response_manager)
                        return mailno
                    except Exception as e:
                        print(f"查询过程出错: {e}")
                        return mailno
                    finally:
                        if page and not page.is_closed():
                            await pool.release_page(page)
                        elif page:
                            print("页面已关闭，无法返回到池中")

            # 创建任务列表
            tasks = [query_with_semaphore(mailno) for mailno in mailnos]

            # 并发执行任务
            await asyncio.gather(*tasks)

            # 从响应管理器中获取收集的所有数据
            merged_data = response_manager.get_all_data()
            #tool.process_input(f"共查询到{len(merged_data['邮件号'])}条数据")
            tool.process_input("\n正在导出数据...")
            # 导出到Excel
            if merged_data:

                df = pd.DataFrame(merged_data)
                # # 生成Excel文件的保存路径，当前目录 + 当前时间
                currentTime = datetime.now().strftime('%Y%m%d_%H%M%S')
                # export_path = os.path.join(os.getcwd(), f'邮件全轨迹_{current_time}.xlsx')
                row = 1048570
                length = len(df)
                number = length // row
                for i in range(number + 1):
                    export_path=os.path.join(os.getcwd(), f'邮件收寄计费信息-{currentTime}{i}-bylhx.xlsx')
                    df[i * row:(i + 1) * row].to_excel(export_path,index=False)
                # 保存文件
                #df.to_excel(export_path, index=False)
                    tool.process_input(f"\n数据已成功导出！\n文件路径：{export_path}")
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                del merged_data
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
                #messagebox.showinfo("成功", f"数据已成功导出！文件路径：{export_path}")
            else:
                #messagebox.showwarning("警告", "没有获取到有效数据！")
                tool.process_input("没有获取到有效数据")
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
    except Exception as e:
        tool.process_input(f"查询失败，错误信息：{e}")
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    finally:
        # 确保浏览器在任何情况下都被关闭
        if browser is not None:
            try:
                await browser.close()
            except Exception:
                pass

# 封装页面加载、输入和点击的任务逻辑
async def load_and_query_page(page, mailno, parent, max_retries=5, retry_delay=0.5):
    for attempt in range(max_retries):
        try:
            # 检查页面是否已关闭
            if page.is_closed():
                print(f"页面已关闭，无法继续查询邮件号: {mailno}")
                return []

            # 使用更短的超时时间以更快检测连接问题
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
                           wait_until='load', timeout=15000)  # 增加超时时间

            await page.wait_for_selector('#traceNo', timeout=10000)
            await page.fill('#traceNo', mailno)
            await page.click('#btnSubmit')

            try:
                await page.wait_for_selector('.mailQuery_container', timeout=10000)
                return []
            except Exception:
                return []

        except Exception as e:
            print(f"第 {attempt + 1} 次加载失败: {str(e)}")
            # 特殊处理连接关闭的情况
            if "Connection closed" in str(e) or "Target closed" in str(e):
                print(f"连接已关闭，尝试重新创建页面并登录...")
                return []  # 连接关闭时直接返回，让调用者处理

            if attempt == max_retries - 1:
                parent.after(0, tool.process_input(f"\n页面加载失败，邮件号: {mailno}"))
                return []

            print(f"等待 {retry_delay} 秒后重试...")
            await asyncio.sleep(retry_delay)

# 查询邮件号全程轨迹函数
async def query_mailno(page, mailno, parent, response_manager, max_retries=5, retry_delay=0.5):
    global L

    dataOutput = {
        "邮件号": [],
        "时间": [],
        "收寄省": [],
        "收寄地市": [],
        "收寄局": [],
        "收寄局代码": [],
        "寄达省": [],
        "寄达地市": [],
        "重量": [],
        "标准资费": [],
        "收件人": [],
        "产品": [],
        "产品代码": [],
        "基础产品": [],
        "基础产品代码": []
    }

    # 使用单次监听模式处理响应
    processed = False
    
    async def handle_response(response):
        nonlocal processed
        # 确保只处理一次
        if processed:
            return
            
        url = response.url
        if any(target_part in url for target_part in TARGET_URL_PARTS) and not response_manager.is_processed(url):
            processed = True
            response_manager.mark_processed(url)  # 标记为已处理
            
            print(f"首次处理响应: {url}")
            try:
                # 获取响应文本
                r = await response.text()
                
                try:
                    jsonObj = json.loads(r)
                    
                    jds = ""
                    mdd = ""
                    for line in jsonObj:
                        if '收寄计费信息' in line["opName"]:
                            dataOutput["邮件号"].append(line["traceNo"])  # 获取JSON中的id，并存入字典中的id内
                            dataOutput["时间"].append(line["opTime"])
                            zl = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                            if zl:
                                zl = zl[0]
                            else:
                                zl = ''
                            dataOutput["重量"].append(zl)
                            bzzf = re.findall(r'标准资费:(.*),', str(line["internalDesc"]))
                            if bzzf:
                                bzzf = bzzf[0]
                            else:
                                bzzf = ''
                            dataOutput["标准资费"].append(bzzf)
                            sjr = re.findall(r'收件人:(.*)', str(line["internalDesc"]))
                            if sjr:
                                sjr = sjr[0]
                            else:
                                sjr = ''
                            if "receivePlace" in line:
                                jdss = re.findall(r'(?:上海市|北京市|天津市|重庆市|[\u4e00-\u9fa5]+(?:省|自治区))',
                                                  str(line["receivePlace"]))
                                # mdd=re.findall(r'([\u4e00-\u9fa5]+市)', str(line["receivePlace"]))
                                mdds = re.findall(r'(?:上海市|北京市|天津市|重庆市)', str(line["receivePlace"]))
                                if jdss:
                                    jds = jdss[0]

                                if mdds:
                                    mdd = mdds[0]
                                else:
                                    mdd = line["receivePlace"].replace(jds, '')
                            dataOutput["收件人"].append(sjr)
                            dataOutput["收寄省"].append(line.get('opOrgProvName', ''))
                            dataOutput["收寄地市"].append(line.get('opOrgCity', ''))
                            dataOutput["收寄局"].append(line.get('opOrgSimpleName', ''))
                            dataOutput["收寄局代码"].append(line.get('opOrgCode', ''))
                            dataOutput["寄达省"].append(jds)
                            dataOutput["寄达地市"].append(mdd)
                            dataOutput["基础产品"].append(line.get('baseProductName', ''))
                            dataOutput["基础产品代码"].append(line.get('baseProductNo', ''))
                            dataOutput["产品"].append(line.get('bizProductName', ''))
                            dataOutput["产品代码"].append(line.get('bizProductNo', ''))
                            break
                except json.JSONDecodeError:
                    dataOutput["邮件号"].append(mailno)
                    dataOutput["时间"].append('')
                    dataOutput["重量"].append('')
                    dataOutput["标准资费"].append('')
                    dataOutput["收件人"].append('')
                    dataOutput["收寄省"].append('')
                    dataOutput["收寄地市"].append('')
                    dataOutput["收寄局"].append('')
                    dataOutput["收寄局代码"].append('')
                    dataOutput["寄达省"].append('')
                    dataOutput["寄达地市"].append('')
                    dataOutput["基础产品"].append('')
                    dataOutput["基础产品代码"].append('')
                    dataOutput["产品"].append('')
                    dataOutput["产品代码"].append('')

                # 立即移除监听器，避免重复处理
                nonlocal listener
                if listener:
                    try: 
                        listener.remove()
                        listener = None
                    except: pass
                
                # 将结果存储到响应管理器
                response_manager.add_data(mailno, dataOutput)
            except Exception as e:
                print(f"处理响应出错: {e}")

    # 安全地注册监听器
    listener = None
    try:
        listener = page.on("response", handle_response)
    except Exception as e:
        print(f"添加事件监听器失败: {e}")

    try:
        # 执行页面加载和查询
        await load_and_query_page(page, mailno, parent, max_retries, retry_delay)
        L += 1
        parent.after(0, tool.update_L(str(L)))

    finally:
        # 安全地移除监听器
        if listener is not None:
            try:
                listener.remove()
            except Exception as e:
                print(f"移除事件监听器失败: {e}")

    return dataOutput

def handle_input(title,parent):
    output_textbox.configure(state="normal")
    # 清空文本框内容
    output_textbox.delete("1.0", tk.END)  # 清空内容

    def run_async_task():
        # 创建事件循环
        asyncio.run(batch_query_and_export(title, parent))

    # 启动线程运行异步任务
    threading.Thread(target=run_async_task, daemon=True).start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20", "30", "40"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项


    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # # 创建单选按钮
    # selected = tk.StringVar(value='全程轨迹')
    # radio_label = ttk.Label(input_label_container, text="功能选择:")
    # radio_label.grid(row=0, column=0, padx=10, pady=10)
    # radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    # radio_button1.grid(row=0, column=1, padx=5, pady=10)
    #
    # radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    # radio_button2.grid(row=0, column=2, padx=5, pady=10)



    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=1, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=1, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()

# 在查询前预热页面
async def preload_pages(context, count):
    """预热多个页面以加快后续查询"""
    pages = []
    for _ in range(count):
        page = await context.new_page()
        await page.goto('https://10.4.188.1/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
                       wait_until='domcontentloaded')
        pages.append(page)
    return pages

# 创建页面池
class PagePool:
    def __init__(self, context, size=10):
        self.context = context
        self.size = size
        self.available_pages = []
        self.in_use = set()

    async def initialize(self):
        for _ in range(self.size):
            page = await self.context.new_page()
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
                           wait_until='domcontentloaded')
            self.available_pages.append(page)

    async def get_page(self):
        if not self.available_pages:
            page = await self.context.new_page()
            await page.goto('https://10.4.188.1/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
                           wait_until='domcontentloaded')
        else:
            page = self.available_pages.pop()
        self.in_use.add(page)
        return page

    async def release_page(self, page):
        if page in self.in_use:
            self.in_use.remove(page)
            self.available_pages.append(page)

class ResponseManager:
    def __init__(self):
        self.processed_urls = set()
        self.data_by_mailno = {}  # 使用邮件号作为key存储数据
        self.mailno_order = []    # 记录邮件号输入顺序
    
    def is_processed(self, url):
        # 使用URL的哈希值作为检测依据，避免URL编码差异
        import hashlib
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        return url_hash in self.processed_urls
    
    def mark_processed(self, url):
        # 保存URL的哈希值
        import hashlib
        url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()
        self.processed_urls.add(url_hash)

    def add_data(self, mailno, data):
        # 不再在这里添加到mailno_order，因为已经在初始化时添加了所有邮件号
        self.data_by_mailno[mailno] = data

    def get_all_data(self):
        # 将所有数据按输入顺序合并为一个列表
        result = {
            "邮件号": [],
            "时间": [],
            "收寄省": [],
            "收寄地市": [],
            "收寄局": [],
            "收寄局代码": [],
            "寄达省": [],
            "寄达地市": [],
            "重量": [],
            "标准资费": [],
            "收件人": [],
            "产品": [],
            "产品代码": [],
            "基础产品": [],
            "基础产品代码": []
        }
        
        # 按照记录的顺序处理数据
        for mailno in self.mailno_order:
            if mailno in self.data_by_mailno:  # 检查邮件号对应的数据是否存在
                data = self.data_by_mailno[mailno]
                for key in result:
                    if key in data:
                        result[key].extend(data[key])
        
        return result