import datetime
import io
import os
import socket
import sys
import threading
import tkinter as tk
import traceback

from datetime import timedelta
from functools import partial
from multiprocessing.dummy import Pool
from tkinter import *
from tkinter import scrolledtext
from tkinter import ttk

from os import path


import pandas
import re
import requests
import time

from bs4 import BeautifulSoup
from tkcalendar import DateEntry
from concurrent.futures import ThreadPoolExecutor, as_completed
from tool import Tool

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称

dataOutput = {
    "邮件号": [],
    "业务种类": [],
    "补录时间": [],
    "补录环节": [],
    "补录人员": [],
    "批译地址": []

}


def getzb(parent,mailno=None):
    #print(jdptid)
    # start_date = str(start_date_entry.get_date())+' 00:00:00'
    # end_date = str(end_date_entry.get_date())+' 23:59:59'
    start_date = str(
        start_date_entry.get_date()) + ' ' + starthour_combobox.get() + ':' + startminute_combobox.get() + ':' + startsecond_combobox.get()
    end_date = str(
        end_date_entry.get_date()) + ' ' + endhour_combobox.get() + ':' + endminute_combobox.get() + ':' + endsecond_combobox.get()
    # 获取选择的值
    selected_interFlag = interFlag_options[interFlag_combobox.get()]
    selected_routeLevel = routeLevel_options[routeLevel_combobox.get()]
    selected_statusJ = statusJ_options[statusJ_combobox.get()]
    blry = input_textbox.get()
    if mailno is None:

        tool.process_input('开始日期:' + start_date)
        tool.process_input('结束日期:' + end_date)

        tool.process_input('补录环节:' + interFlag_combobox.get())
        tool.process_input('业务种类:' + routeLevel_combobox.get())
        tool.process_input('批译状态:' + statusJ_combobox.get())
        tool.process_input('补录人员:' + blry)
    #print(selected_interFlag,selected_routeLevel,selected_statusJ)
    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/intprep-web/a/intprep/impitemrecordtotal/findData'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        # 'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'pageNo': 1,
        'pageSize': 500,
        'recordType': selected_interFlag,
        'beginTime': start_date,
        'endTime': end_date,
        'recordEmp': blry,
        'itemId': mailno,
        'workSect': selected_routeLevel,
        'peStatus': selected_statusJ

    }
    response = session.post(url, headers=headers, params=data, verify=False)
    # response = session.get(url, headers=headers, verify=False)
    r = response.text
    #print(r)
    html = BeautifulSoup(r, 'lxml')
    # 找到指定class的div标签
    div = html.find('div', class_='span12')

    # 如果找到了指定的div标签
    if div:
        # 找到该div内的span标签，并提取文本内容
        span_text = div.span.get_text()
        print(span_text)
        tool.process_input(span_text)
    else:
        print("未找到指定的div标签")
    # 匹配数字部分
    match = re.search(r'共\s*(\d+)页', str(html))

    if match:
        page_number = int(match.group(1))
        print("提取的数字为:", page_number)
        if mailno is None:
            tool.process_input('共' + str(page_number) + '页')
    else:
        print(html)
        print("未找到匹配的数字")

    global dataOutput

    with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
        futures = [
            executor.submit(fetch_data, i, session, url, headers, selected_interFlag, start_date, end_date, blry, selected_routeLevel, selected_statusJ,parent,mailno)
            for i in range(1, page_number + 1)
        ]
        for future in as_completed(futures):
            response_text = future.result()
            parse_data(response_text, dataOutput)
def fetch_data(page_number, session, url, headers, selected_interFlag, start_date, end_date, blry, selected_routeLevel, selected_statusJ,parent,mailno):
    #print(f'Fetching page {page_number}')
    parent.after(0,tool.process_input(f'爬取第{page_number}页'))
    requests.packages.urllib3.disable_warnings()
    data = {
        'pageNo': page_number,
        'pageSize': 500,
        'recordType': selected_interFlag,
        'beginTime': start_date,
        'endTime': end_date,
        'recordEmp': blry,
        'itemId': mailno,
        'workSect': selected_routeLevel,
        'peStatus': selected_statusJ
    }
    response = session.post(url, headers=headers, params=data, verify=False)
    #print(response.text)
    return response.text

def parse_data(response_text, dataOutput):
    html = BeautifulSoup(response_text, 'lxml')
    target_tbodies = html.find('tbody')
    if target_tbodies:
        trs = target_tbodies.find_all('tr')
        for tr in trs:
            tds = tr.find_all('td')
            dataOutput["邮件号"].append(tds[0].get_text().strip())
            dataOutput["业务种类"].append(tds[1].get_text().strip())
            dataOutput["补录时间"].append(tds[2].get_text().strip())
            dataOutput["补录环节"].append(tds[3].get_text().strip())
            dataOutput["补录人员"].append(tds[4].get_text().strip())
            dataOutput["批译地址"].append(tds[5].get_text().strip())
def run(title,parent):
    try:
        global username, password, session, jdptid, L,dataOutput
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")

        # 构造Session
        session = requests.Session()
        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}
            tool.process_input("代理功能已启用")

        # 保存账号和密码
        tool.save_data()

        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password = tool.aes_encrypt(password, 'B+oQ52IuAt9wbMxw')


        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox2.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')

        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/intprep-web/a/intprep/impitemrecordtotal/list'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        #routeLevel()

        #getzb(parent)
        pool = Pool(int(threads_combobox.get()))
        # 创建一个偏函数，其中root参数预先设定
        getmailtrace_bound = partial(getzb, parent)
        # 如果 datalist 为空，提供一个包含空字符串的列表以触发默认逻辑
        if not datalist:
            datalist = [""]
        pool.map(getmailtrace_bound, datalist)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()

        dataForm = pandas.DataFrame(dataOutput)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel(interFlag_combobox.get()+routeLevel_combobox.get()+statusJ_combobox.get()+"-" +
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)

        tool.process_input("写入完成共" + str(number + 1) + "个文件")


        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")
    input_textbox2.delete("1.0", "end")
# 回调函数修改状态下拉框的选项列表
# def on_interFlag_change():
#     global statusJ_options
#     selected_interFlag = interFlag_combobox.get()
#     if selected_interFlag == "出口":
#         statusJ_options = {
#             "未封车": "0",
#             "已封车": "1",
#             "预封车": "2"
#         }
#     else:
#         statusJ_options = {
#             "未进局": "3",
#             "进局未解车": "4",
#             "已解车": "5"
#         }
#     statusJ_combobox.configure(values=list(statusJ_options.keys()))




def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox,input_textbox2, output_textbox, root, submit_button, button_clear, \
        start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,interFlag_options,interFlag_combobox,\
    routeLevel_options, routeLevel_combobox,statusJ_options,statusJ_combobox,dataOutput,starthour_var,startminute_var,endhour_var,endminute_var,\
    starthour_combobox,startminute_combobox,endhour_combobox,endminute_combobox,tool,back_button,organization_combobox,\
    startsecond_var, startsecond_combobox, endsecond_var, endsecond_combobox

    # 构造Session
    session = requests.Session()
    # 创建主窗口
    #root = tk.Tk()

    today = datetime.datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=[ "国际"])
    organization_combobox.set("国际")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=1, column=0)

    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=0, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(date_container, maxdate=today.date())
    start_date_entry.grid(row=0, column=1, padx=10, pady=10)
    # start_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=0, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    starthours = [str(h).zfill(2) for h in range(24)]
    startminutes = [str(m).zfill(2) for m in range(60)]
    startseconds = [str(s).zfill(2) for s in range(60)]

    starthour_var = StringVar()
    startminute_var = StringVar()
    startsecond_var = StringVar()

    starthour_combobox = ttk.Combobox(date_container, textvariable=starthour_var, values=starthours, width=5)
    starthour_combobox.set(starthours[0])
    starthour_combobox.grid(row=0, column=3, padx=5, pady=5)

    startminute_combobox = ttk.Combobox(date_container, textvariable=startminute_var, values=startminutes, width=5)
    startminute_combobox.set(startminutes[0])
    startminute_combobox.grid(row=0, column=4, padx=5, pady=5)
    # startminute_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())

    startsecond_combobox = ttk.Combobox(date_container, textvariable=startsecond_var, values=startseconds, width=5)
    startsecond_combobox.set(startseconds[0])
    startsecond_combobox.grid(row=0, column=5, padx=5, pady=5)

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=1, column=0, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(date_container, maxdate=today.date())
    end_date_entry.grid(row=1, column=1, padx=10, pady=10)
    # end_date_entry.pack()

    # 添加时间选择组件
    time_label = ttk.Label(date_container, text="选择时间:")
    time_label.grid(row=1, column=2, padx=10, pady=10)

    # 创建小时和分钟的下拉菜单
    endhours = [str(h).zfill(2) for h in range(24)]
    endminutes = [str(m).zfill(2) for m in range(60)]
    endseconds = [str(s).zfill(2) for s in range(60)]

    endhour_var = StringVar()
    endminute_var = StringVar()
    endsecond_var = StringVar()

    endhour_combobox = ttk.Combobox(date_container, textvariable=endhour_var, values=endhours, width=5)
    endhour_combobox.set(endhours[23])
    endhour_combobox.grid(row=1, column=3, padx=5, pady=5)

    endminute_combobox = ttk.Combobox(date_container, textvariable=endminute_var, values=endminutes, width=5)
    endminute_combobox.set(endminutes[59])
    endminute_combobox.grid(row=1, column=4, padx=5, pady=5)

    endsecond_combobox = ttk.Combobox(date_container, textvariable=endsecond_var, values=endseconds, width=5)
    endsecond_combobox.set(endseconds[59])
    endsecond_combobox.grid(row=1, column=5, padx=5, pady=5)

    # 添加查询类型下拉框
    interFlag_label = ttk.Label(date_container, text="补录环节:")
    interFlag_label.grid(row=2, column=0, padx=5, pady=10)

    interFlag_options = {
        "--不限--": "0",
        "1. ITMATT整理": "1",
        "2. 一级补录": "2",
        "3. 二级补录": "3",
        "4. 海关申报补录": "4",
        "5. 进口E特快": "5",
        "6. 直封开拆补录": "6",
        "7. 批译": "7"

    }
    interFlag_combobox = ttk.Combobox(date_container, values=list(interFlag_options.keys()))
    interFlag_combobox.grid(row=2, column=1, padx=5, pady=10)
    interFlag_combobox.set("--不限--")
    #interFlag_combobox.bind("<<ComboboxSelected>>", lambda _: on_interFlag_change())

    # 添加邮路级别下拉框
    routeLevel_label = ttk.Label(date_container, text="业务种类:")
    routeLevel_label.grid(row=2, column=2, padx=5, pady=10)

    routeLevel_options = {
        "全部": "",
        "EMS": "EMS",
        "E邮宝": "EYB",
        "包裹": "PCL",
        "函件": "LET",
        "其他": "OTH",
        "一点清关": "YQG",

    }
    routeLevel_combobox = ttk.Combobox(date_container, values=list(routeLevel_options.keys()))
    routeLevel_combobox.grid(row=2, column=3, padx=5, pady=10)
    routeLevel_combobox.set("全部")
    # 添加状态下拉框
    statusJ_label = ttk.Label(date_container, text="批译状态:")
    statusJ_label.grid(row=3, column=0, padx=5, pady=10)

    statusJ_options = {
        "全部": "",
        "未批译": "0",
        "已批译": "1"

    }
    statusJ_combobox = ttk.Combobox(date_container, values=list(statusJ_options.keys()))
    statusJ_combobox.grid(row=3, column=1, padx=5, pady=10)
    statusJ_combobox.set("全部")
    # 添加补录人员
    input_label = ttk.Label(date_container, text="补录人员:")
    input_label.grid(row=3, column=2, padx=10, pady=10)

    # input_label.pack()
    input_textbox = tk.Entry(date_container, width=30)
    input_textbox.grid(row=3, column=3, padx=10, pady=10, columnspan=1)

    # 添加多行输入框
    input_label2 = ttk.Label(date_container, text="邮件号:")
    input_label2.grid(row=4, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox2 = tk.Text(date_container, height=5, width=30)
    input_textbox2.grid(row=4, column=1, padx=10, pady=10, columnspan=1)
    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=2, column=0)

    # 添加交接对象输入框
    # input_label = ttk.Label(input_label_container, text="交接对象:")
    # input_label.grid(row=0, column=0, padx=10, pady=10)
    # # input_label.pack()
    # input_textbox = tk.Entry(input_label_container, width=30)
    # input_textbox.grid(row=0, column=1, padx=10, pady=10, columnspan=1)

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=1, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()