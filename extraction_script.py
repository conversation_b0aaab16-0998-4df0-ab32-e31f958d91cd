import re
import os
import json
from datetime import datetime
import asyncio
import ddddocr
from playwright.async_api import async_playwright

from encryption_analysis_tool import log_message, network_monitor, CAPTURE_DIR

# 配置
TARGET_JS_PATTERNS = [
    "_\\$jX",                # 目标函数1
    "bmF0aXZlRmlVyUHJ",      # 目标函数2 (可能是Base64编码的函数名)
    "MG9Olv7Z",              # 目标参数
    "enable_3bg4b5fckQas",   # 目标Cookie1
    "3bg4b5fckQasP"          # 目标Cookie2
]

OUTPUT_DIR = "js_analysis"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def scan_js_file(file_path):
    """扫描JavaScript文件，寻找目标模式"""
    results = {
        "file_path": file_path,
        "file_size": os.path.getsize(file_path),
        "matches": {}
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 匹配所有目标模式
            for pattern in TARGET_JS_PATTERNS:
                matches = re.finditer(pattern, content)
                contexts = []
                
                for match in matches:
                    # 获取匹配上下文 (前后50个字符)
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]
                    
                    # 记录匹配位置和上下文
                    contexts.append({
                        "position": match.start(),
                        "context": context
                    })
                
                if contexts:
                    results["matches"][pattern] = contexts
    
    except Exception as e:
        print(f"扫描文件 {file_path} 时出错: {e}")
        results["error"] = str(e)
    
    return results

def extract_function_definitions(file_path):
    """提取JavaScript文件中的函数定义"""
    function_patterns = [
        r'function\s+([A-Za-z0-9_$]+)\s*\([^)]*\)\s*{',  # 常规函数声明
        r'var\s+([A-Za-z0-9_$]+)\s*=\s*function\s*\([^)]*\)\s*{',  # 函数表达式
        r'let\s+([A-Za-z0-9_$]+)\s*=\s*function\s*\([^)]*\)\s*{',  # 函数表达式(let)
        r'const\s+([A-Za-z0-9_$]+)\s*=\s*function\s*\([^)]*\)\s*{',  # 函数表达式(const)
        r'([A-Za-z0-9_$]+)\s*:\s*function\s*\([^)]*\)\s*{',  # 对象方法
        r'([A-Za-z0-9_$]+)\s*=\s*new Function\('  # Function构造器
    ]
    
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 匹配所有函数定义模式
            for pattern in function_patterns:
                matches = re.finditer(pattern, content)
                
                for match in matches:
                    function_name = match.group(1)
                    
                    # 检查是否匹配目标函数名
                    if any(target in function_name for target in ["_$jX", "bmF0aXZlRmlVyUHJ"]):
                        # 定位函数体开始和结束
                        start_pos = match.start()
                        
                        # 尝试找到函数体结束位置(简单方法，不适用于所有复杂情况)
                        brace_count = 0
                        end_pos = start_pos
                        in_function = False
                        
                        for i, char in enumerate(content[start_pos:]):
                            if char == '{':
                                brace_count += 1
                                in_function = True
                            elif char == '}':
                                brace_count -= 1
                                if in_function and brace_count == 0:
                                    end_pos = start_pos + i + 1
                                    break
                        
                        # 提取整个函数定义
                        function_code = content[start_pos:end_pos]
                        
                        # 记录函数信息
                        results.append({
                            "name": function_name,
                            "position": start_pos,
                            "length": len(function_code),
                            "code": function_code
                        })
    
    except Exception as e:
        print(f"提取函数定义时出错 {file_path}: {e}")
    
    return results

def extract_eval_calls(file_path):
    """提取JavaScript文件中的eval调用"""
    eval_patterns = [
        r'eval\s*\(([^;]+?)\)',  # 基本eval调用
        r'Function\s*\([^)]*\)\s*\(\)',  # Function构造器调用
        r'new Function\s*\([^)]*\)'  # 新Function构造器
    ]
    
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 匹配所有eval调用模式
            for pattern in eval_patterns:
                matches = re.finditer(pattern, content)
                
                for match in matches:
                    # 获取上下文
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]
                    
                    # 记录eval调用信息
                    results.append({
                        "pattern": pattern,
                        "position": match.start(),
                        "context": context
                    })
    
    except Exception as e:
        print(f"提取eval调用时出错 {file_path}: {e}")
    
    return results

def analyze_cookie_operations(file_path):
    """分析JavaScript文件中的Cookie操作"""
    cookie_patterns = [
        r'document\.cookie\s*=\s*([^;]+)',  # 设置Cookie
        r'getCookie\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # 获取Cookie
        r'setCookie\s*\(\s*[\'"]([^\'"]+)[\'"]',  # 设置Cookie (通过函数)
    ]
    
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 匹配所有Cookie操作模式
            for pattern in cookie_patterns:
                matches = re.finditer(pattern, content)
                
                for match in matches:
                    # 获取上下文
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 100)  # 获取更多后面的内容
                    context = content[start:end]
                    
                    # 检查是否包含目标Cookie
                    cookie_match = any(target in context for target in ["enable_3bg4b5fckQas", "3bg4b5fckQasP"])
                    
                    if cookie_match:
                        # 记录Cookie操作信息
                        results.append({
                            "pattern": pattern,
                            "position": match.start(),
                            "context": context,
                            "target_cookie_found": True
                        })
    
    except Exception as e:
        print(f"分析Cookie操作时出错 {file_path}: {e}")
    
    return results

def search_for_encryption_functions(file_path):
    """寻找可能的加密/解密函数"""
    # 常见的加密相关函数或方法名
    encryption_patterns = [
        r'encrypt',
        r'decrypt',
        r'encode',
        r'decode',
        r'AES',
        r'MD5',
        r'SHA',
        r'hash',
        r'base64'
    ]
    
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # 匹配所有加密相关模式
            for pattern in encryption_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                
                for match in matches:
                    # 获取上下文
                    start = max(0, match.start() - 50)
                    end = min(len(content), match.end() + 50)
                    context = content[start:end]
                    
                    # 记录匹配信息
                    results.append({
                        "pattern": pattern,
                        "position": match.start(),
                        "context": context
                    })
    
    except Exception as e:
        print(f"搜索加密函数时出错 {file_path}: {e}")
    
    return results

def analyze_js_file(file_path):
    """综合分析单个JavaScript文件"""
    print(f"分析文件: {file_path}")
    
    results = {
        "file_path": file_path,
        "timestamp": datetime.now().isoformat(),
        "file_size": os.path.getsize(file_path),
        "pattern_matches": {},
        "functions": [],
        "eval_calls": [],
        "cookie_operations": [],
        "encryption_functions": []
    }
    
    # 扫描目标模式
    scan_results = scan_js_file(file_path)
    results["pattern_matches"] = scan_results.get("matches", {})
    
    # 提取函数定义
    results["functions"] = extract_function_definitions(file_path)
    
    # 提取eval调用
    results["eval_calls"] = extract_eval_calls(file_path)
    
    # 分析Cookie操作
    results["cookie_operations"] = analyze_cookie_operations(file_path)
    
    # 搜索加密函数
    results["encryption_functions"] = search_for_encryption_functions(file_path)
    
    # 保存分析结果
    file_name = os.path.basename(file_path)
    output_file = os.path.join(OUTPUT_DIR, f"{file_name}_analysis.json")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"保存分析结果到: {output_file}")
    
    return results

def analyze_directory(directory):
    """分析指定目录下的所有JavaScript文件"""
    results = {
        "timestamp": datetime.now().isoformat(),
        "directory": directory,
        "files_analyzed": 0,
        "target_patterns_found": 0,
        "key_functions_found": 0,
        "summary": {}
    }
    
    all_files = []
    
    # 遍历目录
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.js'):
                file_path = os.path.join(root, file)
                all_files.append(file_path)
    
    print(f"找到 {len(all_files)} 个JavaScript文件")
    
    # 分析所有JS文件
    for file_path in all_files:
        file_results = analyze_js_file(file_path)
        results["files_analyzed"] += 1
        
        # 统计目标模式匹配
        patterns_found = len(file_results["pattern_matches"])
        if patterns_found > 0:
            results["target_patterns_found"] += patterns_found
        
        # 统计关键函数
        key_functions = [f for f in file_results["functions"] 
                         if any(target in f["name"] for target in ["_$jX", "bmF0aXZlRmlVyUHJ"])]
        results["key_functions_found"] += len(key_functions)
        
        # 记录摘要
        results["summary"][file_path] = {
            "patterns_found": patterns_found,
            "functions_count": len(file_results["functions"]),
            "key_functions_count": len(key_functions),
            "eval_calls_count": len(file_results["eval_calls"]),
            "cookie_operations_count": len(file_results["cookie_operations"]),
            "encryption_functions_count": len(file_results["encryption_functions"])
        }
    
    # 保存总体分析结果
    output_file = os.path.join(OUTPUT_DIR, f"directory_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"保存目录分析结果到: {output_file}")
    
    return results

def analyze_extracted_js_files():
    """分析提取的JavaScript文件"""
    # 常见的提取目录
    possible_dirs = ["extracted_js", "captured_js", "js"]
    
    for directory in possible_dirs:
        if os.path.exists(directory) and os.path.isdir(directory):
            print(f"分析目录: {directory}")
            analyze_directory(directory)
            return
    
    print("未找到JavaScript文件目录，请先运行extraction_tool.py提取JavaScript文件")

async def inject_js_tracker(page):
    # 首先注入反调试绕过代码
    await page.add_init_script("""
    (function() {
        // 1. 禁用debugger语句
        const originalDebugger = Function.prototype.constructor;
        Function.prototype.constructor = function(code) {
            if (code && typeof code === 'string' && code.includes('debugger')) {
                code = code.replace(/debugger/g, '/* 已禁用debugger */');
            }
            return originalDebugger.apply(this, arguments);
        };
        
        // 2. 处理时间差检测反调试
        const originalDate = Date;
        window.Date = function(...args) {
            return new originalDate(...args);
        };
        
        // 复制原有的方法到新构造函数
        for (let prop in originalDate) {
            if (originalDate.hasOwnProperty(prop)) {
                window.Date[prop] = originalDate[prop];
            }
        }
        
        // 修改原型
        window.Date.prototype = originalDate.prototype;
        
        // 创建固定时间间隔以防止检测
        let lastTime = originalDate.now();
        const originalNow = originalDate.now;
        window.Date.now = function() {
            lastTime += 5; // 每次调用只增加5毫秒
            return lastTime;
        };
        
        console.log("反调试保护已启用");
    })();
    """)
    
    # 然后注入修改后的追踪代码
    tracking_script = """
    (function() {
        console.log('启动增强型参数追踪器...');
        
        // 创建日志记录功能
        window._paramLogs = [];
        function logParam(message) {
            console.log('[参数追踪]', message);
            window._paramLogs.push({
                time: new Date().toISOString(),
                message: message
            });
        }
        
        // 监控XHR请求
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url) {
            if (url && url.toString().includes('MG9Olv7Z')) {
                logParam('捕获请求参数: ' + url);
                try {
                    throw new Error();
                } catch(e) {
                    logParam('堆栈: ' + e.stack.split('\\n').slice(1, 3).join(' -> '));
                }
                
                // 同时捕获当前所有Cookie
                const cookies = document.cookie.split('; ');
                cookies.forEach(cookie => {
                    if (cookie.includes('3bg4b5fckQasP') || cookie.includes('enable_3bg4b5fckQas')) {
                        logParam('关联Cookie: ' + cookie);
                    }
                });
            }
            return originalXHROpen.apply(this, arguments);
        };
        
        // 使用被动观察模式
        let paramMonitor = setInterval(() => {
            try {
                // 检测URL中的参数
                const links = Array.from(document.querySelectorAll('a[href*="MG9Olv7Z"]'));
                links.forEach(link => {
                    logParam('发现链接包含参数: ' + link.href);
                });
                
                // 检测网络请求
                const entries = performance.getEntriesByType('resource')
                    .filter(r => r.name.includes('MG9Olv7Z'))
                    .slice(-5); // 只取最新的5个
                
                entries.forEach(entry => {
                    logParam('网络请求包含参数: ' + entry.name);
                });
                
                // 检测Cookie变化
                document.cookie.split('; ').forEach(cookie => {
                    if (cookie.includes('3bg4b5fckQasP') || 
                        cookie.includes('enable_3bg4b5fckQas')) {
                        logParam('检测到目标Cookie: ' + cookie);
                    }
                });
            } catch(e) {
                // 忽略错误
            }
        }, 1000);
        
        logParam('增强型参数追踪器已启动 - 被动模式');
    })();
    """
    
    await page.add_init_script(tracking_script)
    
    # 设置函数以获取跟踪日志
    async def get_tracking_logs():
        logs = await page.evaluate("window._paramLogs || []")
        return logs
    
    # 定期收集跟踪日志
    async def collect_logs():
        while True:
            try:
                logs = await get_tracking_logs()
                if logs:
                    log_file = os.path.join(CAPTURE_DIR, "param_tracking_logs.json")
                    with open(log_file, "w", encoding="utf-8") as f:
                        json.dump(logs, f, indent=2)
            except Exception as e:
                log_message(f"获取跟踪日志失败: {e}")
            await asyncio.sleep(5)  # 每5秒收集一次日志
    
    # 启动日志收集任务
    asyncio.create_task(collect_logs())
    
    log_message("参数追踪代码注入完成")
    return get_tracking_logs

async def download_js_files(page):
    # 确保目录存在
    os.makedirs("captured_js", exist_ok=True)
    
    # 访问页面获取JS文件
    await page.goto("https://10.4.188.1/cas/login", timeout=30000)
    
    # 捕获所有JS文件请求
    js_resources = []
    async def on_response(response):
        if response.url.endswith('.js'):
            js_resources.append(response)
    
    page.on('response', on_response)
    
    # 等待页面完全加载
    await page.wait_for_load_state('networkidle')
    
    # 下载所有JS文件
    for response in js_resources:
        url = response.url
        filename = url.split('/')[-1]
        
        try:
            content = await response.text()
            with open(f"captured_js/{filename}", "w", encoding="utf-8") as f:
                f.write(content)
            print(f"下载文件: {filename}")
        except Exception as e:
            print(f"下载文件失败 {url}: {e}")
            
    # 特别下载关键文件
    try:
        target_file = await page.evaluate("""
            async function getFile() {
                const response = await fetch('/cas/I66ecIB2AbeL/MWDdIri1zTyE.632fab5.js');
                return await response.text();
            }
            getFile();
        """)
        
        with open("captured_js/MWDdIri1zTyE.632fab5.js", "w", encoding="utf-8") as f:
            f.write(target_file)
        print("成功下载关键文件: MWDdIri1zTyE.632fab5.js")
    except Exception as e:
        print(f"下载关键文件失败: {e}")

def extract_eval_code(js_content):
    # 找到eval调用
    eval_match = re.search(r'eval\s*\(([^;]+?)\)', js_content)
    if not eval_match:
        return None
    
    # 获取eval参数
    eval_param = eval_match.group(1)
    
    # 创建提取代码
    extraction_code = f"""
    // 保存原始eval
    const originalEval = window.eval;
    
    // 替换eval以捕获代码
    window.eval = function(code) {{
        console.log("EVAL_CODE_START");
        console.log(code);
        console.log("EVAL_CODE_END");
        return code; // 不执行代码
    }};
    
    // 执行预期会调用eval的代码
    try {{
        {eval_param}
    }} catch(e) {{
        console.log("ERROR: " + e);
    }}
    
    // 恢复原始eval
    window.eval = originalEval;
    """
    
    return extraction_code

async def main():
    """主函数: 完全自动化的请求捕获流程"""
    global capture_count
    capture_count = 0  # 重置计数器
    log_message("开始自动化请求捕获过程...")
    
    # 实例化OCR对象
    ocr = ddddocr.DdddOcr()
    
    async with async_playwright() as p:
        browser = await p.firefox.launch(
            headless=False,
            args=["--disable-blink-features=AutomationControlled"]
        )
        context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
        page = await context.new_page()
        
        # 注入追踪代码
        await inject_js_tracker(page)
        
        try:
            # 访问登录页面
            log_message("访问登录页面...")
            await page.goto("https://10.4.188.1/cas/login", timeout=30000)
            
            # 重要：移除finally块中的browser.close()，改为手动控制退出
            log_message("追踪器已启动，请在浏览器中操作以触发参数生成...")
            # 手动使脚本保持运行
            await asyncio.Future()  # 这将创建一个永不完成的Future，使脚本一直运行
        except Exception as e:
            log_message(f"过程中出现错误: {e}")
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main()) 