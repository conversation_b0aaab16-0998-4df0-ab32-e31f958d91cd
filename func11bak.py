import io
import os
import socket
import tkinter as tk
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import reduce
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote

import requests,json, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading

from tool import Tool

# 修改全局变量 dataOutput 为线程安全的队列



lock = threading.Lock()
# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称


def getmailtrace(mailno,parent):
    global L,dataOutput
    # print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_no': mailno,
        'numType': 15,
        'limit': 20

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text

    jsonObj = json.loads(r)
    # dataOutput = {
    #     "邮件号": [],
    #     "收寄省": [],
    #     "收寄地市": [],
    #     "收寄局": [],
    #     "寄达地": [],
    #     "重量": [],
    #     "标准资费": [],
    #     "收件人": [],
    #     "产品": [],
    #     "产品代码": [],
    #     "基础产品": [],
    #     "基础产品代码": []
    # }
    # 通过循环来获取JSON中的数据，并添加到字典中
    for line in jsonObj:
        if "opName" in line:
            if '收寄计费信息' in line["opName"]:
                dataOutput["邮件号"].append(line["traceNo"])  # 获取JSON中的id，并存入字典中的id内
                zl = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                if zl:
                    zl = zl[0]
                else:
                    zl = ''
                dataOutput["重量"].append(zl)
                bzzf = re.findall(r'标准资费:(.*),', str(line["internalDesc"]))
                if bzzf:
                    bzzf = bzzf[0]
                else:
                    bzzf = ''
                dataOutput["标准资费"].append(bzzf)
                sjr = re.findall(r'收件人:(.*)', str(line["internalDesc"]))
                if sjr:
                    sjr = sjr[0]
                else:
                    sjr = ''
                dataOutput["收件人"].append(sjr)
                dataOutput["收寄省"].append(line.get('opOrgProvName', ''))
                dataOutput["收寄地市"].append(line.get('opOrgCity', ''))
                dataOutput["收寄局"].append(line.get('opOrgSimpleName', ''))
                dataOutput["寄达地"].append(line.get('receivePlace', ''))
                dataOutput["基础产品"].append(line.get('baseProductName', ''))
                dataOutput["基础产品代码"].append(line.get('baseProductNo', ''))
                dataOutput["产品"].append(line.get('bizProductName', ''))
                dataOutput["产品代码"].append(line.get('bizProductNo', ''))
                break
    L += 1
    if L % 1000 == 0:
        # 添加适当的同步机制，例如使用 threading.Lock
        with lock:
            # 模拟一些耗时操作
            #time.sleep(1)
            # 在主线程中调度更新UI
            parent.after(0, tool.process_input('已爬' + str(L) + '件'))
    # 循环结束后释放session资源
    session.close()
    # return dataOutput


 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1


def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data,dataOutput


        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        #tool.postlog(username,title,ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        i = 2

        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        #print(int(threads_combobox.get()))
        pool = Pool(int(threads_combobox.get()))
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果

        # 并发执行并获取结果

        # merged_data = {
        #         "邮件号": [],
        #         "收寄省":[],
        #         "收寄地市":[],
        #         "收寄局":[],
        #         "寄达地": [],
        #         "重量":[],
        #         "标准资费":[],
        #         "收件人":[],
        #         "产品":[],
        #         "产品代码":[],
        #         "基础产品":[],
        #         "基础产品代码":[]
        #
        # }
        dataOutput={
                "邮件号": [],
                "收寄省":[],
                "收寄地市":[],
                "收寄局":[],
                "寄达地": [],
                "重量":[],
                "标准资费":[],
                "收件人":[],
                "产品":[],
                "产品代码":[],
                "基础产品":[],
                "基础产品代码":[]

        }
        # results = pool.map(lambda mailno: getmailtrace(mailno, parent), datalist)
        #pool.map(lambda mailno: getmailtrace(mailno, parent), datalist)
        # merged_data = reduce(merge_dicts, results, merged_data)

        def update_ui(message):
            tool.process_input(message)

            # 初始化数据输出字典（在多线程环境中需要为每个线程创建独立的数据输出）

        shared_data = []
        futures = []

        with ThreadPoolExecutor(max_workers=int(threads_combobox.get())) as executor:
            for mailno in datalist:
                future = executor.submit(getmailtrace, mailno, parent)
                future.add_done_callback(
                    lambda fut: parent.after(0,
                                             lambda: update_ui(f'爬取进度：已完成 {len(shared_data)}/{len(datalist)}'))
                )
                futures.append(future)

            for future in as_completed(futures):
                result = future.result()
                shared_data.append(result)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()
        # dataForm = pandas.DataFrame(merged_data)
        dataForm = pandas.DataFrame(shared_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("邮件收寄计费信息-"+
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        # del merged_data
        del dataOutput
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")


# 从网页获取.ico文件并保存到本地
def download_icon(url, save_path):
    response = requests.get(url)
    if response.status_code == 200:
        with open(save_path, 'wb') as f:
            f.write(response.content)
        return True
    else:
        return False

# 设置窗口图标
def set_window_icon(icon_path):
    if os.path.exists(icon_path):
        root = Tk()
        root.iconbitmap(icon_path)
        root.mainloop()
    else:
        print("文件路径不存在")

# 网页链接
web_icon_url = 'http://10.194.69.53:42300/' + quote('chinapost.ico')
# 保存图标的本地路径
local_icon_path = "chinapost.ico"


def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = 'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项


    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # # 创建单选按钮
    # selected = tk.StringVar(value='全程轨迹')
    # radio_label = ttk.Label(input_label_container, text="功能选择:")
    # radio_label.grid(row=0, column=0, padx=10, pady=10)
    # radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    # radio_button1.grid(row=0, column=1, padx=5, pady=10)
    #
    # radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    # radio_button2.grid(row=0, column=2, padx=5, pady=10)



    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=1, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=1, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=2, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=2, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()