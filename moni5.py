import asyncio
import io
import socket
import sys
import tkinter as tk
import traceback
from functools import partial

from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
from tkcalendar import DateEntry
from bs4 import BeautifulSoup
import requests,json, time, re, os
from PIL import Image
import datetime
from datetime import  timedelta
import warnings
from datetime import datetime
from playwright.async_api import async_playwright
from functools import reduce
import pandas as pd
import threading
from os import path
from tool import Tool

executable_path = "firefox/firefox.exe"    # 指定可执行文件路径，用相对路径
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1
# 需要匹配的 URL 固定部分（正则或字符串）
TARGET_URL_PARTS = [
    'queryTraceByTrace_noHidden',  # 只匹配包含这部分的 URL
]
# 请求信息类
class RequestInfo:
    def __init__(self, url, response_data=None):
        self.url = url
        self.response_data = response_data

# 用于批量处理邮件号查询并导出数据
async def batch_query_and_export(title,parent):
    global L
    # 程序已经在运行中，禁用按钮
    submit_button.configure(state="disabled")
    back_button.configure(state="disabled")

    start = time.perf_counter()
    # 构造Session
    session = requests.Session()
    # 获取本机主机名
    hostname = socket.gethostname()
    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}

        tool.process_input("代理功能已启用")
    L = 0

    # 保存账号和密码
    tool.save_data()
    username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
    password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
    try:
        # 使用 Playwright 启动浏览器
        async with async_playwright() as p:
            browser = await p.firefox.launch(headless=True,
                                             executable_path=executable_path,
                                             args=["--no-sandbox", "--disable-setuid-sandbox"])

            # 创建浏览器上下文
            context = await browser.new_context(bypass_csp=True, ignore_https_errors=True)
            page = await context.new_page()
            # 获取网络请求
            # page.on("request", lambda request: print( ">> ", request.method, request.url))
            # # 获取网络响应
            # page.on("response", lambda response: print( "<< ", response.status, response.text))

            # 设置 User-Agent模拟正常浏览器
            await context.set_extra_http_headers({
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            })

            # 禁用Playwright的自动化标识
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)
            # 获取文本框中的文本
            text = inputmailno_textbox.get("1.0", "end-1c")
            # 将文本按行分割并去除空行
            lines = text.splitlines()
            lines = [line for line in lines if line.strip()]
            if len(lines) == 0:
                i = 2

                tool.process_input('开始登录鹰眼')
                tool.process_input('第1次尝试登录')
                yy = tool.loginyy(session)
                while (yy == '0'):
                    tool.process_input('第' + str(i) + '次尝试登录')
                    yy = tool.loginyy(session)
                    i += 1
                mailnos = getunifyReport()
            else:
                mailnos = lines


            # 登录
            userName,orgName,orgcode=await tool.newlogin(page, username, password)
            tool.postlog(username, userName, title, ip_address)
            await page.close()  # 登录完成后关闭页面

            all_logistics_data = []
            # for i, mailno in enumerate(mailnos):
            #     tool.process_input(f"正在查询第 {i+1} 个邮件号: {mailno}")
            #     logistics_data = await query_mailno(page, mailno)
            #     if logistics_data:
            #         all_logistics_data.extend(logistics_data)
            # 限制并发的最大任务数
            semaphore = asyncio.Semaphore(int(threads_combobox.get()))  # 设置并发上限为 5（可调整）

            async def query_with_semaphore(mailno,all_logistics_data):
                async with semaphore:
                    # 每个任务创建一个新的页面
                    page = await context.new_page()
                    try:
                        #tool.process_input(f"正在查询邮件号: {mailno}")

                        logistics_data = await query_mailno(page, mailno, parent, all_logistics_data)


                    finally:
                        await page.close()  # 确保页面关闭以释放资源
                    return logistics_data

            # 创建任务列表
            tasks = [query_with_semaphore(mailno,all_logistics_data) for mailno in mailnos]

            # 并发执行任务
            results = await asyncio.gather(*tasks)

            # 收集所有结果
            for logistics_data in results:
                if logistics_data:
                    all_logistics_data.extend(logistics_data)

            #merged_data = reduce(merge_dicts, all_logistics_data, merged_data)
            #tool.process_input(f"共查询到{len(merged_data['邮件号'])}条数据")
            tool.process_input("\n正在导出数据...")
            # 导出到Excel
            if all_logistics_data:

                df = pd.DataFrame(all_logistics_data)
                # # 生成Excel文件的保存路径，当前目录 + 当前时间
                currentTime = datetime.now().strftime('%Y%m%d_%H%M%S')
                # export_path = os.path.join(os.getcwd(), f'邮件全轨迹_{current_time}.xlsx')
                row = 1048570
                length = len(df)
                number = length // row
                for i in range(number + 1):
                    export_path=os.path.join(os.getcwd(), f'邮件全程处理信息-{currentTime}{i}-bylhx.xlsx')
                    df[i * row:(i + 1) * row].to_excel(export_path,index=False)
                # 保存文件
                #df.to_excel(export_path, index=False)
                    tool.process_input(f"\n数据已成功导出！\n文件路径：{export_path}")
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
                tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
                del all_logistics_data
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
                #messagebox.showinfo("成功", f"数据已成功导出！文件路径：{export_path}")
            else:
                #messagebox.showwarning("警告", "没有获取到有效数据！")
                tool.process_input("没有获取到有效数据")
                # 程序未在运行中，启用按钮
                submit_button.configure(state="normal")
                back_button.configure(state="normal")
    except Exception as e:
        tool.process_input(f"查询失败，错误信息：{e}")
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
    await browser.close()

# 封装页面加载、输入和点击的任务逻辑
async def load_and_query_page(page, mailno, parent,max_retries,retry_delay):
    # 任务逻辑
    for attempt in range(max_retries):
        try:
            await page.goto('https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList')

            await page.wait_for_selector('#traceNo', timeout=10000)
            # 输入邮件号
            await page.fill('#traceNo', mailno)

            # 点击查询按钮
            await page.click('#btnSubmit')

            try:
                await page.wait_for_selector('.mailQuery_container', timeout=10000)
                return []  # 如果加载成功，返回一个空列表或者其他你希望的内容
            except Exception:
                # tool.process_input(f"查询邮件号 {mailno} 超时，返回空数据。")
                #parent.after(0, tool.process_input(f"邮件号 {mailno} 没有轨迹信息。"))
                # 封装查询的部分，支持重试
                return []

        except Exception as e:
            # tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}")
            # parent.after(0, tool.process_input(f"查询邮件号 {mailno} 发生错误: {e}"))
            # 捕获页面加载时的异常，如果是超时或者其他错误
            print(f"第 {attempt + 1} 次加载失败: {str(e)}")

            # 如果达到最大重试次数，则退出
            if attempt == max_retries - 1:
                parent.after(0, tool.process_input(f"\n页面加载失败，邮件号: {mailno}"))
                return []

            # 否则等待重试
            print(f"等待 {retry_delay} 秒后重试...")
            await asyncio.sleep(retry_delay)

# 查询邮件号全程轨迹函数
async def query_mailno(page, mailno,parent, logistics_data, max_retries=5, retry_delay=0.5):
    global L

    # 获取网络响应
    async def handle_response(response):
        # 判断响应的 URL 是否包含目标部分
        if any(target_part in response.url for target_part in TARGET_URL_PARTS):
            print(f"<< Status: {response.status} URL: {response.url}")
            try:
                # 获取响应文本
                r = await response.text()

                # print(r)

                try:
                    jsonObj = json.loads(r)

                    yjh = mailno
                    sfs = ""
                    sfd = ""
                    zds = ""
                    mdd = ""
                    ddjrsj = ""
                    sjj = ""
                    sjsj = ""
                    sjdqclsc = ""
                    sjjjsj = ""
                    fydw = ""
                    fydwfcsj = ""
                    yssj = ""
                    ddghsj = ""
                    jcjjdx = ""
                    ghjcsj = ""
                    ghffsj = ""
                    ghpfsj = ""
                    ghnbclsc = ""
                    qfsj = ""
                    hbh = ""
                    hbyl = ""
                    ldsj = ""
                    tqsc = ""
                    ddjssj = ""
                    ddclsc = ""
                    tdsj = ""
                    tdjg = ""
                    qcsc = ""
                    fcsj = ""
                    fcjjdx = ""
                    cp = ""
                    ztyssc = ""
                    whgbsj = ""
                    nhgbsj = ""
                    whlgsj = ""
                    nhlgsj = ""
                    whqgsj = ""
                    nhqgsj = ""
                    rgqgry = ""
                    rgqgsj = ""
                    jzcfgbsj = ""
                    jzcflgsj = ""
                    jzxfgbsj = ""
                    jzxflgsj = ""
                    zl = ''
                    # 通过循环来获取JSON中的数据，并添加到字典中
                    for line in jsonObj:
                        if "opName" in line:
                            if '收寄计费信息' in line["opName"]:
                                # yjh = line["traceNo"]
                                sfs = line.get('opOrgProvName', '')
                                sfd = line.get('opOrgCity', '')
                                if "receivePlace" in line:
                                    zdss = re.findall(r'(?:上海市|北京市|天津市|重庆市|[\u4e00-\u9fa5]+(?:省|自治区))',
                                                      str(line["receivePlace"]))
                                    # mdd=re.findall(r'([\u4e00-\u9fa5]+市)', str(line["receivePlace"]))
                                    mdds = re.findall(r'(?:上海市|北京市|天津市|重庆市)', str(line["receivePlace"]))
                                    if zdss:
                                        zds = zdss[0]

                                    if mdds:
                                        mdd = mdds[0]
                                    else:
                                        mdd = line["receivePlace"].replace(zds, '')
                                sjj = line.get('opOrgSimpleName', '')
                                sjsj = line["opTime"]
                                zls = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                                if zls:
                                    zl = zls[0]
                            # if '收寄交接' in line["opName"]:
                            #     sjjjsj = line["opTime"]
                            if '国内订单接入' in line["opName"]:
                                ddjrsj = line["opTime"]
                            if '封车' in line["opName"] and 'originalDesc' in line and '下一站:广州航空中心' in line[
                                "originalDesc"] and "opOrgSimpleName" in line and '广州航空中心' not in line[
                                "opOrgSimpleName"]:
                                fydwfcsj = line["opTime"]
                                fydw = line["opOrgSimpleName"]
                            if '处理中心车辆进局' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in \
                                    line["opOrgSimpleName"]:
                                ddghsj = line["opTime"]
                            if '处理中心解车' in line["opName"] and '51000061' in line["opOrgCode"]:
                                ghjcsj = line["opTime"]
                                jcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                                if jcjjdx:
                                    jcjjdx = jcjjdx[0]
                                # print(jcjjdx)
                            if '处理中心分拣机供包扫描' in line[
                                "opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                                if '5100006101243' in line.get("operatorNo", ""):
                                    whgbsj = line["opTime"]
                                if '5100006101244' in line.get("operatorNo", ""):
                                    nhgbsj = line["opTime"]
                                if '5100006101241' in line.get("operatorNo", ""):
                                    jzcfgbsj = line["opTime"]
                                if '5100006101242' in line.get("operatorNo", ""):
                                    jzxfgbsj = line["opTime"]
                            if '分拣机落格' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line[
                                "opOrgSimpleName"]:
                                if '单层小件交叉带分拣机1' in line.get("originalDesc", ""):
                                    whlgsj = line["opTime"]
                                if '单层小件交叉带分拣机2' in line.get("originalDesc", ""):
                                    nhlgsj = line["opTime"]
                                if '摆轮矩阵分拣系统(粗分)' in line.get("originalDesc", ""):
                                    jzcflgsj = line["opTime"]
                                if '摆轮矩阵分拣系统(细分)' in line.get("originalDesc", ""):
                                    jzxflgsj = line["opTime"]
                            if '处理中心包分机自动齐格' in line[
                                "opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                                if '5100006101243' in line.get("operatorNo", ""):
                                    whqgsj = line["opTime"]
                                elif '5100006101244' in line.get("operatorNo", ""):
                                    nhqgsj = line["opTime"]
                                else:
                                    rgqgry = line["operatorName"] + line["operatorNo"]
                                    rgqgsj = line["opTime"]
                            if '处理中心扫描配发' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in \
                                    line["opOrgSimpleName"]:
                                ghpfsj = line["opTime"]
                            if '航班起飞' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line[
                                "opOrgSimpleName"]:
                                qfsj = line["opTime"]
                                hbh = re.findall(r'航班号:(\w+)', str(line["originalDesc"]))
                                if hbh:
                                    hbh = hbh[0]
                                else:
                                    hbh = ''
                                hbyl = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                                if hbyl:
                                    hbyl = hbyl[0]
                                else:
                                    hbyl = ''
                            if '处理中心封车' in line["opName"] and '51000061' in line["opOrgCode"]:
                                fcsj = line["opTime"]
                                cp = re.findall(r'车牌:(\w+)', str(line["originalDesc"]))
                                if cp:
                                    cp = cp[0]
                                else:
                                    cp = ''
                                fcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                                if fcjjdx:
                                    fcjjdx = fcjjdx[0]
                                else:
                                    fcjjdx = ''
                            if '航班降落' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' not in \
                                    line["opOrgSimpleName"] and hbh in line["originalDesc"]:
                                ldsj = line["opTime"]
                            if '处理中心解车' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' not in \
                                    line["opOrgSimpleName"] and 'lastOrgName' in line and '广州航空中心' in line[
                                "lastOrgName"]:
                                ddjssj = line["opTime"]
                            if '投递结果反馈-妥投' in line["opName"]:
                                tdsj = line["opTime"]
                                tdjg = line["opOrgSimpleName"]
                    if not qfsj:
                        for line in jsonObj:
                            if '处理中心封车' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in \
                                    line["opOrgSimpleName"]:
                                qfsj = line["opTime"]
                    # 收寄单位处理时长
                    # 将日期字符串转换为datetime对象
                    if fydwfcsj.strip() != "" and sjsj.strip() != "":
                        fydwfcsj_obj1 = datetime.strptime(fydwfcsj, '%Y-%m-%d %H:%M:%S')
                        sjsj_obj2 = datetime.strptime(sjsj, '%Y-%m-%d %H:%M:%S')

                        # 计算时间差
                        sjdqclsc_diff = fydwfcsj_obj1 - sjsj_obj2

                        # 计算天数、小时数、分钟数和秒数
                        days = sjdqclsc_diff.days * 24

                        hour = sjdqclsc_diff.seconds // 3600
                        minute = (sjdqclsc_diff.seconds % 3600) / 60
                        sjdqclsc = "%.2f" % (days + hour + minute / 60)

                        # 运输时间
                    # 将日期字符串转换为datetime对象
                    if ddghsj.strip() != "" and fydwfcsj.strip() != "":
                        ddghsj_obj1 = datetime.strptime(ddghsj, '%Y-%m-%d %H:%M:%S')
                        fydwfcsj_obj2 = datetime.strptime(fydwfcsj, '%Y-%m-%d %H:%M:%S')

                        # 计算时间差
                        yssj_diff = ddghsj_obj1 - fydwfcsj_obj2

                        # 计算天数、小时数、分钟数和秒数
                        days = yssj_diff.days * 24

                        hour = yssj_diff.seconds // 3600
                        minute = (yssj_diff.seconds % 3600) / 60
                        yssj = "%.2f" % (days + hour + minute / 60)

                    # 内部处理时长
                    if qfsj.strip() != "" and ddghsj.strip() != "":
                        sorter = sorter_var.get()
                        if sorter == "发航":
                            qfsj_obj1 = datetime.strptime(qfsj, '%Y-%m-%d %H:%M:%S')
                            ddghsj_obj1 = datetime.strptime(ddghsj, '%Y-%m-%d %H:%M:%S')

                            # 计算时间差
                            ghnbclsc_diff = qfsj_obj1 - ddghsj_obj1

                            # 计算天数、小时数、分钟数和秒数
                            days = ghnbclsc_diff.days * 24

                            hour = ghnbclsc_diff.seconds // 3600
                            minute = (ghnbclsc_diff.seconds % 3600) / 60
                            ghnbclsc = "%.2f" % (days + hour + minute / 60)
                        else:
                            fcsj_obj1 = datetime.strptime(fcsj, '%Y-%m-%d %H:%M:%S')
                            ghjcsj_obj1 = datetime.strptime(ghjcsj, '%Y-%m-%d %H:%M:%S')

                            # 计算时间差
                            ghnbclsc_diff = fcsj_obj1 - ghjcsj_obj1

                            # 计算天数、小时数、分钟数和秒数
                            days = ghnbclsc_diff.days * 24

                            hour = ghnbclsc_diff.seconds // 3600
                            minute = (ghnbclsc_diff.seconds % 3600) / 60
                            ghnbclsc = "%.2f" % (days + hour + minute / 60)
                    # 提取时长
                    if ddjssj.strip() != "" and ldsj.strip() != "":
                        ddjssj_obj1 = datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
                        ldsj_obj1 = datetime.strptime(ldsj, '%Y-%m-%d %H:%M:%S')

                        # 计算时间差
                        tqsc_diff = ddjssj_obj1 - ldsj_obj1

                        # 计算天数、小时数、分钟数和秒数
                        days = tqsc_diff.days * 24

                        hour = tqsc_diff.seconds // 3600
                        minute = (tqsc_diff.seconds % 3600) / 60
                        tqsc = "%.2f" % (days + hour + minute / 60)

                    # 中途运输时长
                    if ddjssj.strip() != "" and fcsj.strip() != "":
                        ddjssj_obj1 = datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
                        fcsj_obj1 = datetime.strptime(fcsj, '%Y-%m-%d %H:%M:%S')

                        # 计算时间差
                        ztyssc_diff = ddjssj_obj1 - fcsj_obj1

                        # 计算天数、小时数、分钟数和秒数
                        days = ztyssc_diff.days * 24

                        hour = ztyssc_diff.seconds // 3600
                        minute = (ztyssc_diff.seconds % 3600) / 60
                        ztyssc = "%.2f" % (days + hour + minute / 60)

                    # 对端处理时长
                    if tdsj.strip() != "" and ddjssj.strip() != "":
                        tdsj_obj1 = datetime.strptime(tdsj, '%Y-%m-%d %H:%M:%S')
                        ddjssj_obj1 = datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
                        # 计算时间差
                        ddclsc_diff = tdsj_obj1 - ddjssj_obj1

                        # 计算天数、小时数、分钟数和秒数
                        days = ddclsc_diff.days * 24

                        hour = ddclsc_diff.seconds // 3600
                        minute = (ddclsc_diff.seconds % 3600) / 60
                        ddclsc = "%.2f" % (days + hour + minute / 60)

                    # 全程时长
                    if sjsj.strip() != "" and tdsj.strip() != "":
                        sjsj_obj1 = datetime.strptime(sjsj, '%Y-%m-%d %H:%M:%S')
                        tdsj_obj1 = datetime.strptime(tdsj, '%Y-%m-%d %H:%M:%S')
                        # 计算时间差
                        qcsc_diff = tdsj_obj1 - sjsj_obj1

                        # 将时间转换为小时和小数部分的分钟，并保留两位小数
                        days = qcsc_diff.days * 24
                        hour = qcsc_diff.seconds // 3600
                        minute = (qcsc_diff.seconds % 3600) / 60
                        qcsc = "%.2f" % (days + hour + minute / 60)

                    # 获取文本框中的文本
                    text = input_textbox.get("1.0", "end-1c")

                    # 将文本按行分割并去除空行
                    lines = text.splitlines()
                    lines = [line for line in lines if line.strip()]

                    #results = []
                    if len(lines) == 0:

                        record = {'邮件号': yjh, '始发省': sfs, '始发地': sfd, '终到省': zds, '目的地': mdd,
                                  '国内订单接入时间': ddjrsj,
                                  '收寄局': sjj, '收寄时间': sjsj, '重量': zl, '收寄单位处理时长': sjdqclsc,
                                  '发运单位': fydw,
                                  '发运单位发车时间': fydwfcsj, '运输时间': yssj, '到达广航时间': ddghsj,
                                  '解车交接对象': jcjjdx, '广航解车时间': ghjcsj,
                                  '外环供包时间': whgbsj, '内环供包时间': nhgbsj, '外环落格时间': whlgsj,
                                  '内环落格时间': nhlgsj, '外环齐格时间': whqgsj,
                                  '内环齐格时间': nhqgsj, '人工齐格人员': rgqgry, '人工齐格时间': rgqgsj,
                                  '矩阵粗分供包时间': jzcfgbsj, '矩阵粗分落格时间': jzcflgsj,
                                  '矩阵细分供包时间': jzxfgbsj,
                                  '矩阵细分落格时间': jzxflgsj,
                                  '广航封发时间': ghffsj, '广航配发时间': ghpfsj, '广航内部处理时长': ghnbclsc,
                                  '起飞时间': qfsj, '航班号': hbh, '航班邮路': hbyl, '封车时间': fcsj,
                                  '封车交接对象': fcjjdx, '车牌': cp, '落地时间': ldsj,
                                  '提取时长': tqsc, '中途运输时长': ztyssc, '对端接收时间': ddjssj,
                                  '对端处理时长': ddclsc, '投递机构': tdjg,
                                  '投递时间': tdsj, '全程时长': qcsc}
                        logistics_data.append(record)

                    else:
                        if hbh in lines:
                            record = {'邮件号': yjh, '始发省': sfs, '始发地': sfd, '终到省': zds, '目的地': mdd,
                                      '国内订单接入时间': ddjrsj,
                                      '收寄局': sjj, '收寄时间': sjsj, '重量': zl, '收寄单位处理时长': sjdqclsc,
                                      '发运单位': fydw,
                                      '发运单位发车时间': fydwfcsj, '运输时间': yssj, '到达广航时间': ddghsj,
                                      '解车交接对象': jcjjdx, '广航解车时间': ghjcsj,
                                      '外环供包时间': whgbsj, '内环供包时间': nhgbsj, '外环落格时间': whlgsj,
                                      '内环落格时间': nhlgsj, '外环齐格时间': whqgsj,
                                      '内环齐格时间': nhqgsj, '人工齐格人员': rgqgry, '人工齐格时间': rgqgsj,
                                      '矩阵粗分供包时间': jzcfgbsj, '矩阵粗分落格时间': jzcflgsj,
                                      '矩阵细分供包时间': jzxfgbsj,
                                      '矩阵细分落格时间': jzxflgsj,
                                      '广航封发时间': ghffsj, '广航配发时间': ghpfsj, '广航内部处理时长': ghnbclsc,
                                      '起飞时间': qfsj, '航班号': hbh, '航班邮路': hbyl, '封车时间': fcsj,
                                      '封车交接对象': fcjjdx, '车牌': cp, '落地时间': ldsj,
                                      '提取时长': tqsc, '中途运输时长': ztyssc, '对端接收时间': ddjssj,
                                      '对端处理时长': ddclsc, '投递机构': tdjg,
                                      '投递时间': tdsj, '全程时长': qcsc}
                            logistics_data.append(record)


                except json.JSONDecodeError:
                    pass



                # 将此邮件号查询结果添加到主物流数据列表中
                #logistics_data.append(dataOutput)

                # 打印响应的前200个字符
                #print(f"Response Text: {r}...")  # 只打印部分响应，防止输出过多
            except Exception as e:
                print(f"Error reading response: {e}")
        else:
            #print(f"忽略的请求：{response.url}")
            pass

    # 监听指定的响应
    page.on("response", handle_response)
    # 设置最大重试次数和重试间隔

    # 任务逻辑
    await load_and_query_page(page, mailno, parent, max_retries, retry_delay)
    L += 1
    parent.after(0, tool.update_L(str(L)))

def handle_input(title,parent):
    output_textbox.configure(state="normal")
    # 清空文本框内容
    output_textbox.delete("1.0", tk.END)  # 清空内容

    def run_async_task():
        # 创建事件循环
        asyncio.run(batch_query_and_export(title, parent))

    # 启动线程运行异步任务
    threading.Thread(target=run_async_task, daemon=True).start()



def getunifyReport():
    # print('开始获取邮件号')
    #t1 = time.perf_counter()
    start_date = str(start_date_entry.get_date())
    end_date = str(end_date_entry.get_date())
    # 获取选择的值
    selected_business = business_options[business_combobox.get()]
    selected_flow = flow_options[flow_combobox.get()]
    tool.process_input('开始获取邮件号')
    tool.process_input('开始日期:' + start_date)
    tool.process_input('结束日期:' + end_date)
    tool.process_input('业务种类:' + business_combobox.get())
    tool.process_input('流向:' + flow_combobox.get())
    requests.packages.urllib3.disable_warnings()
    url = 'http://*************/gdsdjygkxt/unifyReport/unifyReportAction!queryReportData.action'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded',
        'Host': '*************',
        'Origin': 'http://*************',
        'Referer': 'http://*************/gdsdjygkxt/',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'
        # 'Cookie': session
    }
    numerlist=[]
    # 获取当前时间戳（以毫秒为单位）
    timestamp_ms = int(time.time() * 1000)
    # print(timestamp_ms)
    data = {
        'report': 'cent_job_sum730',
        'reportId': '98384',
        'reportTitle': '中心局计划执行率',
        'procudureName': '{"N_PROD_ID":98384,"V_PROD_NAME":"pkg_jdk_ckd_jhzxl.p_cent_export_sum","V_PROC_TYPE":40}',
        'pageSize': '50',
        'pageNo': '1',
        'editFlag': '0',
        'pageFlag': '',
        'tigTime': str(timestamp_ms),
        'isLink': 'false',
        'linkParams': '',
        'beginDate': start_date,
        'endDate': end_date,
        'V_TXT_FILE_TYPE': selected_business,#2
        'V_CENT_ORG': '4',
        'V_TJWD': '2',
        'V_LX': selected_flow,#3
        'V_JC_JJ': '1',
        'V_CHNEL': '0'
    }
    # response = requests.post(url, headers=headers,params=data, verify=False,proxies=proxies,allow_redirects=False)
    response = session.get(url, headers=headers, params=data, verify=False)
    r = response.text
    #print(r)
    html = BeautifulSoup(r, 'lxml')
    # 查找<tbody>标签
    tbody_tag = html.find('tbody')

    # 查找倒数第三个<a>标签
    a_tags = tbody_tag.find_all('a')
    third_last_a_tag = a_tags[-3]
    # 匹配onclick属性中的内容
    pattern = r"exportLink\('([^']*)'\)"
    match = re.search(pattern, str(third_last_a_tag))

    # 提取匹配到的内容
    if match:
        onclick_content = match.group(1)
    else:
        onclick_content = None

    # 输出结果
    # print('共'+a_tags[-3].text+'件')
    tool.process_input('共' + a_tags[-3].text + '件')
    # print('开始下载Excel文件')
    tool.process_input('开始下载Excel文件,请稍等')
    j = 0
    if int(a_tags[-3].text)<80000:
        numerlist=export(onclick_content,str(timestamp_ms))
    else:
        trs=tbody_tag.find_all('tr')
        for tr in trs[:-1]:
            i=2

            # 查找倒数第三个<a>标签
            tags = tr.find_all('a')
            third_last_a_tag = tags[-3]
            #print(third_last_a_tag)

            # 匹配onclick属性中的内容
            pattern = r"exportLink\('([^']*)'\)"
            match = re.search(pattern, str(third_last_a_tag))
            # 提取匹配到的内容
            if match:
                onclick_content = match.group(1)
            else:
                onclick_content = None
            jdds= tr.find('th').text

            tool.process_input(jdds+'共' + tags[-3].text + '件')

            numeric_list=export(onclick_content,str(timestamp_ms))

            numerlist += numeric_list
            j += 1
    return numerlist

def export(linkParams,timestamp_ms):
    start_date = str(start_date_entry.get_date())
    end_date = str(end_date_entry.get_date())
    # 获取选择的值
    selected_business = business_options[business_combobox.get()]
    selected_flow = flow_options[flow_combobox.get()]

    # 发送POST请求并下载Excel文件
    url = "http://*************/gdsdjygkxt/unifyReport/unifyReportAction!exportReport.action?isLink=true"
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded',
        'Host': '*************',
        'Origin': 'http://*************',
        'Referer': 'http://*************/gdsdjygkxt/',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36'

    }
    data = {
        'report': 'cent_job_sum730',
        'reportId': '98384',
        'reportTitle': '中心局计划执行率',
        'procudureName': '{"N_PROD_ID":98384,"V_PROD_NAME":"pkg_jdk_ckd_jhzxl.p_cent_export_sum","V_PROC_TYPE":40}',
        'pageSize': '50',
        'pageNo': '1',
        'editFlag': '0',
        'pageFlag': '',
        'tigTime': timestamp_ms,
        'isLink': 'false',
        'linkParams': linkParams,
        'beginDate': start_date,
        'endDate': end_date,
        'V_TXT_FILE_TYPE': selected_business,  # 2
        'V_CENT_ORG': '4',
        'V_TJWD': '2',
        'V_LX': selected_flow,  # 3
        'V_JC_JJ': '1',
        'V_CHNEL': '0'
    }
    response = session.post(url, headers=headers, data=data)
    # print(response.content)

    # 将Excel保存到本地
    with open("data.xlsx", "wb") as f:
        f.write(response.content)
        # print('下载完毕')

    # 在延时之后进行验证或处理
    while os.path.isfile("data.xlsx") and os.path.getsize("data.xlsx") < 1:
        # print("Excel 文件获取成功")
        response = session.post(url, headers=headers, data=data)
        time.sleep(5)  # 等待 5 秒钟
        # 将Excel保存到本地
        with open("data.xlsx", "wb") as f:
            f.write(response.content)
        # process_input('Excel 文件获取失败')
    tool.process_input('Excel 文件获取成功')
    tool.process_input('开始解析Excel文件,请稍等')
    # 忽略 openpyxl 库的警告
    warnings.simplefilter("ignore", category=UserWarning)

    # 读取 Excel 文件
    filename = "data.xlsx"
    df = pd.read_excel(filename)

    # 提取第一列从第四行开始单元格值为纯数字的行，并转换为纯数字字符串列表
    numeric_list = df.iloc[3:, 0].str.strip().str.extract(r'^(\d+)$').dropna()[0].tolist()

    # 打印纯数字字符串列表
    tool.process_input('获取邮件号成功')
    os.remove("data.xlsx")
    return numeric_list


def getmailtrace(parent,mailno):
    global L
    # print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

         'trace_no': mailno,
        'numType': 15,
        'limit': 20
    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text

    jsonObj = json.loads(r)

    yjh = mailno
    sfs = ""
    sfd = ""
    zds = ""
    mdd = ""
    ddjrsj=""
    sjj = ""
    sjsj = ""
    sjdqclsc = ""
    sjjjsj = ""
    fydw = ""
    fydwfcsj = ""
    yssj = ""
    ddghsj = ""
    jcjjdx= ""
    ghjcsj=""
    ghffsj = ""
    ghpfsj = ""
    ghnbclsc = ""
    qfsj = ""
    hbh = ""
    hbyl = ""
    ldsj = ""
    tqsc = ""
    ddjssj = ""
    ddclsc = ""
    tdsj = ""
    tdjg =""
    qcsc = ""
    fcsj=""
    fcjjdx=""
    cp=""
    ztyssc=""
    whgbsj=""
    nhgbsj = ""
    whlgsj=""
    nhlgsj = ""
    whqgsj=""
    nhqgsj = ""
    rgqgry=""
    rgqgsj=""
    jzcfgbsj=""
    jzcflgsj=""
    jzxfgbsj=""
    jzxflgsj=""
    zl=''
    # 通过循环来获取JSON中的数据，并添加到字典中
    for line in jsonObj:
        if "opName" in line:
            if '收寄计费信息' in line["opName"]:
                #yjh = line["traceNo"]
                sfs = line.get('opOrgProvName', '')
                sfd = line.get('opOrgCity', '')
                if "receivePlace" in line:
                    zdss = re.findall(r'(?:上海市|北京市|天津市|重庆市|[\u4e00-\u9fa5]+(?:省|自治区))',
                                     str(line["receivePlace"]))
                # mdd=re.findall(r'([\u4e00-\u9fa5]+市)', str(line["receivePlace"]))
                    mdds = re.findall(r'(?:上海市|北京市|天津市|重庆市)', str(line["receivePlace"]))
                    if zdss:
                        zds = zdss[0]

                    if mdds:
                        mdd = mdds[0]
                    else:
                        mdd = line["receivePlace"].replace(zds, '')
                sjj = line.get('opOrgSimpleName', '')
                sjsj = line["opTime"]
                zls = re.findall(r'重量:(.*),标', str(line["internalDesc"]))
                if zls:
                    zl = zls[0]
            # if '收寄交接' in line["opName"]:
            #     sjjjsj = line["opTime"]
            if '国内订单接入' in line["opName"]:
                ddjrsj = line["opTime"]
            if '封车' in line["opName"] and 'originalDesc' in line and '下一站:广州航空中心' in line[
                "originalDesc"] and "opOrgSimpleName" in line and '广州航空中心' not in line["opOrgSimpleName"]:
                fydwfcsj = line["opTime"]
                fydw = line["opOrgSimpleName"]
            if '处理中心车辆进局' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                ddghsj = line["opTime"]
            if '处理中心解车' in line["opName"] and '51000061' in line["opOrgCode"]:
                ghjcsj = line["opTime"]
                jcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                if jcjjdx:
                    jcjjdx = jcjjdx[0]
                #print(jcjjdx)
            if '处理中心分拣机供包扫描' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"] :
                if '5100006101243' in line.get("operatorNo", ""):
                    whgbsj = line["opTime"]
                if '5100006101244' in line.get("operatorNo", ""):
                    nhgbsj = line["opTime"]
                if '5100006101241' in line.get("operatorNo", ""):
                    jzcfgbsj = line["opTime"]
                if '5100006101242' in line.get("operatorNo", ""):
                    jzxfgbsj = line["opTime"]
            if '分拣机落格' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                if '单层小件交叉带分拣机1' in line.get("originalDesc", ""):
                    whlgsj = line["opTime"]
                if '单层小件交叉带分拣机2' in line.get("originalDesc", ""):
                    nhlgsj = line["opTime"]
                if '摆轮矩阵分拣系统(粗分)' in line.get("originalDesc", ""):
                    jzcflgsj = line["opTime"]
                if '摆轮矩阵分拣系统(细分)' in line.get("originalDesc", ""):
                    jzxflgsj = line["opTime"]
            if '处理中心包分机自动齐格' in  line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                if '5100006101243' in line.get("operatorNo", ""):
                    whqgsj = line["opTime"]
                elif '5100006101244' in line.get("operatorNo", ""):
                    nhqgsj = line["opTime"]
                else:
                    rgqgry = line["operatorName"]+line["operatorNo"]
                    rgqgsj = line["opTime"]
            if '处理中心扫描配发' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                ghpfsj = line["opTime"]
            if '航班起飞' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                qfsj = line["opTime"]
                hbh = re.findall(r'航班号:(\w+)', str(line["originalDesc"]))
                if hbh:
                    hbh = hbh[0]
                else:
                    hbh=''
                hbyl = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                if hbyl:
                    hbyl = hbyl[0]
                else:
                    hbyl=''
            if '处理中心封车' in line["opName"] and '51000061' in line["opOrgCode"]:
                fcsj = line["opTime"]
                cp = re.findall(r'车牌:(\w+)', str(line["originalDesc"]))
                if cp:
                    cp = cp[0]
                else:
                    cp=''
                fcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
                if fcjjdx:
                    fcjjdx = fcjjdx[0]
                else:
                    fcjjdx=''
            if '航班降落' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' not in line["opOrgSimpleName"] and hbh in line["originalDesc"]:
                ldsj = line["opTime"]
            if '处理中心解车' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' not in line["opOrgSimpleName"] and 'lastOrgName' in line and '广州航空中心' in line["lastOrgName"]:
                ddjssj = line["opTime"]
            if '投递结果反馈-妥投' in line["opName"]:
                tdsj = line["opTime"]
                tdjg = line["opOrgSimpleName"]
    if not qfsj:
        for line in jsonObj:
            if '处理中心封车' in line["opName"] and "opOrgSimpleName" in line and '广州航空中心' in line["opOrgSimpleName"]:
                qfsj = line["opTime"]
     # 收寄单位处理时长
    # 将日期字符串转换为datetime对象
    if fydwfcsj.strip() != "" and sjsj.strip() != "":
        fydwfcsj_obj1 = datetime.datetime.strptime(fydwfcsj, '%Y-%m-%d %H:%M:%S')
        sjsj_obj2 = datetime.datetime.strptime(sjsj, '%Y-%m-%d %H:%M:%S')

        # 计算时间差
        sjdqclsc_diff = fydwfcsj_obj1 - sjsj_obj2

        # 计算天数、小时数、分钟数和秒数
        days = sjdqclsc_diff.days*24
        # hours = sjdqclsc_diff.seconds // 3600
        # minutes = (sjdqclsc_diff.seconds % 3600) // 60
        # seconds = (sjdqclsc_diff.seconds % 3600) % 60
        #
        # # 构建时间差字符串
        # sjdqclsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        hour = sjdqclsc_diff.seconds // 3600
        minute = (sjdqclsc_diff.seconds % 3600) / 60
        sjdqclsc = "%.2f" % (days+hour + minute/60)

        # 运输时间
    # 将日期字符串转换为datetime对象
    if ddghsj.strip() != "" and fydwfcsj.strip() != "":
        ddghsj_obj1 = datetime.datetime.strptime(ddghsj, '%Y-%m-%d %H:%M:%S')
        fydwfcsj_obj2 = datetime.datetime.strptime(fydwfcsj, '%Y-%m-%d %H:%M:%S')

        # 计算时间差
        yssj_diff = ddghsj_obj1 - fydwfcsj_obj2

        # 计算天数、小时数、分钟数和秒数
        days = yssj_diff.days*24
        # hours = yssj_diff.seconds // 3600
        # minutes = (yssj_diff.seconds % 3600) // 60
        # seconds = (yssj_diff.seconds % 3600) % 60
        #
        # # 构建时间差字符串
        # yssj = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        hour = yssj_diff.seconds // 3600
        minute = (yssj_diff.seconds% 3600) / 60
        yssj = "%.2f" % (days+hour + minute/60)

    # 内部处理时长
    if qfsj.strip() != "" and ddghsj.strip() != "":
        sorter = sorter_var.get()
        if sorter == "发航":
            qfsj_obj1 = datetime.datetime.strptime(qfsj, '%Y-%m-%d %H:%M:%S')
            ddghsj_obj1 = datetime.datetime.strptime(ddghsj, '%Y-%m-%d %H:%M:%S')

            # 计算时间差
            ghnbclsc_diff = qfsj_obj1 - ddghsj_obj1

            # 计算天数、小时数、分钟数和秒数
            days = ghnbclsc_diff.days*24
            # hours = ghnbclsc_diff.seconds // 3600
            # minutes = (ghnbclsc_diff.seconds % 3600) // 60
            # seconds = (ghnbclsc_diff.seconds % 3600) % 60
            #
            # # 构建时间差字符串
            # ghnbclsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
            hour = ghnbclsc_diff.seconds // 3600
            minute = (ghnbclsc_diff.seconds % 3600) / 60
            ghnbclsc = "%.2f" % (days+hour + minute/60)
        else:
            fcsj_obj1 = datetime.datetime.strptime(fcsj, '%Y-%m-%d %H:%M:%S')
            ghjcsj_obj1 = datetime.datetime.strptime(ghjcsj, '%Y-%m-%d %H:%M:%S')

            # 计算时间差
            ghnbclsc_diff = fcsj_obj1 - ghjcsj_obj1

            # 计算天数、小时数、分钟数和秒数
            days = ghnbclsc_diff.days * 24
            # hours = ghnbclsc_diff.seconds // 3600
            # minutes = (ghnbclsc_diff.seconds % 3600) // 60
            # seconds = (ghnbclsc_diff.seconds % 3600) % 60
            #
            # # 构建时间差字符串
            # ghnbclsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
            hour = ghnbclsc_diff.seconds // 3600
            minute = (ghnbclsc_diff.seconds % 3600) / 60
            ghnbclsc = "%.2f" % (days + hour + minute / 60)
    # 提取时长
    if ddjssj.strip() != "" and ldsj.strip() != "":
        ddjssj_obj1 = datetime.datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
        ldsj_obj1 = datetime.datetime.strptime(ldsj, '%Y-%m-%d %H:%M:%S')

        # 计算时间差
        tqsc_diff = ddjssj_obj1 - ldsj_obj1

        # 计算天数、小时数、分钟数和秒数
        days = tqsc_diff.days*24
        # hours = tqsc_diff.seconds // 3600
        # minutes = (tqsc_diff.seconds % 3600) // 60
        # seconds = (tqsc_diff.seconds % 3600) % 60
        #
        # # 构建时间差字符串
        # tqsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        hour = tqsc_diff.seconds // 3600
        minute = (tqsc_diff.seconds % 3600) / 60
        tqsc = "%.2f" % (days+hour + minute/60)

    # 中途运输时长
    if ddjssj.strip() != "" and fcsj.strip() != "":
        ddjssj_obj1 = datetime.datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
        fcsj_obj1 = datetime.datetime.strptime(fcsj, '%Y-%m-%d %H:%M:%S')

        # 计算时间差
        ztyssc_diff = ddjssj_obj1 - fcsj_obj1

        # 计算天数、小时数、分钟数和秒数
        days = ztyssc_diff.days * 24
        # hours = tqsc_diff.seconds // 3600
        # minutes = (tqsc_diff.seconds % 3600) // 60
        # seconds = (tqsc_diff.seconds % 3600) % 60
        #
        # # 构建时间差字符串
        # tqsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        hour = ztyssc_diff.seconds // 3600
        minute = (ztyssc_diff.seconds % 3600) / 60
        ztyssc = "%.2f" % (days + hour + minute / 60)

    # 对端处理时长
    if tdsj.strip() != "" and ddjssj.strip() != "":
        tdsj_obj1 = datetime.datetime.strptime(tdsj, '%Y-%m-%d %H:%M:%S')
        ddjssj_obj1 = datetime.datetime.strptime(ddjssj, '%Y-%m-%d %H:%M:%S')
        # 计算时间差
        ddclsc_diff = tdsj_obj1 - ddjssj_obj1

        # 计算天数、小时数、分钟数和秒数
        days = ddclsc_diff.days*24
        # hours = ddclsc_diff.seconds // 3600
        # minutes = (ddclsc_diff.seconds % 3600) // 60
        # seconds = (ddclsc_diff.seconds % 3600) % 60
        #
        # # 构建时间差字符串
        # ddclsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        hour = ddclsc_diff.seconds // 3600
        minute = (ddclsc_diff.seconds % 3600) / 60
        ddclsc = "%.2f" % (days+hour + minute/60)

    # 全程时长
    if sjsj.strip() != "" and tdsj.strip() != "":
        sjsj_obj1 = datetime.datetime.strptime(sjsj, '%Y-%m-%d %H:%M:%S')
        tdsj_obj1 = datetime.datetime.strptime(tdsj, '%Y-%m-%d %H:%M:%S')
        # 计算时间差
        qcsc_diff = tdsj_obj1 - sjsj_obj1

        # # 计算天数、小时数、分钟数和秒数
        # days = qcsc_diff.days
        # hours = qcsc_diff.seconds // 3600
        # minutes = (qcsc_diff.seconds % 3600) // 60
        # seconds = (qcsc_diff.seconds % 3600) % 60
        # 构建时间差字符串
        #qcsc = "{}天{}小时{}分钟{}秒".format(days, hours, minutes, seconds)
        # 将时间转换为小时和小数部分的分钟，并保留两位小数
        days = qcsc_diff.days*24
        hour = qcsc_diff.seconds // 3600
        minute = (qcsc_diff.seconds % 3600) / 60
        qcsc = "%.2f" % (days+hour + minute/60)


    # 获取文本框中的文本
    text = input_textbox.get("1.0", "end-1c")

    # 将文本按行分割并去除空行
    lines = text.splitlines()
    lines = [line for line in lines if line.strip()]

    results = []
    if len(lines) == 0:

        record = {'邮件号': yjh, '始发省': sfs, '始发地': sfd, '终到省': zds, '目的地': mdd,'国内订单接入时间':ddjrsj,
                  '收寄局': sjj, '收寄时间': sjsj,'重量':zl, '收寄单位处理时长': sjdqclsc, '发运单位': fydw,
                  '发运单位发车时间': fydwfcsj, '运输时间': yssj, '到达广航时间': ddghsj,'解车交接对象':jcjjdx,'广航解车时间':ghjcsj,
                  '外环供包时间':whgbsj,'内环供包时间':nhgbsj,'外环落格时间':whlgsj,'内环落格时间':nhlgsj,'外环齐格时间':whqgsj,
                  '内环齐格时间':nhqgsj, '人工齐格人员':rgqgry,'人工齐格时间':rgqgsj,'矩阵粗分供包时间': jzcfgbsj, '矩阵粗分落格时间': jzcflgsj, '矩阵细分供包时间':jzxfgbsj,
                  '矩阵细分落格时间':jzxflgsj,
                  '广航封发时间': ghffsj, '广航配发时间': ghpfsj, '广航内部处理时长': ghnbclsc,
                  '起飞时间': qfsj, '航班号': hbh, '航班邮路': hbyl, '封车时间':fcsj,'封车交接对象':fcjjdx,'车牌':cp,'落地时间': ldsj,
                  '提取时长': tqsc, '中途运输时长':ztyssc,'对端接收时间': ddjssj, '对端处理时长': ddclsc,'投递机构':tdjg,
                  '投递时间': tdsj, '全程时长': qcsc}
        results.append(record)

    else:
        if hbh in lines:
            record = {'邮件号': yjh, '始发省': sfs, '始发地': sfd, '终到省': zds, '目的地': mdd,'国内订单接入时间':ddjrsj,
                      '收寄局': sjj, '收寄时间': sjsj, '重量':zl,'收寄单位处理时长': sjdqclsc, '发运单位': fydw,
                      '发运单位发车时间': fydwfcsj, '运输时间': yssj, '到达广航时间': ddghsj,'解车交接对象':jcjjdx,'广航解车时间':ghjcsj,
                      '外环供包时间':whgbsj,'内环供包时间':nhgbsj,'外环落格时间':whlgsj,'内环落格时间':nhlgsj,'外环齐格时间':whqgsj,
                      '内环齐格时间':nhqgsj,'人工齐格人员':rgqgry,'人工齐格时间':rgqgsj, '矩阵粗分供包时间': jzcfgbsj, '矩阵粗分落格时间': jzcflgsj, '矩阵细分供包时间':jzxfgbsj,
                      '矩阵细分落格时间':jzxflgsj,
                      '广航封发时间': ghffsj, '广航配发时间': ghpfsj, '广航内部处理时长': ghnbclsc,
                      '起飞时间': qfsj, '航班号': hbh, '航班邮路': hbyl, '封车时间':fcsj,'封车交接对象':fcjjdx,'车牌':cp,'落地时间': ldsj,
                      '提取时长': tqsc, '中途运输时长':ztyssc,'对端接收时间': ddjssj, '对端处理时长': ddclsc,'投递机构':tdjg,
                      '投递时间': tdsj, '全程时长': qcsc}
            results.append(record)
    L += 1
    if L % 1000 == 0:

        # 在主线程中调度更新UI
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))

    # 循环结束后释放session资源
    session.close()
    return results
    # print(dataOutput)


# 把下载完成的图片灰度化处理一下 提高识别准确率

def image_er():
    image = Image.open('captcha.jpg').convert('L')
    table = []
    for i in range(256):
        if i < 70:
            table.append(0)
        else:
            table.append(1)
    image.point(table, '1').save('code.jpg')


def image_yyer():
    image = Image.open('Kaptcha.jpg').convert('L')
    table = []
    for i in range(256):
        if i < 70:
            table.append(0)
        else:
            table.append(1)
    image.point(table, '1').save('code.jpg')


def handle_input(title,parent):
    output_textbox.configure(state="normal")
    # 清空文本框内容
    output_textbox.delete("1.0", tk.END)  # 清空内容

    def run_async_task():
        # 创建事件循环
        asyncio.run(batch_query_and_export(title, parent))

    # 启动线程运行异步任务
    threading.Thread(target=run_async_task, daemon=True).start()



def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")
    inputmailno_textbox.delete("1.0", "end")



def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标
    # # 如果本地图标文件不存在则下载并设置窗口图标
    # if not os.path.exists(local_icon_path):
    #     if download_icon(web_icon_url, local_icon_path):
    #         set_window_icon(local_icon_path)
    #     else:
    #         print("无法下载图标")
    # else:
    #     set_window_icon(local_icon_path)
    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L, input_textbox, output_textbox,  \
        submit_button,button_clear, start_date_entry, end_date_entry, account_entry, password_entry,business_combobox,\
        flow_combobox,business_options,flow_options,threads_combobox,merged_data,inputmailno_textbox,sorter_var,tool,back_button,organization_combobox

    # 构造Session
    session = requests.Session()
    # 创建主窗口
    #root = tk.Tk()

    today = datetime.today()
    yesterday = today - timedelta(days=1)

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    date_container = ttk.Frame(frame)
    date_container.grid(row=1, column=0)

    # 添加开始日期组件
    start_date_label = ttk.Label(date_container, text="开始日期:")
    start_date_label.grid(row=0, column=0, padx=10, pady=10)
    # start_date_label.pack()
    start_date_entry = DateEntry(date_container, maxdate=yesterday.date())
    start_date_entry.grid(row=0, column=1, padx=10, pady=10)
    # start_date_entry.pack()

    # 添加结束日期组件
    end_date_label = ttk.Label(date_container, text="结束日期:")
    end_date_label.grid(row=0, column=2, padx=10, pady=10)
    # end_date_label.pack()
    end_date_entry = DateEntry(date_container, maxdate=yesterday.date())
    end_date_entry.grid(row=0, column=3, padx=10, pady=10)
    # end_date_entry.pack()



    # 添加业务类别下拉框
    business_label = ttk.Label(date_container, text="业务类别:")
    business_label.grid(row=1, column=0, padx=10, pady=10)

    business_options = {
        "全部": "0",
        "标准快递": "1",
        "快递包裹": "2"
    }
    business_combobox = ttk.Combobox(date_container, values=list(business_options.keys()))
    business_combobox.grid(row=1, column=1, padx=10, pady=10)

    # 添加流向下拉框
    flow_label = ttk.Label(date_container, text="流向:")
    flow_label.grid(row=1, column=2, padx=10, pady=10)

    flow_options = {
        "省内互寄": "1",
        "省际进口": "2",
        "省际出口": "3"
    }
    flow_combobox = ttk.Combobox(date_container, values=list(flow_options.keys()))
    flow_combobox.grid(row=1, column=3, padx=10, pady=10)

    sorter_var = tk.StringVar(value="发航")  # 默认选择内环分拣机
    inner_sorter_radio = tk.Radiobutton(date_container, text="发航", variable=sorter_var, value="发航")
    inner_sorter_radio.grid(row=2, column=1, padx=10, pady=10)

    outer_sorter_radio = tk.Radiobutton(date_container, text="陆运", variable=sorter_var, value="陆运")
    outer_sorter_radio.grid(row=2, column=2, padx=10, pady=10)




    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=2, column=0)

    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="筛选航班号:")
    input_label.grid(row=0, column=0, padx=10, pady=10)

    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=0, column=1, padx=10, pady=10, columnspan=1)

    inputmailno_label = ttk.Label(input_label_container, text="邮件号:")
    inputmailno_label.grid(row=0, column=2, padx=10, pady=10)

    inputmailno_textbox = tk.Text(input_label_container, height=5, width=30)
    inputmailno_textbox.grid(row=0, column=3, padx=10, pady=10, columnspan=1)


    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title,func_window))
    submit_button.grid(row=1, column=1, padx=10, pady=10)
    # submit_button.pack()

    # 创建停止按钮
    # stop_button = ttk.Button(input_label_container, text="停止运行", command=stop_program)
    # stop_button.grid(row=1, column=2, padx=10, pady=10)

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=1, column=2, padx=10, pady=10)
    # button_clear.pack()



    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [func_window.destroy(), parent.deiconify()])
    back_button.pack()
