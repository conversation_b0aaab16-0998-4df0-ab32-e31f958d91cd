import io
import json
import random
import tkinter as tk
import traceback
from tkinter import scrolledtext
from tkinter import ttk
from tkinter import *
import requests
import threading
import socket

# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

def run():
    try:
        global  session
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")

        # 并发执行并获取结果
        if '指定' in selected.get():
            appoint()
        elif '随机' in selected.get():
            randoms()
        elif '全部' in selected.get():
            all()


        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        #back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        #back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        process_input(error_message)

        # 删除excel文件
        #os.remove('data.xlsx')

def process_input(text):
    # 在输出文本框中显示结果

    output_textbox.configure(state="normal")  # 设置文本框为可编辑状态
    output_textbox.insert(tk.END, text + '\n')  # 在文本框末尾插入新内容
    output_textbox.see(tk.END)  # 滚动到文本框末尾
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    root.update()  # 更新界面，确保文本框能及时显示新内容


def handle_input():
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run)
    t.start()


def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")


# 定义请求函数
def send_request(posi_no,state):
    url = f'http://************/storepositions/iowrite/{posi_no}/{state}'
    headers = {
        'accept': 'application/json',
        'Authorization': 'Bearer ' + root.token_var.get()
    }
    response = session.post(url, headers=headers)
    if state=='0':
        state='不亮灯'
    elif state=='1' :
        state = '黄灯'
    elif state == '2':
        state = '绿灯'
    elif state == '3':
        state = '红灯'
    # 处理响应
    if response.status_code == 200:
        print(response.json())
        process_input(posi_no+'设置为'+state)
        #return f"Request for {posi_no} successful."
    else:
        print(response.json())
        process_input(response.json())

        #return f"Request for {posi_no} failed with status code {response.status_code}."

def appoint():
    #global access_token

    posi_no = organization_combobox.get()
    # 获取选择的值
    state=business_options[threads_combobox.get()]
    if posi_no:
        send_request(posi_no,state)
    else:
        process_input('请选择货架!!!')

def randoms():
    #global access_token

    # 进行十次随机选择
    for _ in range(10):
        # 随机选择 organization_combobox 的值
        selected_organization = random.choice(all_posi_nos)

        # 随机选择 threads_combobox 的值，并获取对应的状态
        selected_business_option = random.choice(list(business_options.keys()))
        selected_state = business_options[selected_business_option]
        send_request(selected_organization,selected_state)

def all():
    #global access_token

    # 获取选择的值
    state = business_options[threads_combobox.get()]
    # 遍历 all_posi_nos 列表，对每个 posi_no 发送 HTTP 请求
    for posi_no in all_posi_nos:
        send_request(posi_no,state)
    # # 使用 ThreadPoolExecutor 实现并发请求
    # with ThreadPoolExecutor(max_workers=20) as executor:
    #     # 提交任务，每个任务对应一个 posi_no
    #     futures = [executor.submit(send_request, posi_no, state) for posi_no in all_posi_nos]
    #
    #     # 获取任务结果
    #     for future in futures:
    #         result = future.result()
    #         print(result)

def login():
    url = 'http://************/auth/jwt/login'
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
    }

    data = {
        'username': '<EMAIL>',
        'password': 'admin123',
        'scope': '',
        'client_id': '',
        'client_secret': '',
    }

    response = session.post(url, headers=headers, data=data)

    # 处理响应
    if response.status_code == 200:
        print(response.json())
        process_input( '登录成功'  )
        # return f"Request for {posi_no} successful."
    else:
        print(response.json())
        process_input(response.json())
    # 将 JSON 数据解析为 Python 对象
    data = json.loads(response.text)
    # print(response.status_code)
    # print(response.json())
    # print(data['access_token'])
    root.token_var.set(data['access_token'])
    #return data['access_token']

def getshelf():
    url = 'http://************/shelfs/?skip=0&limit=50'
    headers = {
        'accept': 'application/json'
    }

    response = session.get(url, headers=headers)
    # 将 JSON 数据解析为 Python 对象
    data = json.loads(response.text)

    # 获取所有 "posi_no" 的值
    all_posi_nos = [posi["posi_no"] for item in data for posi in item["store_position"]]

    print(response.status_code)
    print(response.json())
    return all_posi_nos

def main():
    global username, password, session, jdptid, L, input_textbox, output_textbox, root, \
        submit_button, button_clear, start_date_entry, end_date_entry, account_entry, password_entry, threads_combobox, \
        business_label, business_combobox, business_options, selected, jlyy_textbox, tool, back_button, organization_combobox,access_token,all_posi_nos
    # 创建主窗口
    root = tk.Tk()

    # 设置图标
    # icon_path = 'chinapost.ico'
    # root.iconbitmap(icon_path)
    # 设置新的标题
    root.title("货架亮灯 Power by LHX ")
    # 获取屏幕的宽度和高度
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 设置窗口的宽度为屏幕宽度的一半，高度为屏幕高度的一半
    window_width = int(screen_width / 2)
    window_height = int(screen_height / 2)
    window_geometry = f"{window_width}x{window_height}"
    root.geometry(window_geometry)

    root.token_var = tk.StringVar()

    # 构造Session
    session = requests.Session()

    # 获取本机主机名
    hostname = socket.gethostname()

    # 获取本机 IP 地址
    ip_address = socket.gethostbyname(hostname)

    print("本机主机名:", hostname)
    print("本机 IP 地址:", ip_address)
    if '************' == ip_address:
        session.proxies = {'http': "http://************:9999",
                           'https': "http://************:9999"}


    # 创建子容器
    frame = tk.Frame(root)
    frame.pack(padx=0, pady=0)


    # 创建代理功能勾选框的变量
    #proxy_enabled = tk.BooleanVar()

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="货架号:")
    organization_label.grid(row=0, column=0, padx=5)
    all_posi_nos=getshelf()
    organization_combobox = ttk.Combobox(account_container, values=all_posi_nos)
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    thread_label = tk.Label(account_container, text="存储位置状态:")
    thread_label.grid(row=0, column=3, padx=5)

    business_options = {
        "不亮灯": "0",
        "黄灯": "1",
        "绿灯": "2",
        "红灯": "3"
    }
    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=list(business_options.keys()))
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项

    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # 创建单选按钮
    selected = tk.StringVar(value='指定')
    radio_label = ttk.Label(input_label_container, text="功能选择:")
    radio_label.grid(row=1, column=0, padx=5, pady=10)
    radio_button1 = ttk.Radiobutton(input_label_container, text="指定", value="指定", variable=selected)
    radio_button1.grid(row=1, column=1, padx=5, pady=10)

    radio_button2 = ttk.Radiobutton(input_label_container, text="随机", value="随机", variable=selected)
    radio_button2.grid(row=1, column=2, padx=5, pady=10)

    radio_button3 = ttk.Radiobutton(input_label_container, text="全部", value="全部", variable=selected)
    radio_button3.grid(row=1, column=3, padx=5, pady=10)



    # # 添加多行输入框
    # input_label = ttk.Label(input_label_container, text="邮件号:")
    # input_label.grid(row=2, column=1, padx=10, pady=10)
    # # input_label.pack()
    # input_textbox = tk.Text(input_label_container, height=5, width=30)
    # input_textbox.grid(row=2, column=2, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 创建登录按钮
    button_clear = ttk.Button(input_label_container, text="登录", command=login)
    button_clear.grid(row=3, column=1, padx=10, pady=10)

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="设置", command=handle_input)
    submit_button.grid(row=3, column=2, padx=10, pady=10)
    # submit_button.pack()


    # # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)


    root.protocol("WM_DELETE_WINDOW", root.quit)  # 添加此行代码以侦听窗口关闭事件
    #access_token = login()
    # 运行界面主循环
    root.mainloop()
if __name__ == '__main__':
    main()