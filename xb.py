import os
import sys

import requests,  time

import traceback
import cx_Oracle as cx
import datetime

from multiprocessing.dummy import Pool as ThreadPool

# 获取打包后的可执行文件路径
executable_path = sys.argv[0]

# 从可执行文件路径中提取出真正的工作目录
current_dir = os.path.dirname(os.path.abspath(executable_path))

# 设置 Oracle 客户端路径
os.environ['PATH'] = os.path.join(current_dir, 'instantclient_12_1')
def getmailmg(id, mailno, sjrdz):
    cursor = con.cursor()
    # cursor2 = con2.cursor()
    mailno_first_digit = mailno[0]
    time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    var_A_values = ['A', 'B']
    if mailno_first_digit == '1':
        var_7_values = ['7']
    elif mailno_first_digit in ['8', '9']:
        var_7_values = ['8']
    else:
        raise ValueError(f"Invalid mailno first digit: {mailno_first_digit}")
    requests.packages.urllib3.disable_warnings()
    url = 'http://************/gdmzppService'
    headers = {

        'Content-type': 'application/x-www-form-urlencoded;charset=UTF-8'

    }
    for var_A in var_A_values:
        for var_7 in var_7_values:
            data = "msgBody=" + "{\"value\":\"" + mailno + "||" + var_A + "||" + var_7 + "||" + sjrdz.replace('\\N',
                                                                                      '') + "||*||" + time + "||*||*||*||*||*||*||*\"}"

            # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
            response = session.post(url, headers=headers, data=data.encode('utf-8'), verify=False)
            r = response.text
            # print(r)
            # jgdm=r.split("||")[1]
            # 添加额外的键值对
            extra_keys = ["邮速属性", "业务种类"]
            extra_values = ["邮政" if var_A == 'A' else "速递",
                            "标快" if var_7 == '7' else "快包"]
            # 将额外的键值对插入到数据字典的最前面
            data_dict = dict(zip(extra_keys, extra_values))
            data_dict.update(data_dict)
            keys = [
                "邮件条码",
                "投递局代码",
                "投递段代码",
                "投递段序代码",
                "匹配明细",
                "命中地址",
                "备注信息",
                "匹配类型",
                "匹配结果标志",
                "寄达省全称",
                "寄达市全称",
                "寄达区/县全称",
                "备注",
                "返回时间戳"
            ]

            values = r.split("||")
            # 去除第一个value中{"data":"的开头
            values[0] = values[0][len('{"data":"'):]
            data_dict.update(zip(keys, values))

            output_string = ", ".join([f"{key}: {value}" for key, value in data_dict.items()])
            print(output_string)
    # split_result = r.split("||")
    # if len(split_result) >= 2:
    #     jgdm = split_result[1]
    #     cursor.execute("update TB_FJ_YJXXB set sd_xbsj_y =sysdate,sd_bkjgdm = '" + str(jgdm) + "' where yjtm='" + str(
    #         mailno) + "'")
    #     con.commit()
    # else:
    #     # 处理分割失败的情况
    #     print(r)
    #     # print("无法找到指定位置的元素")

    # print(mailno,jgdm)
    # cursor.execute("update TB_FJ_YJXXB set sd_xbsj_y =sysdate,sd_bkjgdm = '"+str(jgdm)+"' where yjtm='"+str(mailno)+"'")


# cursor2.execute("insert into TB_TEMP  (yjhm) values('"+str(mailno)+"') ")
# con.commit()
#  con2.commit()

def execThread(func, args, pool_size=20):
    pool = ThreadPool(pool_size)
    if len(args) == 1 or len(args[0]) == 1:
        # res = pool.map(func, args)
        res = func(*args[0])  # 直接调用函数，传递参数列表中的参数
    else:
        fun = lambda x: func(*x)
        res = pool.map(fun, args)
    pool.close()
    pool.join()
    return res


def run_forever():
    try:

        global username, password, session, jdptid, con, cursor, con2, cursor2, k, flag

        i = 1

        flag = True
        while flag:

            con = cx.connect('c##pdafz', 'Gzyqzxjpda2019.', '10.194.69.27:1521/pdafz')  # 创建数据库连接
            # con2 = cx.connect('c##pdafz', 'Gzyqzxjpda2019.', '10.194.69.27:1521/pdafz')  #创建数据库连接
            cursor = con.cursor()  # 创建游标
            # cursor2 = con2.cursor()       #创建游标
            start = time.perf_counter()


            cnt = cursor.execute(
                "SELECT id, yjtm, '1' as sjrdz from (SELECT t.id, t.yjtm, t.sjrdz FROM TB_FJ_YJXXB t WHERE t.yjtm in ('9875215713023','1360643342501'))")

            list = cnt.fetchall()
            # print(list[0][0])
            j = 0
            data = []

            for d in range(0, len(list)):
                # print ('第'+str(i)+'件')
                t = (list[d][0], list[d][1], list[d][2].replace("\\\\N", "").replace("\\R", ""))
                # print(t)
                data.append(t)
                # getmailmg(jdptid,cursor,mailno)
                # time.sleep(0.1)
                j += 1
            # print(data)
            if len(data) > 0:
                ##清空
                # os.system('cls')
                # 构造Session
                # print(data)
                session = requests.Session()
                # 是否启用代理

                session.proxies = {'http': "http://10.194.69.22:9999",
                                   'https': "http://10.194.69.22:9999"}

                # pool=Pool(10)
                # pool.map(getmailmg,datalist)
                # try:
                execThread(getmailmg, data)
                con.commit()
                #  con2.commit()
                cursor.close()
                # cursor2.close()
                print("修编完毕,今次处理共" + str(j) + "件")
                # except:
                # run_forever()
                end = time.perf_counter()
                runTime = end - start
                # 计算时分秒
                hour = runTime // 3600
                minute = (runTime - 3600 * hour) // 60
                second = runTime - 3600 * hour - 60 * minute
                # 输出
                print(f'运行时间：{hour}小时{minute}分钟{second}秒')
                print("第" + str(i) + "次")
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print('当前时间：' + now)
                i += 1
                # del list,data
            else:

                print('无邮件，暂停十分钟')
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print('当前时间：' + now)
                # now=datetime.now().strftime("%Y-%m-%d %H:%M")
                # print(now)
                time.sleep(600)
    except Exception as e:
        # k += 1
        # # 弹出错误提示窗口
        # # print("连接数据库失败", f"连接数据库时发生错误：{str(e)}")
        # print('出错第' + str(k) + '次')
        traceback.print_exc()

        run_forever()


global k, flag
k = 0
run_forever()