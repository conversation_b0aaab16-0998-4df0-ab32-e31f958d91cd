<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=us-ascii" />
<meta http-equiv="Content-Script-Type" content="text/javascript" />
<meta name="generator" content="Oracle DARB XHTML Converter (Mode = browser help) - Version 5.1.2 Build 708" />
<title>Description of the illustration odbcmodel.gif</title>
</head>
<body>
<p>This graphic shows an ODBC application accessing three separate databases. An ODBC application makes a call to the Driver Manager through the ODBC API. The Driver Manager can be either the Microsoft Driver Manager or the unixODBC Driver Manager. Still using the ODBC API, the Driver Manager makes a call to the ODBC Driver. The ODBC Driver accesses the database over a network communications link using the database API.</p>
<div class="footer"><img width="144" height="18" src="../../../dcommon/gifs/oracle.gif" alt="Oracle" /><br />
Copyright&nbsp;&copy;&nbsp;,&nbsp;Oracle&nbsp;and/or&nbsp;its&nbsp;affiliates.&nbsp;All&nbsp;rights&nbsp;reserved.<br />
<a href="../../../dcommon/html/cpyr.htm">Legal Notices</a></div>
<!-- class="footer" -->
</body>
</html>
