import concurrent
import csv
import io
import multiprocessing
import os
import queue
import random
import socket
import sys
import tkinter as tk
import traceback
from functools import reduce, partial
from tkinter import scrolledtext, filedialog
from tkinter import ttk
from tkinter import *
from urllib.parse import quote
from os import path
import openpyxl
import requests,json, time, re
import datetime
import pandas
from multiprocessing.dummy import Pool
import threading
from pynput.mouse import Listener as MouseListener
from pynput.mouse import Controller as MouseController

from tool import Tool


# proxies = {'http': "http://************:9999",
# 'https': "http://************:9999"}

# testdata_dir_config = '--tessdata-dir "D:\\Tesseract-OCR\\tessdata"'

# 以下为定义一个字典，以及需要获取的字段名称
def getzdmailtrace(parent,mailno):
    global L
    # print ('正在处理:'+mailno)

    requests.packages.urllib3.disable_warnings()
    url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noHidden'
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',

        'Connection': 'keep-alive',
        'Content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Host': '**********',
        'Referer': 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList',
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36',
        'Cookie': 'jdpt.session.id=' + jdptid + ',CORAL_GRAYLEVEL=0'
    }
    data = {

        'trace_no': mailno,
        'numType': 15,
        'limit': 20

    }
    # response = requests.post(url, headers=headers, verify=False,proxies=proxies)
    response = session.post(url, headers=headers, params=data, verify=False)
    r = response.text

    jsonObj = json.loads(r)
    dataOutput= {
        "邮件号": [],
        "产品": [],
        "产品代码": [],
        "基础产品": [],
        "基础产品代码": [],
        "解车交接时间": [],
        "解车交接对象": [],
        "封车交接时间": [],
        "封车交接对象": [],
        "车牌/航班号": []
    }
    hbh=""
    fcjjdx=""
    fcjjsj=""
    jcjjsj=""
    jcjjdx=""
    jccp=""
    jccpdm=""
    cp=""
    cpdm=""
    # 通过循环来获取JSON中的数据，并添加到字典中
    for line in jsonObj:
        if '收寄计费信息' in line["opName"]:
            jccp = line.get('baseProductName','')
            jccpdm = line.get('baseProductNo','')
            cp = line.get('bizProductName','')
            cpdm = line.get('bizProductNo','')

        if '处理中心解车' in line["opName"] and '51000061' in line["opOrgCode"]and jcjjdx is '':

            jcjjsj=line["opTime"]
            jcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
            if jcjjdx:
                jcjjdx = jcjjdx[0]

            else:
                jcjjdx = ''
        if '航班起飞' in line["opName"] and '广州航空中心' in line["opOrgSimpleName"]:
            fcjjsj = line["opTime"]
            hbh = re.findall(r'航班号:(\w+)', str(line["originalDesc"]))
            if hbh:
                hbh = hbh[0]
            else:
                hbh = ''
            fcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
            if fcjjdx:
                fcjjdx = fcjjdx[0]
            else:
                fcjjdx = ''

        if '处理中心封车' in line["opName"] and '51000061' in line["opOrgCode"]:
            fcjjsj=line["opTime"]
            hbh = re.findall(r'车牌:(\w+)', str(line["originalDesc"]))
            if hbh:
                hbh = hbh[0]
            else:
                hbh = ''
            fcjjdx = re.findall(r'交接对象:(.*?)(?=\s\d+)', str(line["originalDesc"]))
            if fcjjdx:
                fcjjdx = fcjjdx[0]
            else:
                fcjjdx = ''

        # print("正在处理第 "+str(indexNUm) + "行")    #打印正在执行的行数
    dataOutput["邮件号"].append(mailno)
    dataOutput["基础产品"].append(jccp)
    dataOutput["基础产品代码"].append(jccpdm)
    dataOutput["产品"].append(cp)
    dataOutput["产品代码"].append(cpdm)
    dataOutput["解车交接时间"].append(jcjjsj)
    dataOutput["解车交接对象"].append(jcjjdx)
    dataOutput["封车交接时间"].append(fcjjsj)
    dataOutput["车牌/航班号"].append(hbh)
    dataOutput["封车交接对象"].append(fcjjdx)
    L += 1
    #print("正在处理第 "+str(L) + "件")
    if L % 1000 == 0:  # 每处理1000件时
        parent.after(0, tool.process_input('已爬' + str(L) + '件'))  # 立即调用UI更新方法
    return dataOutput


 # 合并字典数据
def merge_dicts(dict1, dict2):
    for key in dict1:
        dict1[key].extend(dict2[key])
    return dict1



def run(title,parent):
    try:
        global username, password, session, jdptid, L,merged_data



        # 构造Session
        session = requests.Session()

        # 获取本机主机名
        hostname = socket.gethostname()

        # 获取本机 IP 地址
        ip_address = socket.gethostbyname(hostname)

        print("本机主机名:", hostname)
        print("本机 IP 地址:", ip_address)
        if '************' == ip_address:
            session.proxies = {'http': "http://************:9999",
                               'https': "http://************:9999"}

            tool.process_input("代理功能已启用")
        L = 1
        # 程序已经在运行中，禁用按钮
        submit_button.configure(state="disabled")
        back_button.configure(state="disabled")
        # 保存账号和密码
        tool.save_data()
        username = account_entry.get()  # linecache.getline(r'user.txt',1).strip('\n')  # 修改要获取的行数
        password = password_entry.get()  # linecache.getline(r'user.txt',2).strip('\n')  # 修改要获取的行数
        #password = jiami()
        password=tool.aes_encrypt(password,'B+oQ52IuAt9wbMxw')

        # 提交使用记录
        #tool.postlog(username, title, ip_address)
        start = time.perf_counter()

        # 获取文本框中的文本
        text = input_textbox.get("1.0", "end-1c")

        # 将文本按行分割并去除空行
        lines = text.splitlines()
        lines = [line for line in lines if line.strip()]
        datalist = lines

        # 检查 datalist 是否为空
        if not datalist:
            # 打开文件夹并选择 Excel 文件或 CSV 文件
            file_path = filedialog.askopenfilename(filetypes=[("CSV Files", "*.csv"),("Excel Files", "*.xlsx;*.xls") ])
            if file_path:
                if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
                    # 读取 Excel 文件
                    wb = openpyxl.load_workbook(file_path)
                    sheet = wb.active

                    # 从第一行开始逐行搜索第三列值为 "广州广航" 的对应第一列的值
                    for row in sheet.iter_rows(values_only=True):
                        if row[2] == "广州广航":
                            datalist.append(row[0])
                        elif not row[0]:  # 如果第一列为空，表示到达表格末尾，停止搜索
                            break
                elif file_path.endswith('.csv'):
                    # 读取 CSV 文件
                    with open(file_path, newline='', encoding='utf-8') as csvfile:
                        reader = csv.reader(csvfile)
                        for row in reader:
                            if len(row) >= 3 and row[2] == "广州广航":
                                datalist.append(row[0])
                            elif not row[0]:  # 如果第一列为空，表示到达文件末尾，停止搜索
                                break
                # 将 datalist 中的数据显示在 input_textbox 中
                input_textbox.delete(1.0, "end")  # 清空文本框
                for item in datalist:
                    input_textbox.insert("end", item + "\n")  # 插入数据并换行
                tool.process_input('数据解析成功')
        i = 2
        # print ('开始登录新一代')
        # print ('第1次尝试登录')
        # 删除excel文件
        #os.remove('data.xlsx')
        tool.process_input('开始登录新一代')
        tool.process_input('第1次尝试登录')
        #url = 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/queryTraceByTrace_noNew'
        url= 'https://**********/querypush-web/a/qps/qpswaybilltraceinternal/traceList'
        result = tool.getck(username, password, session, url)
        jdptid = result[0]
        userName = result[1]

        while (jdptid == '0'):
            # print ('第'+str(i)+'次尝试登录')
            tool.process_input('第' + str(i) + '次尝试登录')
            result = tool.getck(username, password, session, url)
            jdptid = result[0]
            userName = result[1]
            i += 1
            if i > 5:
                tool.process_input('超5次登录失败,请检查账号密码正确后重试')
                break
        tool.postlog(username, userName, title, ip_address)
        tool.process_input('当前线程:'+threads_combobox.get())
        pool = Pool(int(threads_combobox.get()))
        #pool = multiprocessing.Pool(processes=int(threads_combobox.get()))
        #pool.map(getmailtrace, datalist)
        # 并发执行并获取结果

        merged_data = {
            "邮件号": [],
            "产品": [],
            "产品代码": [],
            "基础产品": [],
            "基础产品代码": [],
            "解车交接时间": [],
            "解车交接对象": [],
            "封车交接时间": [],
            "封车交接对象": [],
            "车牌/航班号": []
        }
        #results = pool.map(getallmailtrace, datalist)
        # 创建一个偏函数，其中root参数预先设定
        getzdmailtrace_bound = partial(getzdmailtrace, parent)
        results = pool.map(getzdmailtrace_bound,datalist)

        merged_data = reduce(merge_dicts, results, merged_data)

        tool.process_input('爬取完毕')
        tool.process_input('正在写入Excel')
        # 定义当前时间
        currentTime = datetime.datetime.now()
        dataForm = pandas.DataFrame(merged_data)
        row = 1048570
        length = len(dataForm)
        number = length // row
        for i in range(number + 1):
            dataForm[i * row:(i + 1) * row].to_excel("邮件解车交接对象"+"-"+
                                                     currentTime.strftime("%Y%m%d%H%M%S") + str(i) + "-bylhx.xlsx",
                                                     index=False)


        tool.process_input("写入完成共" + str(number + 1) + "个文件")
        # file = open("待爬邮件.txt", 'w').close()

        end = time.perf_counter()
        runTime = end - start
        # 计算时分秒
        hour = runTime // 3600
        minute = (runTime - 3600 * hour) // 60
        second = runTime - 3600 * hour - 60 * minute
        # 输出
        # print (f'运行时间：{hour}小时{minute}分钟{second}秒')
        tool.process_input(f'运行时间：{hour}小时{minute}分钟{second}秒')
        del merged_data
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")

    except Exception as e:
        # 程序未在运行中，启用按钮
        submit_button.configure(state="normal")
        back_button.configure(state="normal")
        buffer = io.StringIO()
        traceback.print_exc(file=buffer)
        error_message = buffer.getvalue()
        tool.process_input(error_message)
        # 删除excel文件
        #os.remove('data.xlsx')


def handle_input(title,parent):
    # 创建一个新线程来执行耗时的处理操作
    t = threading.Thread(target=run, args=(title, parent))
    t.start()

def clear_text():
    # 删除文本框中的所有文本
    input_textbox.delete("1.0", "end")



last_move_time = time.time()

def on_move(x, y):
    global last_move_time
    last_move_time = time.time()
    #print(last_move_time)
def on_click(x, y, button, pressed):
    global last_move_time
    last_move_time = time.time()
    #print(last_move_time)
def on_scroll(x, y, dx, dy):
    global last_move_time
    last_move_time = time.time()
    #print(last_move_time)

stop_event = threading.Event()
def simulate_mouse_movement():
    m = MouseController()
    last_move_time = time.time()
    while not stop_event.is_set():
        # 这里添加了time.sleep(1)，让线程每隔一秒检查一次，减少CPU占用
        time.sleep(1)
        if time.time() - last_move_time > 60:
            x, y = m.position
            # 微小地随机移动鼠标
            new_x = x + random.choice([-1, 0, 1])
            new_y = y + random.choice([-1, 0, 1])
            m.move(new_x, new_y)
            print("鼠标移动到新位置：", new_x, new_y)
            last_move_time = time.time()


def get_resource_path(relative_path):
    """ 获取资源文件的路径 """
    base_path = getattr(sys, '_MEIPASS', path.dirname(path.abspath(__file__)))
    return path.join(base_path, relative_path)
def open_func_window(parent,title):
    # 隐藏主窗口
    parent.withdraw()

    func_window = tk.Toplevel(parent)
    # 设置图标

    icon_path = get_resource_path('chinapost.ico')#'chinapost.ico'
    func_window.iconbitmap(icon_path)
    # 设置新的标题
    func_window.title(title+" Power by LHX ")
    # 设置功能1界面窗口的大小和位置与主界面窗口一样
    x = parent.winfo_x()
    y = parent.winfo_y()
    width = parent.winfo_width()
    height = parent.winfo_height()
    func_window.geometry(f"{width}x{height}+{x}+{y}")
    global username, password, session, jdptid, L,  input_textbox, output_textbox, selected, selected2,submit_button,\
        button_clear, start_date_entry, end_date_entry, account_entry, password_entry,threads_combobox,merged_data,tool,back_button,organization_combobox,input2_textbox

    # 构造Session
    session = requests.Session()

    # 创建子容器
    frame = tk.Frame(func_window)
    frame.pack(padx=0, pady=0)

    # 创建子容器
    account_container = ttk.Frame(frame)
    account_container.grid(row=0, column=0)
    # 创建下拉框
    organization_label = Label(account_container, text="机构:")
    organization_label.grid(row=0, column=0, padx=5)
    organization_combobox = ttk.Combobox(account_container, values=["报关行", "国际", "国内"])
    #organization_combobox.set("报关行")  # 默认选择第一个机构

    organization_combobox.grid(row=0, column=1, padx=10, pady=10)

    # 创建账号输入框
    account_label = Label(account_container, text="新一代账号:")
    account_label.grid(row=0, column=2, padx=5)

    account_entry = Entry(account_container)
    account_entry.grid(row=0, column=3, padx=5)

    # 创建密码输入框
    password_label = Label(account_container, text="密码:")
    password_label.grid(row=0, column=4, padx=5)

    password_entry = Entry(account_container, show="*")
    password_entry.grid(row=0, column=5, padx=5)

    thread_label = tk.Label(account_container, text="线程选择:")
    thread_label.grid(row=0, column=6, padx=5)

    # 创建Combobox组件
    threads_combobox = ttk.Combobox(account_container, values=["5", "10", "15", "20"])
    threads_combobox.grid(row=0, column=7, padx=5)
    threads_combobox.current(0)  # 设置默认选择的值为第一个选项



    # 创建子容器
    input_label_container = ttk.Frame(frame)
    input_label_container.grid(row=1, column=0)

    # # 创建单选按钮
    # selected = tk.StringVar(value='全程轨迹')
    # radio_label = ttk.Label(input_label_container, text="功能选择:")
    # radio_label.grid(row=0, column=0)
    # radio_button1 = ttk.Radiobutton(input_label_container, text="全程轨迹", value="全程轨迹", variable=selected)
    # radio_button1.grid(row=0, column=1)
    #
    # radio_button2 = ttk.Radiobutton(input_label_container, text="最后轨迹", value="最后轨迹", variable=selected)
    # radio_button2.grid(row=0, column=2, padx=15)
    #
    # radio_button3 = ttk.Radiobutton(input_label_container, text="指定轨迹", value="指定轨迹", variable=selected)
    # radio_button3.grid(row=0, column=3)

    # # 创建单选按钮
    # selected2 = tk.StringVar(value='否')
    # radio2_label = ttk.Label(input_label_container, text="是否只筛广航轨迹:")
    # radio2_label.grid(row=1, column=0, padx=10, pady=10)
    # radio2_button1 = ttk.Radiobutton(input_label_container, text="是", value='是', variable=selected2)
    # radio2_button1.grid(row=1, column=1, padx=5, pady=10)
    #
    # radio2_button2 = ttk.Radiobutton(input_label_container, text="否", value='否', variable=selected2)
    # radio2_button2.grid(row=1, column=2, padx=5, pady=10)
    #
    #
    # # 添加指定处理动作
    # input2_label = ttk.Label(input_label_container, text="筛处理动作(多个处理动作用#分隔):")
    # input2_label.grid(row=2, column=0, padx=10, pady=10)
    # # input_label.pack()
    # input2_textbox = tk.Entry(input_label_container, width=30)
    # input2_textbox.grid(row=2, column=1, padx=10, pady=10, columnspan=1)
    # 添加多行输入框
    input_label = ttk.Label(input_label_container, text="邮件号:")
    input_label.grid(row=3, column=0, padx=10, pady=10)
    # input_label.pack()
    input_textbox = tk.Text(input_label_container, height=5, width=30)
    input_textbox.grid(row=3, column=1, padx=10, pady=10, columnspan=1)
    # input_textbox.pack()

    # 添加运行按钮
    submit_button = ttk.Button(input_label_container, text="开始运行", command=lambda: handle_input(title, func_window))
    submit_button.grid(row=4, column=0, padx=10, pady=10)
    # submit_button.pack()

    # 创建清空文本按钮
    button_clear = ttk.Button(input_label_container, text="清空文本", command=clear_text)
    button_clear.grid(row=4, column=1, padx=10, pady=10)
    # button_clear.pack()

    # 创建子容器
    output_container = ttk.Frame(frame)
    output_container.grid(row=5, column=0, columnspan=5)

    # 添加滚动文本框
    output_label = ttk.Label(output_container, text="运行情况:")
    output_label.grid(row=0, column=0, padx=10, pady=10)

    output_textbox = scrolledtext.ScrolledText(output_container, height=10, width=50)
    output_textbox.configure(state="disabled")  # 设置文本框为只读状态
    output_textbox.grid(row=0, column=1, padx=10, pady=10)

    # 创建工具类实例
    tool = Tool(output_textbox, account_entry, password_entry, func_window, organization_combobox)
    # 读取上次保存的账号和密码
    tool.read_data()

    func_window.protocol("WM_DELETE_WINDOW", tool.quit)  # 添加此行代码以侦听窗口关闭事件
    back_button = ttk.Button(func_window, text="返回主菜单",
                             command=lambda: [quit()])
    back_button.pack()


    # 监听鼠标移动事件
    mouse_listener = MouseListener(on_move=on_move, on_click=on_click, on_scroll=on_scroll)
    mouse_listener.start()

    # 在单独的线程中运行模拟鼠标移动的函数
    mouse_simulation_thread = threading.Thread(target=simulate_mouse_movement)
    mouse_simulation_thread.start()

    def quit():
        stop_event.set()  # 设置事件，通知线程停止
        mouse_listener.stop()
        func_window.destroy()
        if mouse_simulation_thread.is_alive():
            mouse_simulation_thread.join()
        parent.deiconify()
    # # 当Tkinter应用程序结束时，停止监听器
    # mouse_listener.stop()